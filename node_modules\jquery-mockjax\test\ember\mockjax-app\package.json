{"name": "mockjax-app", "version": "0.0.0", "description": "Small description for mockjax-app goes here", "license": "MIT", "author": "", "directories": {"doc": "doc", "test": "tests"}, "repository": "", "scripts": {"build": "ember build", "start": "ember server", "test": "ember test"}, "devDependencies": {"broccoli-asset-rev": "^2.4.5", "ember-ajax": "^2.4.1", "ember-cli": "2.10.0", "ember-cli-app-version": "^2.0.0", "ember-cli-babel": "^5.1.7", "ember-cli-dependency-checker": "^1.3.0", "ember-cli-htmlbars": "^1.0.10", "ember-cli-htmlbars-inline-precompile": "^0.3.3", "ember-cli-inject-live-reload": "^1.4.1", "ember-cli-jshint": "^2.0.1", "ember-cli-qunit": "^3.0.1", "ember-cli-release": "^0.2.9", "ember-cli-sri": "^2.1.0", "ember-cli-test-loader": "^1.1.0", "ember-cli-uglify": "^1.2.0", "ember-data": "^2.10.0", "ember-export-application-global": "^1.0.5", "ember-load-initializers": "^0.5.1", "ember-resolver": "^2.0.3", "ember-welcome-page": "^1.0.3", "loader.js": "^4.0.10", "phantomjs-prebuilt": "^2.1.14"}, "engines": {"node": ">= 0.12.0"}, "private": true}