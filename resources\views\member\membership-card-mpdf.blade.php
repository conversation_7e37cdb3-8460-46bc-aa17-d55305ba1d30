<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>सदस्यता कार्ड - {{ $member->name }}</title>
</head>
<body>
    <div class="watermark hindi-text">छत्तीसगढ़ यादव समाज</div>
    
    <!-- Official Letterhead -->
    <div class="letterhead">
        <div class="letterhead-content">
            <div class="logo-section">
                <div class="logo">
                    छ.ग. यादव<br>
                    शासकीय सेवक समिति<br>
                    <small>YSSS 36</small>
                </div>
            </div>
            <div class="header-text">
                <div class="organization-name hindi-text">छत्तीसगढ़ यादव शासकीय सेवक समिति</div>
                <div class="sub-details hindi-text">पं. क्रमांक. {{ $member->membership_number }} दिनांक {{ $member->created_at->format('d.m.Y') }}</div>
                <div class="address hindi-text">पता : A-26, गायत्री नगर, रायपुर</div>
            </div>
        </div>
    </div>

    <!-- Form Content -->
    <div class="form-content">
        <div class="form-header">
            <div class="form-left">
                <div style="margin-bottom: 10px;" class="hindi-text">
                    <strong>प्रति,</strong><br>
                    <span style="margin-left: 20px;">अध्यक्ष / सचिव</span><br>
                    <span style="margin-left: 20px;">छ.ग यादव शासकीय सेवक समिति</span><br>
                    <span style="margin-left: 20px;">गायत्री नगर रायपुर</span>
                </div>
                
                <div class="subject-line hindi-text">
                    <strong>विषय:- छ.ग. यादव शासकीय सेवक समिति की सदस्यता ग्रहण करने बाबत्।</strong>
                </div>
            </div>
            
            <div class="form-right">
                <div style="border: 1px solid #333; padding: 5px; margin-bottom: 10px; font-size: 9px; text-align: center;" class="hindi-text">
                    <div>सदस्यता संख्या {{ substr($member->membership_number, -7) }} का कार्ड</div>
                    <div>आजीवन सदस्य 5,000 रुपए कार्ड</div>
                    <div>साधारण सदस्य 1200 वार्षिक</div>
                </div>
                
                <div class="photo-section">
                    @if($member->photo && file_exists(public_path('storage/' . $member->photo)))
                        <img src="{{ public_path('storage/' . $member->photo) }}" style="width: 100%; height: 100%; object-fit: cover;" alt="Member Photo">
                    @else
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 12px; color: #666;" class="hindi-text">
                            Passport photo<br>size
                        </div>
                    @endif
                </div>
                
                <div class="qr-section">
                    <div class="qr-code">QR Code</div>
                    <div style="font-size: 8px;" class="hindi-text">सदस्यता सत्यापन हेतु क्यू आर कोड</div>
                </div>
            </div>
        </div>

        <div class="content-text hindi-text">
            <strong>महोदय,</strong><br>
            निवेदन है कि मैं छत्तीसगढ़ / केन्द्र शासन के कार्मिक हूं मैं समिति की गतिविधियों एवं कार्य योजना से परिचित हूं। (सदस्यक/आजीवन / साधारण सदस्य बनकर यादव समाज की सेवा करना चाहता हूं।
        </div>

        <div style="margin-bottom: 15px;" class="hindi-text">
            <strong>मेरी संपूर्ण जानकारी निम्न है:-</strong>
        </div>

        <div class="form-fields">
            <div class="field-row">
                <div class="field-number">1.</div>
                <div class="field-label hindi-text">नाम/पिता का नाम</div>
                <div class="field-value hindi-text">{{ $member->name }} / {{ $member->fathers_husband_name }}</div>
            </div>
            
            <div class="field-row">
                <div class="field-number">2.</div>
                <div class="field-label hindi-text">पद/पदेश स्थान</div>
                <div class="field-value hindi-text">{{ $member->department_name ?? 'N/A' }}</div>
            </div>
            
            <div class="field-row">
                <div class="field-number">3.</div>
                <div class="field-label hindi-text">जन्म तिथि</div>
                <div class="field-value">{{ $member->birth_date ? $member->birth_date->format('d/m/Y') : 'N/A' }}</div>
            </div>
            
            <div class="field-row">
                <div class="field-number">4.</div>
                <div class="field-label hindi-text">मोबाइल / वैवाहिक स्थिति</div>
                <div class="field-value">{{ $member->mobile_number }} / {{ $member->marital_status ?? 'N/A' }}</div>
            </div>
            
            <div class="field-row">
                <div class="field-number">5.</div>
                <div class="field-label hindi-text">संस्थान/कार्यालय A पुत्र/पुत्री का नाम एवं जन्म तिथि</div>
                <div class="field-value">{{ $member->office ?? 'N/A' }}</div>
            </div>
            
            <div class="field-row" style="border-bottom: none;">
                <div class="field-number"></div>
                <div class="field-label hindi-text">B पुत्र/पुत्री का नाम एवं जन्म तिथि</div>
                <div class="field-value">
                    @if($member->children && count($member->children) > 0)
                        @foreach($member->children as $index => $child)
                            {{ $child['name'] ?? 'N/A' }} ({{ $child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') : 'N/A' }})
                            @if(!$loop->last), @endif
                        @endforeach
                    @else
                        N/A
                    @endif
                </div>
            </div>
            
            <div class="field-row">
                <div class="field-number">6.</div>
                <div class="field-label hindi-text">सदस्य का संपूर्ण पता जी.</div>
                <div class="field-value hindi-text">{{ $member->address ?? 'N/A' }}</div>
            </div>
            
            <div class="field-row">
                <div class="field-number">7.</div>
                <div class="field-label hindi-text">सदस्य बनने का आधार</div>
                <div class="field-value hindi-text">{{ $member->caste_details ?? 'यादव समुदाय' }}</div>
            </div>
        </div>

        <!-- Declaration Section -->
        <div class="declaration-section">
            <div class="declaration-title hindi-text">घोषणा पत्र</div>
            <div class="declaration-text hindi-text">
                मैं घोषणा करता/करती हूं कि उपरोक्त जानकारी सत्य है। असत्य पाये जाने की स्थिति में मेरी सदस्यता समाप्त करने का अधिकार प्रबंधकारिणी समिति का होगा।
            </div>
        </div>

        <!-- Proposer Section -->
        <div class="proposer-section">
            <div class="proposer-title hindi-text">प्रस्तावक सदस्य</div>
            <div style="margin-bottom: 15px;" class="hindi-text">
                <strong>सदस्य का नाम:</strong> {{ $member->member_name_signature ?? 'N/A' }}<br>
                <strong>पदनाम:</strong> {{ $member->vibhag ?? 'N/A' }}<br>
                <strong>कार्यालय पता/जी. नं:</strong> {{ $member->proposer_address ?? 'N/A' }}
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature-footer">
            <div class="signature-left">
                <div style="margin-bottom: 10px;">
                    @if($member->signature && file_exists(public_path('storage/' . $member->signature)))
                        <img src="{{ public_path('storage/' . $member->signature) }}" style="width: 120px; height: 40px; object-fit: contain; border-bottom: 1px solid #333;" alt="Member Signature">
                    @else
                        <div class="signature-line"></div>
                    @endif
                </div>
                <div class="signature-label hindi-text">सदस्य हस्ताक्षर</div>
                <div style="margin-top: 20px;">
                    <div class="signature-line"></div>
                    <div class="signature-label hindi-text">अधिकारी का कार्यक्षेत्र:-</div>
                </div>
            </div>

            <div class="signature-right">
                <div class="signature-line"></div>
                <div class="signature-label hindi-text">दिनांक: {{ $member->created_at->format('d/m/Y') }}</div>
                <div style="margin-top: 20px;">
                    <div class="signature-line"></div>
                    <div class="signature-label hindi-text">हस्ताक्षर सचिव</div>
                </div>
            </div>
        </div>

        <!-- Office Use Section -->
        <div class="office-use">
            <div class="office-title hindi-text">हस्ताक्षर अध्यक्ष</div>
            <div style="height: 40px;"></div>
        </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 30px; text-align: center; font-size: 9px; color: #666; border-top: 1px solid #ddd; padding-top: 10px;" class="hindi-text">
        <p><strong>यह एक आधिकारिक दस्तावेज है। कृपया इसे सुरक्षित रखें।</strong></p>
        <p>छत्तीसगढ़ यादव शासकीय सेवक समिति | जेनरेट किया गया: {{ now()->format('d M Y, h:i A') }}</p>
        <p>सदस्यता संख्या: {{ $member->membership_number }} | स्थिति: {{ $member->getStatusDisplayText() }}</p>
    </div>
</body>
</html>
