@extends('layouts.app')

@section('title', 'एकादश सदस्यता सूची')

@section('content')
    <div class="bg-gradient-to-b to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">एकादश सदस्यता सूची</h1>
                <p class="text-gray-600 max-w-2xl mx-auto">स्वीकृत एकादश सदस्यों की सूची</p>
            </div>

            <!-- Search and Filter -->
            <div class="max-w-7xl mx-auto mb-6">
                <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                    <form method="GET" action="{{ route('ekadash-sadasyata.members') }}" class="space-y-4">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                                <input type="text" name="search" value="{{ request('search') }}"
                                       placeholder="नाम, मोबाइल, सदस्यता संख्या..."
                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                            </div>
                            <div>
                                <select name="division_master_id" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                                    <option value="">सभी संभाग</option>
                                    @foreach($divisionMasters as $division)
                                        <option value="{{ $division->id }}" {{ request('division_master_id') == $division->id ? 'selected' : '' }}>
                                            {{ $division->division_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <select name="district_master_id" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                                    <option value="">सभी जिले</option>
                                    @foreach($districtMasters as $district)
                                        <option value="{{ $district->id }}" {{ request('district_master_id') == $district->id ? 'selected' : '' }}>
                                            {{ $district->district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <select name="vikaskhand_master_id" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                                    <option value="">सभी विकासखंड</option>
                                    @foreach($vikaskhandMasters as $vikaskhand)
                                        <option value="{{ $vikaskhand->id }}" {{ request('vikaskhand_master_id') == $vikaskhand->id ? 'selected' : '' }}>
                                            {{ $vikaskhand->sub_district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-center gap-3">
                            <button type="submit" class="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-md text-sm font-medium">
                                खोजें
                            </button>
                            <a href="{{ route('ekadash-sadasyata.members') }}" class="px-4 py-2 bg-gray-400 hover:bg-gray-500 text-white rounded-md text-sm font-medium">
                                रीसेट
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="max-w-7xl mx-auto mb-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-teal-600">{{ $members->total() }}</div>
                            <div class="text-gray-600">कुल स्वीकृत सदस्य</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600">₹11</div>
                            <div class="text-gray-600">न्यूनतम सदस्यता शुल्क</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600">{{ $members->count() }}</div>
                            <div class="text-gray-600">इस पेज पर सदस्य</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Members Section -->
            <div class="max-w-7xl mx-auto">
                @if($members->count() > 0)
                    <!-- Desktop Table View -->
                    <div class="hidden lg:block mb-8">
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                            <div class="bg-gradient-to-r from-teal-600 to-teal-700 px-6 py-4">
                                <h2 class="text-xl font-bold text-white">एकादश सदस्यता सूची (तालिका दृश्य)</h2>
                                <p class="text-teal-100 text-sm">स्वीकृत सदस्यों की विस्तृत जानकारी</p>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                सदस्यता संख्या
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                सदस्य विवरण
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                संपर्क जानकारी
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                शैक्षणिक योग्यता
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                विभाग
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                स्थिति
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                सदस्यता दिनांक
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($members as $member)
                                            <tr class="hover:bg-gray-50 transition duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ $member->membership_number }}
                                                    </div>
                                                    <div class="text-xs text-gray-500">
                                                        ID: {{ $member->id }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        @if($member->photo)
                                                            <img class="h-10 w-10 rounded-full object-cover mr-3"
                                                                 src="{{ asset('storage/' . $member->photo) }}"
                                                                 alt="{{ $member->name }}">
                                                        @else
                                                            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                                                                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                                </svg>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <div class="text-sm font-medium text-gray-900">{{ $member->name }}</div>
                                                            <div class="text-sm text-gray-500">{{ $member->fathers_husband_name }}</div>
                                                            @if($member->birth_date)
                                                                <div class="text-xs text-gray-400">
                                                                    जन्म: {{ $member->birth_date->format('d/m/Y') }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900">{{ $member->mobile_number }}</div>
                                                    @if($member->vartaman_pata)
                                                        <div class="text-xs text-gray-500 max-w-xs truncate">
                                                            {{ Str::limit($member->vartaman_pata, 40) }}
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    @if($member->educationQualification)
                                                        <div class="text-sm text-gray-900">{{ $member->educationQualification->name }}</div>
                                                    @else
                                                        <div class="text-sm text-gray-400">-</div>
                                                    @endif
                                                    @if($member->course_stream_name)
                                                        <div class="text-xs text-gray-500">{{ $member->course_stream_name }}</div>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    @if($member->departmentMaster)
                                                        <div class="text-sm text-gray-900">{{ $member->departmentMaster->name }}</div>
                                                    @else
                                                        <div class="text-sm text-gray-400">-</div>
                                                    @endif
                                                    @if($member->vibhagiy_padnaam)
                                                        <div class="text-xs text-gray-500">{{ $member->vibhagiy_padnaam }}</div>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                                                            <circle cx="4" cy="4" r="3"/>
                                                        </svg>
                                                        स्वीकृत सदस्य
                                                    </span>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        शुल्क: ₹{{ number_format($member->fee, 0) }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $member->created_at->format('d/m/Y') }}
                                                    <div class="text-xs text-gray-400">
                                                        {{ $member->created_at->format('H:i') }}
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-700">
                                        कुल {{ $members->total() }} स्वीकृत सदस्य में से {{ $members->count() }} दिखाए गए हैं
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        पृष्ठ {{ $members->currentPage() }} / {{ $members->lastPage() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile/Tablet Card View -->
                    <div class="lg:hidden grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        @foreach($members as $member)
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                                <!-- Member Photo -->
                                <div class="bg-gradient-to-r from-teal-500 to-teal-600 p-6 text-center">
                                    @if($member->photo)
                                        <img src="{{ asset('storage/' . $member->photo) }}" 
                                             alt="{{ $member->name }}"
                                             class="w-24 h-24 rounded-full mx-auto border-4 border-white object-cover">
                                    @else
                                        <div class="w-24 h-24 rounded-full mx-auto border-4 border-white bg-white flex items-center justify-center">
                                            <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="mt-4 text-white">
                                        <div class="text-sm opacity-90">सदस्यता संख्या</div>
                                        <div class="font-bold">{{ $member->membership_number }}</div>
                                    </div>
                                </div>

                                <!-- Member Details -->
                                <div class="p-6">
                                    <h3 class="text-xl font-bold text-gray-800 mb-2">{{ $member->name }}</h3>
                                    
                                    <div class="space-y-2 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                            </svg>
                                            <span>{{ $member->fathers_husband_name }}</span>
                                        </div>

                                        @if($member->mobile_number)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                                </svg>
                                                <span>{{ $member->mobile_number }}</span>
                                            </div>
                                        @endif

                                        @if($member->educationQualification)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.75 2.524z"/>
                                                </svg>
                                                <span>{{ $member->educationQualification->name }}</span>
                                            </div>
                                        @endif

                                        @if($member->departmentMaster)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8 0a2 2 0 114 0 2 2 0 01-4 0z" clip-rule="evenodd"/>
                                                </svg>
                                                <span>{{ $member->departmentMaster->name }}</span>
                                            </div>
                                        @endif

                                        @if($member->address)
                                            <div class="flex items-start">
                                                <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                                </svg>
                                                <span class="text-xs">{{ Str::limit($member->address, 50) }}</span>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Member Status -->
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                स्वीकृत सदस्य
                                            </span>
                                            <span class="text-xs text-gray-500">
                                                {{ $member->created_at->format('d/m/Y') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-center">
                        {{ $members->links() }}
                    </div>
                @else
                    <!-- No Members Found -->
                    <div class="bg-white rounded-lg shadow-lg p-12 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">कोई सदस्य नहीं मिला</h3>
                        <p class="text-gray-500">अभी तक कोई एकादश सदस्यता स्वीकृत नहीं हुई है।</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
