<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('memberships', function (Blueprint $table) {
            // Make photo and signature nullable
            $table->string('photo')->nullable()->change();
            $table->string('signature')->nullable()->change();

            // Make fathers_husband_name nullable
            $table->string('fathers_husband_name')->nullable()->change();

            // Make birth_date required
            $table->date('birth_date')->nullable(false)->change();

            // Add new fields
            $table->date('vivah_tithi')->nullable()->after('marital_status');
            $table->string('course_stream_name')->nullable()->after('education');
            $table->string('vibhagiy_padnaam')->nullable()->after('course_stream_name');
            $table->string('karyalay_ka_pata')->nullable()->after('office');
            $table->text('vartaman_pata')->nullable()->after('karyalay_ka_pata');
            $table->text('isthayi_pata')->nullable()->after('vartaman_pata');

            // Add location master data foreign keys
            $table->unsignedBigInteger('division_master_id')->nullable()->after('yadav_varg_id');
            $table->unsignedBigInteger('district_master_id')->nullable()->after('division_master_id');
            $table->unsignedBigInteger('vikaskhand_master_id')->nullable()->after('district_master_id');

            // Add foreign key constraints
            $table->foreign('division_master_id')->references('id')->on('division_masters')->onDelete('set null');
            $table->foreign('district_master_id')->references('id')->on('district_masters')->onDelete('set null');
            $table->foreign('vikaskhand_master_id')->references('id')->on('vikaskhand_masters')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('memberships', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['division_master_id']);
            $table->dropForeign(['district_master_id']);
            $table->dropForeign(['vikaskhand_master_id']);

            // Drop new columns
            $table->dropColumn([
                'vivah_tithi',
                'course_stream_name',
                'vibhagiy_padnaam',
                'karyalay_ka_pata',
                'vartaman_pata',
                'isthayi_pata',
                'division_master_id',
                'district_master_id',
                'vikaskhand_master_id'
            ]);

            // Revert changes
            $table->string('photo')->nullable(false)->change();
            $table->string('signature')->nullable(false)->change();
            $table->string('fathers_husband_name')->nullable(false)->change();
            $table->date('birth_date')->nullable()->change();
        });
    }
};
