@extends('layouts.member')

@section('title', 'सदस्य डैशबोर्ड')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Mobile Header -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-navy-700 lg:hidden">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    @if($member->photo)
                        <img class="h-10 w-10 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                    @else
                        <div class="h-10 w-10 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                            <span class="text-sm font-medium text-teal-600 dark:text-teal-300">
                                {{ substr($member->name, 0, 1) }}
                            </span>
                        </div>
                    @endif
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $member->name }}</h1>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $member->membership_number }}</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <!-- Theme Toggle -->
                    <button
                        @click="darkMode = !darkMode"
                        class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500"
                        :class="darkMode ? 'bg-navy-100 text-navy-800 hover:bg-navy-200' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                        title="थीम बदलें"
                    >
                        <!-- Sun icon (visible when dark mode is on) -->
                        <svg x-show="darkMode" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <!-- Moon icon (visible when dark mode is off) -->
                        <svg x-show="!darkMode" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <span x-text="darkMode ? 'लाइट' : 'डार्क'" class="hidden sm:inline"></span>
                    </button>

                    <!-- Logout Button -->
                    <form method="POST" action="{{ route('member.logout') }}" class="inline">
                        @csrf
                        <button type="submit" class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-red-100 text-red-700 hover:bg-red-200 hover:text-red-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500" title="लॉगआउट">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            <span class="hidden sm:inline">लॉगआउट</span>
                        </button>
                    </form>

                    <!-- Mobile Menu Toggle -->
                    <button onclick="toggleMobileMenu()" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-navy-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobileMenu" class="hidden lg:hidden bg-white dark:bg-navy-800 border-b border-gray-200 dark:border-navy-700">
        <div class="px-4 py-2 space-y-1">
            <a href="{{ route('member.dashboard') }}" class="block px-3 py-2 rounded-md text-base font-medium text-teal-600 bg-teal-50 dark:bg-teal-900/30 dark:text-teal-300">
                डैशबोर्ड
            </a>
            <a href="{{ route('member.application-status') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700">
                आवेदन स्थिति
            </a>
            <a href="{{ route('member.profile') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700">
                प्रोफाइल
            </a>
            @if($member->isApproved())
                <a href="{{ route('member.preview-card') }}" target="_blank" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700">
                    सदस्यता कार्ड देखें
                </a>
                <a href="{{ route('member.download-card') }}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700">
                    PDF डाउनलोड करें
                </a>
            @endif

        </div>
    </div>

    <div class="flex">
        <!-- Desktop Sidebar -->
        <div class="hidden lg:flex lg:flex-shrink-0">
            <div class="flex flex-col w-64">
                <div class="flex flex-col flex-grow bg-white dark:bg-navy-800 border-r border-gray-200 dark:border-navy-700 pt-5 pb-4 overflow-y-auto">
                    <div class="flex items-center flex-shrink-0 px-4">
                        <div class="flex items-center justify-between w-full">
                            <div class="flex items-center space-x-3">
                                @if($member->photo)
                                    <img class="h-12 w-12 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                                @else
                                    <div class="h-12 w-12 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                                        <span class="text-lg font-medium text-teal-600 dark:text-teal-300">
                                            {{ substr($member->name, 0, 1) }}
                                        </span>
                                    </div>
                                @endif
                                <div>
                                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $member->name }}</h2>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $member->membership_number }}</p>
                                </div>
                            </div>

                            <!-- Desktop Controls -->
                            <div class="flex items-center space-x-2 flex-shrink-0">
                                <!-- Theme Toggle -->
                                <button
                                    @click="darkMode = !darkMode"
                                    class="p-2 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500"
                                    :class="darkMode ? 'text-navy-600 dark:text-navy-300 hover:bg-navy-100 dark:hover:bg-navy-700' : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-navy-700'"
                                    title="थीम बदलें"
                                >
                                    <!-- Sun icon (visible when dark mode is on) -->
                                    <svg x-show="darkMode" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                    <!-- Moon icon (visible when dark mode is off) -->
                                    <svg x-show="!darkMode" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                    </svg>
                                </button>

                                <!-- Logout Button -->
                                <form method="POST" action="{{ route('member.logout') }}">
                                    @csrf
                                    <button type="submit" class="p-2 rounded-md text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500" title="लॉगआउट">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <nav class="mt-8 flex-1 px-2 space-y-1">
                        <a href="{{ route('member.dashboard') }}" class="bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                            <svg class="text-teal-500 dark:text-teal-400 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            डैशबोर्ड
                        </a>
                        <a href="{{ route('member.application-status') }}" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                            <svg class="text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            आवेदन स्थिति
                        </a>
                        <a href="{{ route('member.profile') }}" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                            <svg class="text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            प्रोफाइल
                        </a>
                        @if($member->ancestors && count($member->ancestors) > 0)
                            <a href="{{ route('member.family-tree') }}" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                                <svg class="text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                पारिवारिक वंशावली
                            </a>
                        @endif
                        @if($member->isApproved())
                            <a href="{{ route('member.preview-card') }}" target="_blank" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                                <svg class="text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                सदस्यता कार्ड देखें
                            </a>
                            <a href="{{ route('member.download-card') }}" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-navy-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                                <svg class="text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                PDF डाउनलोड करें
                            </a>
                        @endif
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <div class="px-4 sm:px-6 lg:px-8 py-6">
                <!-- Welcome Section -->
                <div class="mb-8">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                        स्वागत है, {{ $member->name }}!
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        आपके सदस्यता डैशबोर्ड में आपका स्वागत है
                    </p>
                </div>

                <!-- Status Card -->
                <div class="bg-white dark:bg-navy-800 overflow-hidden shadow-sm rounded-lg mb-6">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-{{ $member->isApproved() ? 'green' : ($member->isRejected() ? 'red' : 'yellow') }}-100 dark:bg-{{ $member->isApproved() ? 'green' : ($member->isRejected() ? 'red' : 'yellow') }}-900/30 rounded-full flex items-center justify-center">
                                    @if($member->isApproved())
                                        <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    @elseif($member->isRejected())
                                        <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    @endif
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        सदस्यता स्थिति
                                    </dt>
                                    <dd class="flex items-baseline">
                                        <div class="text-2xl font-semibold text-gray-900 dark:text-white">
                                            {{ $member->getStatusDisplayText() }}
                                        </div>
                                        <div class="ml-2 flex items-baseline text-sm font-semibold">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $member->getStatusBadgeClass() }}">
                                                {{ ucfirst($member->membership_type) }}
                                            </span>
                                        </div>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                    <a href="{{ route('member.application-status') }}" class="bg-white dark:bg-navy-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-navy-700">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">आवेदन स्थिति</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">अपने आवेदन की जांच करें</p>
                            </div>
                        </div>
                    </a>

                    @if($member->isApproved())
                        <div class="bg-white dark:bg-navy-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-navy-700">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">सदस्यता कार्ड</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">कार्ड देखें और डाउनलोड करें</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{{ route('member.preview-card') }}" target="_blank"
                                       class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-navy-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                                        <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        देखें
                                    </a>
                                    <a href="{{ route('member.download-card') }}"
                                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                                        <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($member->ancestors && count($member->ancestors) > 0)
                        <a href="{{ route('member.family-tree') }}" class="bg-white dark:bg-navy-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-navy-700">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">पारिवारिक वंशावली</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">पूर्वजों का विवरण देखें</p>
                                </div>
                            </div>
                        </a>
                    @endif

                    <a href="{{ route('member.profile') }}" class="bg-white dark:bg-navy-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 dark:border-navy-700">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">प्रोफाइल</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">जानकारी अपडेट करें</p>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Member Information -->
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg mb-6">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            सदस्य जानकारी
                        </h3>
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्यता संख्या</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white font-mono">{{ $member->membership_number }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्यता प्रकार</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ ucfirst($member->membership_type) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">मोबाइल नंबर</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->mobile_number }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">आवेदन दिनांक</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->created_at->format('d M Y') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Family Tree Section -->
                @if($member->ancestors && count($member->ancestors) > 0)
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg mb-6">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                                पारिवारिक वंशावली
                            </h3>
                            <button onclick="toggleFamilyTree()" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-navy-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600">
                                <svg id="treeToggleIcon" class="mr-1 h-4 w-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                                <span id="treeToggleText">विस्तार करें</span>
                            </button>
                        </div>

                        <div id="familyTreeContainer" class="hidden">
                            <div class="family-tree-wrapper overflow-x-auto">
                                <div class="family-tree min-w-max">
                                    @php
                                        // Sort ancestors by generation level (highest to lowest)
                                        $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                                        $generationLabels = [
                                            7 => 'सातवीं पीढ़ी',
                                            6 => 'छठी पीढ़ी',
                                            5 => 'पांचवीं पीढ़ी',
                                            4 => 'चौथी पीढ़ी',
                                            3 => 'तीसरी पीढ़ी',
                                            2 => 'दूसरी पीढ़ी',
                                            1 => 'पहली पीढ़ी'
                                        ];
                                        $generationColors = [
                                            7 => 'bg-purple-100 border-purple-300 text-purple-800',
                                            6 => 'bg-indigo-100 border-indigo-300 text-indigo-800',
                                            5 => 'bg-blue-100 border-blue-300 text-blue-800',
                                            4 => 'bg-green-100 border-green-300 text-green-800',
                                            3 => 'bg-yellow-100 border-yellow-300 text-yellow-800',
                                            2 => 'bg-orange-100 border-orange-300 text-orange-800',
                                            1 => 'bg-red-100 border-red-300 text-red-800'
                                        ];
                                    @endphp

                                    @foreach($generationLabels as $level => $label)
                                        @php
                                            $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                                        @endphp
                                        @if($ancestorsInLevel->count() > 0)
                                            <div class="generation-level mb-8">
                                                <div class="text-center mb-4">
                                                    <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-200 {{ $generationColors[$level] }} inline-block px-4 py-2 rounded-full border-2">
                                                        {{ $label }}
                                                    </h4>
                                                </div>

                                                <div class="flex justify-center items-center space-x-6 flex-wrap">
                                                    @foreach($ancestorsInLevel as $ancestor)
                                                        <div class="ancestor-card {{ $generationColors[$level] }} p-4 rounded-lg border-2 shadow-sm max-w-xs">
                                                            <div class="text-center">
                                                                <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-inner">
                                                                    <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                                    </svg>
                                                                </div>
                                                                <h5 class="font-bold text-sm mb-2">{{ $ancestor['name'] ?? 'N/A' }}</h5>

                                                                <div class="text-xs space-y-1">
                                                                    @if(!empty($ancestor['birth_date']))
                                                                        <div><strong>जन्म:</strong> {{ $ancestor['birth_date'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['death_date']))
                                                                        <div><strong>मृत्यु:</strong> {{ $ancestor['death_date'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['birth_place']))
                                                                        <div><strong>जन्म स्थान:</strong> {{ $ancestor['birth_place'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['occupation']))
                                                                        <div><strong>व्यवसाय:</strong> {{ $ancestor['occupation'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['spouse']))
                                                                        <div><strong>पत्नी:</strong> {{ $ancestor['spouse'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['children_count']))
                                                                        <div><strong>संतान:</strong> {{ $ancestor['children_count'] }}</div>
                                                                    @endif
                                                                    @if(!empty($ancestor['gotra']))
                                                                        <div><strong>गोत्र:</strong> {{ $ancestor['gotra'] }}</div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>

                                                @if(!$loop->last)
                                                    <div class="flex justify-center mt-6">
                                                        <div class="w-px h-8 bg-gray-300 dark:bg-gray-600"></div>
                                                        <svg class="w-6 h-6 text-gray-400 -mt-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                                        </svg>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    @endforeach

                                    <!-- Current Member -->
                                    <div class="generation-level">
                                        <div class="text-center mb-4">
                                            <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-200 bg-teal-100 border-teal-300 text-teal-800 inline-block px-4 py-2 rounded-full border-2">
                                                वर्तमान सदस्य
                                            </h4>
                                        </div>

                                        <div class="flex justify-center">
                                            <div class="ancestor-card bg-teal-100 border-teal-300 text-teal-800 p-4 rounded-lg border-2 shadow-md max-w-xs">
                                                <div class="text-center">
                                                    <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-inner">
                                                        @if($member->photo)
                                                            <img class="w-14 h-14 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                                                        @else
                                                            <svg class="w-8 h-8 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                            </svg>
                                                        @endif
                                                    </div>
                                                    <h5 class="font-bold text-sm mb-2">{{ $member->name }}</h5>

                                                    <div class="text-xs space-y-1">
                                                        <div><strong>पिता:</strong> {{ $member->fathers_husband_name }}</div>
                                                        @if($member->birth_date)
                                                            <div><strong>जन्म:</strong> {{ $member->birth_date->format('d/m/Y') }}</div>
                                                        @endif
                                                        @if($member->address)
                                                            <div><strong>पता:</strong> {{ Str::limit($member->address, 30) }}</div>
                                                        @endif
                                                        @if($member->department_name)
                                                            <div><strong>विभाग:</strong> {{ $member->department_name }}</div>
                                                        @endif
                                                        <div><strong>सदस्यता:</strong> {{ $member->membership_number }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Children Section -->
                @if($member->children && count($member->children) > 0)
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            संतान विवरण
                        </h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($member->children as $child)
                                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-900 dark:text-white">{{ $child['name'] ?? 'N/A' }}</h4>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <span>{{ $child['gender'] === 'male' ? 'पुत्र' : 'पुत्री' }}</span>
                                                @if(!empty($child['dob']))
                                                    <span class="ml-2">• {{ \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
                <!-- Theme Preferences -->
                <div class="bg-white dark:bg-navy-800 overflow-hidden shadow rounded-lg mt-8">
                    <div class="p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            <svg class="inline h-5 w-5 mr-2 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            डैशबोर्ड सेटिंग्स
                        </h3>
                        <div class="space-y-4">
                            <!-- Theme Selection -->
                            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-navy-700 rounded-lg">
                                <div>
                                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">डिस्प्ले थीम</label>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">अपनी पसंदीदा डिस्प्ले थीम चुनें</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <!-- Light Theme Button -->
                                    <button
                                        @click="darkMode = false"
                                        :class="!darkMode ? 'bg-teal-500 text-white border-teal-500 shadow-md' : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'"
                                        class="px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 flex items-center space-x-2"
                                    >
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                        <span>लाइट मोड</span>
                                    </button>

                                    <!-- Dark Theme Button -->
                                    <button
                                        @click="darkMode = true"
                                        :class="darkMode ? 'bg-navy-600 text-white border-navy-600 shadow-md' : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'"
                                        class="px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 flex items-center space-x-2"
                                    >
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                                        </svg>
                                        <span>डार्क मोड</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Theme Info -->
                            <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <div class="flex items-start">
                                    <svg class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300">थीम प्राथमिकता</h4>
                                        <p class="text-sm text-blue-700 dark:text-blue-400 mt-1">
                                            आपकी थीम प्राथमिकता स्वचालित रूप से सहेजी जाती है। डिफ़ॉल्ट रूप से लाइट मोड सेट है।
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    function toggleMobileMenu() {
        const menu = document.getElementById('mobileMenu');
        menu.classList.toggle('hidden');
    }

    function toggleFamilyTree() {
        const container = document.getElementById('familyTreeContainer');
        const icon = document.getElementById('treeToggleIcon');
        const text = document.getElementById('treeToggleText');

        if (container.classList.contains('hidden')) {
            container.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
            text.textContent = 'छुपाएं';
        } else {
            container.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
            text.textContent = 'विस्तार करें';
        }
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const menu = document.getElementById('mobileMenu');
        const button = event.target.closest('button[onclick="toggleMobileMenu()"]');

        if (!menu.contains(event.target) && !button && !menu.classList.contains('hidden')) {
            menu.classList.add('hidden');
        }
    });
</script>

<style>
    .family-tree {
        font-family: 'Noto Sans Devanagari', sans-serif;
    }

    .ancestor-card {
        transition: all 0.3s ease;
        min-width: 200px;
    }

    .ancestor-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .generation-level {
        position: relative;
    }

    .family-tree-wrapper {
        padding: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    @media (max-width: 768px) {
        .ancestor-card {
            min-width: 180px;
            margin-bottom: 1rem;
        }

        .flex.justify-center.items-center.space-x-6 {
            flex-direction: column;
            space-x: 0;
            gap: 1rem;
        }
    }
</style>
@endsection
