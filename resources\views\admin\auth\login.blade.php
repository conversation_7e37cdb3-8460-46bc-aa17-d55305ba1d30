<!DOCTYPE html> <html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full bg-gray-50"> <head> <meta charset="utf-8"> <meta name="viewport" content="width=device-width, initial-scale=1"> <meta name="csrf-token" content="{{ csrf_token() }}"> <title>Admin Login</title> <!-- Fonts --> <link rel="preconnect" href="https://fonts.googleapis.com"> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin> <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Hind:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet"> <!-- Scripts --> @vite(['resources/css/app.css', 'resources/js/app.js']) <style> [x-cloak] { display: none !important; } </style> </head> <body class="h-full font-hindi antialiased"> <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50"> <div class="max-w-md w-full space-y-8"> <div> <div class="flex justify-center items-center space-x-2"> <div class="text-3xl font-bold text-navy-900"> <span class="text-saffron-500">Admin</span> Panel </div> </div> <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900"> Sign in to your account </h2> </div> @if(session('error')) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert"> <span class="block sm:inline">{{ session('error') }}</span> </div> @endif <form class="mt-8 space-y-6" action="{{ route('admin.login.submit') }}" method="POST"> @csrf <div class="rounded-md shadow-sm -space-y-px"> <div> <label for="email" class="sr-only">Email address</label> <input id="email" name="email" type="email" autocomplete="email" required value="{{ old('email') }}" class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-navy-500 focus:border-navy-500 focus:z-10 sm:text-sm" placeholder="Email address"> @error('email') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="password" class="sr-only">Password</label> <input id="password" name="password" type="password" autocomplete="current-password" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-navy-500 focus:border-navy-500 focus:z-10 sm:text-sm" placeholder="Password"> @error('password') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> </div> <div class="flex items-center justify-between"> <div class="flex items-center"> <input id="remember_me" name="remember" type="checkbox" class="h-4 w-4 text-navy-600 focus:ring-navy-500 border-gray-300 rounded"> <label for="remember_me" class="ml-2 block text-sm text-gray-900"> Remember me </label> </div> </div> <div> <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-navy-600 hover:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> <span class="absolute left-0 inset-y-0 flex items-center pl-3"> <svg class="h-5 w-5 text-navy-500 group-hover:text-navy-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"> <path fill-rule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clip-rule="evenodd" /> </svg> </span> Sign in </button> </div> </form> </div> </div> </body> </html> 