/* Family Tree Styles */

/* Chart container */
#family-tree-chart {
    width: 100%;
    height: 600px;
    position: relative;
    overflow: hidden;
    background-color: #f9fafb;
}

/* Dark mode support */
.dark #family-tree-chart {
    background-color: #0f172a;
}

/* Node styling */
.family-node {
    transition: all 0.2s ease;
    cursor: pointer;
}

.family-node:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Org<PERSON>hart overrides */
.orgchart {
    background-image: none !important;
    background-color: transparent !important;
}

.orgchart .lines {
    stroke: #94a3b8;
    stroke-width: 2px;
}

.dark .orgchart .lines {
    stroke: #475569;
}

/* Zoom controls */
.tree-control-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 4px;
    background: #3b82f6;
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s;
}

.tree-control-btn:hover {
    background: #2563eb;
}

.dark .tree-control-btn {
    background: #2563eb;
}

.dark .tree-control-btn:hover {
    background: #1d4ed8;
}

/* Generation legend */
#generation-legend {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-family: 'Noto Sans Devanagari', sans-serif;
    font-size: 12px;
    max-width: 200px;
    z-index: 100;
}

.dark #generation-legend {
    background: #1e293b;
    border-color: #334155;
    color: #e2e8f0;
}

/* Print styles */
@media print {
    #family-tree-chart {
        height: auto !important;
        overflow: visible !important;
    }
    
    .tree-control-btn,
    #generation-legend {
        display: none !important;
    }
    
    .orgchart {
        transform: scale(0.8) !important;
        transform-origin: top center !important;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #family-tree-chart {
        height: 500px;
    }
    
    .family-node {
        min-width: 150px !important;
        padding: 8px !important;
    }
}

@media (max-width: 480px) {
    #family-tree-chart {
        height: 400px;
    }
    
    .family-node {
        min-width: 120px !important;
        padding: 6px !important;
        font-size: 12px !important;
    }
}
