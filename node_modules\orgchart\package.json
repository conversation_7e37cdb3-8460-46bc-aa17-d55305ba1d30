{"name": "orgchart", "version": "5.0.0", "description": "Simple and direct organization chart(tree-like hierarchy) plugin based on pure DOM and jQuery.", "main": "./dist/js/jquery.orgchart.min.js", "style": ["./dist/css/jquery.orgchart.min.css"], "scripts": {"test": "gulp test", "unit-tests": "gulp unit-tests", "build": "gulp build", "start": "gulp serve", "cypress:open": "cypress open"}, "jest": {"preset": "jest-puppeteer"}, "repository": {"type": "git", "url": "https://github.com/dabeng/OrgChart.git"}, "keywords": ["j<PERSON>y", "plugin", "organization", "chart", "orgchart", "tree", "tree-like", "tree-view"], "author": "dabeng <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dabeng/OrgChart/issues"}, "homepage": "https://github.com/dabeng/OrgChart#readme", "dependencies": {"html2canvas": "^1.4.1", "jquery": "^3.6.2", "jquery-mockjax": "^2.6.0", "json-digger": "^2.0.2", "jspdf": "^2.5.1"}, "devDependencies": {"browser-sync": "^3.0.2", "chai": "^4.2.0", "cypress": "^13.2.0", "del": "^3.0.0", "gulp": "^5.0.0", "gulp-clean-css": "^3.10.0", "gulp-csslint": "^1.0.1", "gulp-eslint": "^4.0.2", "gulp-inject": "^4.3.2", "gulp-jest": "^4.0.3", "gulp-mocha": "^8.0.0", "gulp-rename": "^1.4.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "jest": "^29.3.1", "jest-cli": "^29.3.1", "jest-image-snapshot": "^6.1.0", "jest-puppeteer": "^4.3.0", "jsdom": "^16.5.0", "jsdom-global": "^3.0.2", "merge-stream": "^1.0.1", "mocha": "^10.2.0", "puppeteer": "^1.20.0", "sinon": "^4.5.0", "sinon-chai": "^2.14.0"}}