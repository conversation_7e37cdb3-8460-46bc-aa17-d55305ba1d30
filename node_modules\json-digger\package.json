{"name": "json-digger", "version": "2.0.2", "description": "", "main": "dist/json-digger.js", "module": "src/index.js", "scripts": {"test": "mocha", "build": "webpack"}, "repository": {"type": "git", "url": "git+https://github.com/dabeng/json-helper.git"}, "keywords": ["json", "loop"], "author": "dabeng", "license": "MIT", "bugs": {"url": "https://github.com/dabeng/json-helper/issues"}, "homepage": "https://github.com/dabeng/json-helper#readme", "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/register": "^7.18.9", "babel-loader": "^9.1.2", "chai": "^4.3.7", "mocha": "^10.2.0", "nodemon": "^1.18.11", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}}