{"name": "j<PERSON>y-mockjax", "title": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.6.1", "main": "./src/jquery.mockjax.js", "description": "The jQuery Mockjax Plugin provides a simple and extremely flexible interface for mocking or simulating ajax requests and responses.", "url": "https://github.com/jakerella/jquery-mockjax", "scripts": {"test": "grunt test:all && node browserstack.js"}, "keywords": ["ajax", "mock", "unit", "testing", "jquery-plugin", "ecosystem:jquery"], "author": {"name": "<PERSON>", "url": "http://jordankasper.com"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "url": "http://jdsharp.com"}], "homepage": "https://github.com/jakerella/jquery-mockjax", "repository": {"type": "git", "url": "https://github.com/jakerella/jquery-mockjax.git"}, "bugs": {"web": "http://github.com/jakerella/jquery-mockjax/issues"}, "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "devDependencies": {"browserify": "^17.0.0", "browserstack-runner": "^0.9.4", "grunt": "^1.6.1", "grunt-browserify": "^6.0.0", "grunt-contrib-concat": "^2.1.0", "grunt-contrib-connect": "^4.0.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-qunit": "^8.0.1", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-watch": "^1.1.0", "grunt-mocha-test": "^0.13.3", "http-server": "^14.1.1", "jquery": "^3.7.1", "jsdom": "^24.0.0", "load-grunt-tasks": "^5.1.0", "mocha": "^10.3.0", "puppeteer": "^22.1.0", "qunit": "^2.20.1", "sinon": "^17.0.1", "xmlhttprequest": "^1.8.0"}}