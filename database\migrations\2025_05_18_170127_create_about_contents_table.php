<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_contents', function (Blueprint $table) {
            $table->id();
            $table->string('page_key'); // To identify which page (uddeshya/pramukh/sanrachna)
            $table->string('section_key')->nullable(); // For multiple sections on the same page
            $table->string('title');
            $table->text('content');
            $table->string('image_path')->nullable(); // Optional image
            $table->integer('display_order')->default(0); // For ordering sections
            $table->boolean('is_published')->default(true);
            $table->timestamps();
            
            // Make section_key unique only when it's not null
            $table->index(['page_key', 'section_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_contents');
    }
};
