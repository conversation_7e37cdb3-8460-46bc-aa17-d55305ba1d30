@extends('layouts.admin') @section('title', 'Admin Dashboard') @php use Illuminate\Support\Facades\Storage; @endphp @section('breadcrumbs') <nav class="flex" aria-label="Breadcrumb"> <ol class="flex items-center space-x-4"> <li> <div> <a href="{{ route('admin.dashboard') }}" class="text-gray-400 hover:text-gray-500"> <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /> </svg> <span class="sr-only">Home</span> </a> </div> </li> <li> <div class="flex items-center"> <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20"> <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" /> </svg> <span class="ml-4 text-sm font-medium text-gray-500">Dashboard</span> </div> </li> </ol> </nav> @endsection @section('header') <div class="mb-6"> <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1> <p class="mt-1 text-sm">Overview of your website statistics and activities.</p> </div> @endsection @section('content') <!-- Stats Cards --> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8"> <!-- Users Stats --> <div class="bg-white overflow-hidden shadow-soft rounded-lg"> <div class="px-4 py-5 sm:p-6"> <div class="flex items-center"> <div class="flex-shrink-0 bg-navy-100 rounded-md p-3"> <svg class="h-6 w-6 text-navy-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /> </svg> </div> <div class="ml-5 w-0 flex-1"> <dl> <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt> <dd class="flex items-baseline"> <div class="text-2xl font-semibold text-gray-900">{{ $stats['users'] ?? 0 }}</div> <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600"> <svg class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"> <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" /> </svg> <span class="sr-only">Increased by</span> 8.2% </div> </dd> </dl> </div> </div> </div> </div> <!-- Activity Stats --> <div class="bg-white overflow-hidden shadow-soft rounded-lg"> <div class="px-4 py-5 sm:p-6"> <div class="flex items-center"> <div class="flex-shrink-0 bg-saffron-100 rounded-md p-3"> <svg class="h-6 w-6 text-saffron-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" /> </svg> </div> <div class="ml-5 w-0 flex-1"> <dl> <dt class="text-sm font-medium text-gray-500 truncate">Activities</dt> <dd class="flex items-baseline"> <div class="text-2xl font-semibold text-gray-900">{{ $stats['activities'] ?? 0 }}</div> <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600"> <svg class="self-center flex-shrink-0 h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"> <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" /> </svg> <span class="sr-only">Increased by</span> 12.5% </div> </dd> </dl> </div> </div> </div> </div> <!-- Membership Stats --> <div class="bg-white overflow-hidden shadow-soft rounded-lg"> <div class="px-4 py-5 sm:p-6"> <div class="flex items-center"> <div class="flex-shrink-0 bg-green-100 rounded-md p-3"> <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /> </svg> </div> <div class="ml-5 w-0 flex-1"> <dl> <dt class="text-sm font-medium text-gray-500 truncate">Total Memberships</dt> <dd class="flex items-baseline"> <div class="text-2xl font-semibold text-gray-900">{{ $stats['memberships']['total'] ?? 0 }}</div> @if(($stats['memberships']['pending'] ?? 0) > 0) <div class="ml-2 flex items-baseline text-sm font-semibold text-yellow-600"> <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs"> {{ $stats['memberships']['pending'] }} pending </span> </div> @endif </dd> </dl> </div> </div> <div class="mt-4 flex space-x-4 text-sm"> <div class="flex items-center text-green-600"> <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div> {{ $stats['memberships']['approved'] ?? 0 }} Approved </div> <div class="flex items-center text-red-600"> <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div> {{ $stats['memberships']['rejected'] ?? 0 }} Rejected </div> </div> </div> </div> <!-- News Stats --> <div class="bg-white overflow-hidden shadow-soft rounded-lg"> <div class="px-4 py-5 sm:p-6"> <div class="flex items-center"> <div class="flex-shrink-0 bg-maroon-100 rounded-md p-3"> <svg class="h-6 w-6 text-maroon-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" /> </svg> </div> <div class="ml-5 w-0 flex-1"> <dl> <dt class="text-sm font-medium text-gray-500 truncate">News Articles</dt> <dd class="flex items-baseline"> <div class="text-2xl font-semibold text-gray-900">{{ $stats['news'] ?? 0 }}</div> <div class="ml-2 flex items-baseline text-sm font-semibold text-red-600"> <svg class="self-center flex-shrink-0 h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true"> <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd" /> </svg> <span class="sr-only">Decreased by</span> 3.2% </div> </dd> </dl> </div> </div> </div> </div> </div> <!-- Recent Activity and News Cards --> <div class="grid grid-cols-1 lg:grid-cols-2 gap-6"> <!-- Recent Membership Applications --> <div class="bg-white shadow-soft rounded-lg"> <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center"> <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Membership Applications</h3> <div class="flex space-x-2"> <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full"> {{ $stats['memberships']['pending'] ?? 0 }} Pending </span> <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full"> {{ $stats['memberships']['approved'] ?? 0 }} Approved </span> </div> </div> <div class="px-4 py-5 sm:p-6"> <div class="flow-root"> <ul class="-my-5 divide-y divide-gray-200"> @forelse($stats['recent_memberships'] ?? [] as $membership) <li class="py-4"> <div class="flex items-center space-x-4"> <div class="flex-shrink-0"> @if($membership->photo) <img class="h-8 w-8 rounded-full object-cover" src="{{ Storage::url($membership->photo) }}" alt="{{ $membership->name }}"> @else <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-navy-100"> <span class="text-sm font-medium leading-none text-navy-800"> {{ substr($membership->name, 0, 1) }} </span> </span> @endif </div> <div class="flex-1 min-w-0"> <p class="text-sm font-medium text-gray-900 truncate">{{ $membership->name }}</p> <p class="text-sm text-gray-500"> {{ $membership->membership_number }} - {{ ucfirst($membership->membership_type) }} </p> </div> <div class="flex-shrink-0 flex items-center space-x-2"> <span class="px-2 py-1 text-xs font-medium rounded-full {{ $membership->getStatusBadgeClass() }}"> {{ $membership->getStatusDisplayText() }} </span> <span class="whitespace-nowrap text-sm text-gray-500"> {{ $membership->created_at->diffForHumans() }} </span> </div> </div> </li> @empty <li class="py-4"> <div class="text-center text-gray-500"> No membership applications yet. </div> </li> @endforelse </ul> </div> <div class="mt-6"> <a href="{{ route('admin.memberships.index') }}" class="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"> View all applications </a> </div> </div> </div> <!-- Latest News --> <div class="bg-white shadow-soft rounded-lg"> <div class="px-4 py-5 border-b border-gray-200 sm:px-6"> <h3 class="text-lg leading-6 font-medium text-gray-900">Latest News</h3> </div> <div class="px-4 py-5 sm:p-6"> <div class="flow-root"> <ul class="-my-5 divide-y divide-gray-200"> <li class="py-4"> <div class="flex items-center space-x-4"> <div class="flex-shrink-0"> <div class="h-12 w-12 rounded-md bg-maroon-100 flex items-center justify-center"> <svg class="h-6 w-6 text-maroon-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" /> </svg> </div> </div> <div class="flex-1 min-w-0"> <p class="text-sm font-medium text-gray-900 truncate">Important Announcement</p> <p class="text-sm text-gray-500 line-clamp-2"> Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel tincidunt luctus, nisl nisl aliquam. </p> </div> <div class="flex-shrink-0"> <a href="#" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-navy-700 bg-navy-100 hover:bg-navy-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> View </a> </div> </div> </li> <li class="py-4"> <div class="flex items-center space-x-4"> <div class="flex-shrink-0"> <div class="h-12 w-12 rounded-md bg-navy-100 flex items-center justify-center"> <svg class="h-6 w-6 text-navy-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /> </svg> </div> </div> <div class="flex-1 min-w-0"> <p class="text-sm font-medium text-gray-900 truncate">Upcoming Event</p> <p class="text-sm text-gray-500 line-clamp-2"> Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Donec velit neque. </p> </div> <div class="flex-shrink-0"> <a href="#" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-navy-700 bg-navy-100 hover:bg-navy-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> View </a> </div> </div> </li> <li class="py-4"> <div class="flex items-center space-x-4"> <div class="flex-shrink-0"> <div class="h-12 w-12 rounded-md bg-green-100 flex items-center justify-center"> <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /> </svg> </div> </div> <div class="flex-1 min-w-0"> <p class="text-sm font-medium text-gray-900 truncate">New Document Published</p> <p class="text-sm text-gray-500 line-clamp-2"> Fusce vel risus nec ipsum mattis facilisis. Praesent pulvinar eu dui at elementum. </p> </div> <div class="flex-shrink-0"> <a href="#" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-navy-700 bg-navy-100 hover:bg-navy-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> View </a> </div> </div> </li> </ul> </div> <div class="mt-6"> <a href="#" class="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"> View all </a> </div> </div> </div> </div> @endsection 