<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Organization Chart Plugin</title>
  <link rel="icon" href="img/logo.png">
  <link rel="stylesheet" href="css/jquery.orgchart.css">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <div id="chart-container"></div>

  <script type="text/javascript" src="js/jquery.min.js"></script>
  <script type="text/javascript" src="js/jquery.orgchart.js"></script>
  <script type="text/javascript">
    $(function() {

    var datascource = {
      'name': 'Lao Lao',
      'title': 'general manager',
      'children': [
        { 'name': '<PERSON> Miao', 'title': 'department manager',
          'children': [
            { 'name': '<PERSON>', 'title': 'senior engineer' },
            { 'name': '<PERSON>', 'title': 'senior engineer' }
          ]
        },
        { 'name': '<PERSON>', 'title': 'department manager' },
        { 'name': '<PERSON> Mia<PERSON>', 'title': 'department manager',
          'children': [
            { 'name': '<PERSON>ng An', 'title': 'senior engineer' },
            { 'name': '<PERSON> Dian', 'title': 'senior engineer' }
          ]
        }
      ]
    };

    $('#chart-container').orgchart({
      'data' : datascource,
      'nodeContent': 'title',
      'toggleSiblingsResp': true
    });

  });
  </script>
  </body>
</html>