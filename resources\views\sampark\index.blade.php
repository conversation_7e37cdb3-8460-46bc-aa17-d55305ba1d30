@extends('layouts.app')

@section('title', 'संपर्क करें')

@section('content')
<div class="bg-gray-50 py-10">
    <div class="container-custom">
        <!-- Page Header -->
        <div class="text-center mb-10">
            <h1 class="text-3xl md:text-4xl font-bold text-navy-800 mb-4">संपर्क करें</h1>
            <div class="w-24 h-1.5 bg-saffron-500 mx-auto rounded-full mb-6"></div>
            <p class="text-gray-700 max-w-2xl mx-auto">यादव समाज से संपर्क करें और अपने सुझाव साझा करें।</p>
        </div>

        <div class="max-w-4xl mx-auto mb-12">
            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
                    <ul class="list-disc list-inside">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Contact Section -->
            <div class="bg-white rounded-xl shadow-card p-6 md:p-8" id="contact">
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-navy-50 rounded-lg flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-saffron-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h2 class="text-xl font-bold text-navy-800">संपर्क करें</h2>
                    </div>
                    <p class="text-gray-600">हमसे संपर्क करने या पूछताछ के लिए इस फॉर्म का उपयोग करें। हम जल्द से जल्द आपसे संपर्क करेंगे।</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <!-- Contact Information Block -->
                    <div class="bg-navy-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-navy-800 mb-5">संपर्क विवरण</h3>
                        <ul class="space-y-4">
                            <li class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <div>
                                    <h4 class="text-navy-700 font-medium mb-1">पता</h4>
                                    <p class="text-gray-600">{{ config('constants.ADDRESS') }}</p>
                                </div>
                            </li>
                            <li class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <div>
                                    <h4 class="text-navy-700 font-medium mb-1">फोन</h4>
                                    <p class="text-gray-600">{{config('constants.MOBILE') }}</p>
                                </div>
                            </li>
                            <li class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <h4 class="text-navy-700 font-medium mb-1">ईमेल</h4>
                                    <p class="text-gray-600">{{ config('constants.EMAIL') }}</p>
                                </div>
                            </li>
                            <li class="flex">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <h4 class="text-navy-700 font-medium mb-1">कार्यालय समय</h4>
                                    <p class="text-gray-600">{{ $officeHours ?? 'सोमवार - शुक्रवार: सुबह 10:00 - शाम 6:00' }}</p>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- Map Block -->
                    <div class="rounded-lg overflow-hidden h-80 bg-gray-200">
                        <!-- Replace with Google Map iframe or interactive map -->
                        <div class="w-full h-full flex items-center justify-center bg-navy-50">
                            <div class="text-center text-navy-500 p-4">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-saffron-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                </svg>
                                <p class="font-medium">नक्शे के लिए यहां Google मैप का एम्बेड कोड दें</p>
                                <p class="text-sm mt-1">src="https://www.google.com/maps/embed?pb=..."</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div>
                    <h3 class="text-lg font-semibold text-navy-800 mb-5">संपर्क फॉर्म</h3>
                    <form action="{{ route('sampark.submit') }}" method="POST" id="contactForm">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-navy-700 mb-1">नाम</label>
                                <input type="text" name="name" id="name" class="form-input w-full rounded-md" placeholder="अपना नाम दर्ज करें" required>
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-navy-700 mb-1">ईमेल</label>
                                <input type="email" name="email" id="email" class="form-input w-full rounded-md" placeholder="अपना ईमेल दर्ज करें" required>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label for="phone" class="block text-sm font-medium text-navy-700 mb-1">फोन नंबर</label>
                            <input type="tel" name="phone" id="phone" class="form-input w-full rounded-md" placeholder="अपना फोन नंबर दर्ज करें">
                        </div>
                        <div class="mb-4">
                            <label for="subject" class="block text-sm font-medium text-navy-700 mb-1">विषय</label>
                            <input type="text" name="subject" id="subject" class="form-input w-full rounded-md" placeholder="विषय दर्ज करें" required>
                        </div>
                        <div class="mb-4">
                            <label for="message" class="block text-sm font-medium text-navy-700 mb-1">संदेश</label>
                            <textarea name="message" id="message" rows="4" class="form-input w-full rounded-md" placeholder="अपना संदेश यहां लिखें..." required></textarea>
                        </div>
                        <div class="mb-5">
                            <label for="suggestion" class="block text-sm font-medium text-navy-700 mb-1">सुझाव (वैकल्पिक)</label>
                            <textarea name="suggestion" id="suggestion" rows="3" class="form-input w-full rounded-md" placeholder="यदि आपके पास कोई सुझाव है तो यहां लिखें..."></textarea>
                            <p class="text-xs text-gray-500 mt-1">आपके सुझाव हमारे लिए बहुत महत्वपूर्ण हैं और समाज की बेहतरी में सहायक होंगे।</p>
                        </div>
                        <div>
                            <button type="submit" class="btn-primary">
                                <span>भेजें</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>


        <!-- Social Platforms Section -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl shadow-card p-6 md:p-8">
                <h2 class="text-xl font-bold text-navy-800 mb-6">सोशल मीडिया पर जुड़ें</h2>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <!-- Facebook -->
                    <a href="{{ config('constants.SOCIAL_MEDIA.FACEBOOK') }}" class="flex items-center p-4 border border-gray-100 rounded-lg hover:shadow-card transition-all">
                        <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mr-4">
                            <svg class="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-navy-800">फेसबुक</h3>
                            <p class="text-sm text-gray-500">@YadavSamaj</p>
                        </div>
                    </a>

                    <!-- Twitter -->
                    <a href="{{ config('constants.SOCIAL_MEDIA.TWITTER') }}" class="flex items-center p-4 border border-gray-100 rounded-lg hover:shadow-card transition-all">
                        <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mr-4">
                            <svg class="h-6 w-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-navy-800">ट्विटर</h3>
                            <p class="text-sm text-gray-500">@YadavSamaj</p>
                        </div>
                    </a>

                    <!-- Instagram -->
                    <a href="{{ config('constants.SOCIAL_MEDIA.INSTAGRAM') }}" class="flex items-center p-4 border border-gray-100 rounded-lg hover:shadow-card transition-all">
                        <div class="w-12 h-12 bg-pink-50 rounded-lg flex items-center justify-center mr-4">
                            <svg class="h-6 w-6 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-navy-800">इंस्टाग्राम</h3>
                            <p class="text-sm text-gray-500">@YadavSamaj</p>
                        </div>
                    </a>

                    <!-- YouTube -->
                    <a href="{{ config('constants.SOCIAL_MEDIA.YOUTUBE') }}" class="flex items-center p-4 border border-gray-100 rounded-lg hover:shadow-card transition-all">
                        <div class="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center mr-4">
                            <svg class="h-6 w-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-medium text-navy-800">यूट्यूब</h3>
                            <p class="text-sm text-gray-500">यादव समाज चैनल</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .btn-primary {
        @apply inline-flex items-center py-2.5 px-6 bg-saffron-500 text-white rounded-md font-medium hover:bg-saffron-600 transition-colors;
    }

    .btn-secondary {
        @apply inline-flex items-center py-2.5 px-6 bg-navy-600 text-white rounded-md font-medium hover:bg-navy-700 transition-colors;
    }

    .form-input {
        @apply border border-gray-300 focus:border-saffron-500 focus:ring focus:ring-saffron-200 focus:ring-opacity-50 rounded-md shadow-sm px-4 py-2;
    }
</style>

<!-- Form Handling Scripts -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Contact Form Handling
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const submitText = submitBtn.querySelector('span');

                // Show loading state
                submitBtn.disabled = true;
                submitText.textContent = 'भेजा जा रहा है...';

                // Allow form to submit normally to server
                // The form will be processed by Laravel controller
            });
        }
    });
</script>
@endsection