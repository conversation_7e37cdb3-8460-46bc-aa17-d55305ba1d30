@extends('layouts.member')

@section('title', 'प्रोफाइल')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Mobile Header -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-navy-700 lg:hidden">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="{{ route('member.dashboard') }}" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-navy-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white">प्रोफाइल</h1>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Desktop Back Button -->
        <div class="hidden lg:block mb-6">
            <a href="{{ route('member.dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                डैशबोर्ड पर वापस जाएं
            </a>
        </div>

        <!-- Page Header -->
        <div class="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                    प्रोफाइल
                </h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    अपनी व्यक्तिगत जानकारी देखें और अपडेट करें
                </p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="{{ route('member.profile.edit') }}" class="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    प्रोफाइल संपादित करें
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Profile Photo & Basic Info -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg p-6">
                    <div class="text-center">
                        @if($member->photo)
                            <img class="mx-auto h-32 w-32 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                        @else
                            <div class="mx-auto h-32 w-32 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                                <span class="text-4xl font-medium text-teal-600 dark:text-teal-300">
                                    {{ substr($member->name, 0, 1) }}
                                </span>
                            </div>
                        @endif
                        <h3 class="mt-4 text-xl font-semibold text-gray-900 dark:text-white">{{ $member->name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $member->membership_number }}</p>
                        <div class="mt-3">
                            <span class="px-3 py-1 text-sm font-medium rounded-full {{ $member->getStatusBadgeClass() }}">
                                {{ $member->getStatusDisplayText() }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यक्तिगत जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पिता/पति का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->fathers_husband_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">मोबाइल नंबर</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->mobile_number }}</dd>
                            </div>
                            @if($member->birth_date)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">जन्म तिथि</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->birth_date->format('d M Y') }}</dd>
                            </div>
                            @endif
                            @if($member->marital_status)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">वैवाहिक स्थिति</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->marital_status }}</dd>
                            </div>
                            @endif
                            @if($member->family_members)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">परिवार के सदस्य</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->family_members }}</dd>
                            </div>
                            @endif
                            @if($member->education)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">शिक्षा</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->education }}</dd>
                            </div>
                            @endif
                            @if($member->caste_details)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">जाति विवरण</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->caste_details }}</dd>
                            </div>
                            @endif
                            @if($member->email)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">ईमेल</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->email }}</dd>
                            </div>
                            @endif
                            @if($member->membershipVarg)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्यता वर्ग</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400">
                                        {{ $member->membershipVarg->name }}
                                    </span>
                                    @if($member->membershipVarg->fee)
                                        <span class="text-gray-500 text-xs ml-2">- ₹{{ number_format($member->membershipVarg->fee, 2) }}</span>
                                    @endif
                                </dd>
                            </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">आवेदन दिनांक</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->created_at->format('d M Y') }}</dd>
                            </div>
                        </dl>

                        @if($member->address)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पता</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->address }}</dd>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Professional Information -->
                @if($member->department_name || $member->office)
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यावसायिक जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            @if($member->department_name)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">विभाग का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->department_name }}</dd>
                            </div>
                            @endif
                            @if($member->office)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">कार्यालय</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->office }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
                @endif

                <!-- Children Information -->
                @if($member->children && count($member->children) > 0)
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">बच्चों की जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-navy-700">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">नाम</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">लिंग</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">जन्म तिथि</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-navy-700">
                                    @foreach($member->children as $child)
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">{{ $child['name'] ?? 'N/A' }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">{{ $child['gender'] ?? 'N/A' }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">
                                            {{ $child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d M Y') : 'N/A' }}
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Proposer Information -->
                @if($member->member_name_signature || $member->mobile || $member->proposer_address)
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">प्रस्तावक की जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            @if($member->member_name_signature)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्य का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->member_name_signature }}</dd>
                            </div>
                            @endif
                            @if($member->vibhag)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">विभाग</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->vibhag }}</dd>
                            </div>
                            @endif
                            @if($member->mobile)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">मोबाइल</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->mobile }}</dd>
                            </div>
                            @endif
                        </dl>
                        @if($member->proposer_address)
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पता</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $member->proposer_address }}</dd>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Vanshavali (Ancestors) Information -->
                @if($member->ancestors && count($member->ancestors) > 0)
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">पारिवारिक वंशावली</h3>
                            <a href="{{ route('member.family-tree') }}" class="text-sm text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300">
                                विस्तृत वंशावली देखें →
                            </a>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        @php
                            $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                            $generationLabels = [
                                7 => '7वीं पीढ़ी',
                                6 => '6वीं पीढ़ी',
                                5 => '5वीं पीढ़ी',
                                4 => '4वीं पीढ़ी',
                                3 => '3वीं पीढ़ी',
                                2 => '2वीं पीढ़ी (दादा-दादी)',
                                1 => '1वीं पीढ़ी (माता-पिता)'
                            ];
                        @endphp

                        @foreach($generationLabels as $level => $label)
                            @php
                                $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                            @endphp
                            @if($ancestorsInLevel->count() > 0)
                                <div class="mb-6">
                                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3 pb-2 border-b border-gray-200 dark:border-navy-600">
                                        {{ $label }}
                                    </h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($ancestorsInLevel as $ancestor)
                                            <div class="border border-gray-200 dark:border-navy-600 rounded-lg p-4 bg-gray-50 dark:bg-navy-700">
                                                <h5 class="font-bold text-gray-900 dark:text-white mb-2">
                                                    {{ $ancestor['name'] ?? 'N/A' }}
                                                </h5>
                                                <div class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                                    @if(!empty($ancestor['birth_date']))
                                                        <div><strong>जन्म:</strong> {{ \Carbon\Carbon::parse($ancestor['birth_date'])->format('d/m/Y') }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['death_date']))
                                                        <div><strong>मृत्यु:</strong> {{ \Carbon\Carbon::parse($ancestor['death_date'])->format('d/m/Y') }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['birth_place']))
                                                        <div><strong>जन्म स्थान:</strong> {{ $ancestor['birth_place'] }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['occupation']))
                                                        <div><strong>व्यवसाय:</strong> {{ $ancestor['occupation'] }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['spouse']))
                                                        <div><strong>पत्नी/पति:</strong> {{ $ancestor['spouse'] }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['children_count']))
                                                        <div><strong>संतान:</strong> {{ $ancestor['children_count'] }}</div>
                                                    @endif
                                                    @if(!empty($ancestor['gotra']))
                                                        <div><strong>गोत्र:</strong> {{ $ancestor['gotra'] }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection
