<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pramukh_padadhikaris', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('post_in_community');
            $table->text('address');
            $table->string('office_name')->nullable();
            $table->string('office_post')->nullable();
            $table->string('mobile_number', 15);
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pramukh_padadhikaris');
    }
};
