<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DarshanikIshthal;

class DarshanikIshthalController extends Controller
{
    /**
     * Display a listing of Darshanik Ishthal submissions.
     */
    public function index(Request $request)
    {
        $status = $request->query('status', 'all');

        $query = DarshanikIshthal::query()
            ->orderBy('created_at', 'desc');

        if ($status === 'verified') {
            $query->where('is_verified', true);
        } elseif ($status === 'unverified') {
            $query->where('is_verified', false);
        } elseif ($status === 'active') {
            $query->where('is_active', true);
        } elseif ($status === 'inactive') {
            $query->where('is_active', false);
        }

        $submissions = $query->paginate(15);

        return view('admin.darshanik-ishthal.index', [
            'submissions' => $submissions,
            'status' => $status,
            'statusOptions' => [
                'all' => 'All Submissions',
                'verified' => 'Verified',
                'unverified' => 'Unverified',
                'active' => 'Active',
                'inactive' => 'Inactive',
            ]
        ]);
    }

    /**
     * Display the specified Darshanik Ishthal submission.
     */
    public function show(string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);
        return view('admin.darshanik-ishthal.show', compact('submission'));
    }

    /**
     * Verify a Darshanik Ishthal submission.
     */
    public function verify(Request $request, string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);

        $submission->update([
            'is_verified' => true,
        ]);

        // Log the verification action
        \Log::info('Darshanik Ishthal verified', [
            'submission_id' => $submission->id,
            'place_name' => $submission->place_name,
            'verified_by' => auth()->user()->name,
        ]);

        return redirect()->back()->with('success', 'Submission verified successfully.');
    }

    /**
     * Unverify a Darshanik Ishthal submission.
     */
    public function unverify(Request $request, string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);

        $submission->update([
            'is_verified' => false,
        ]);

        // Log the unverification action
        \Log::info('Darshanik Ishthal unverified', [
            'submission_id' => $submission->id,
            'place_name' => $submission->place_name,
            'unverified_by' => auth()->user()->name,
        ]);

        return redirect()->back()->with('success', 'Submission unverified successfully.');
    }

    /**
     * Activate a Darshanik Ishthal submission.
     */
    public function activate(Request $request, string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);

        $submission->update([
            'is_active' => true,
        ]);

        return redirect()->back()->with('success', 'Submission activated successfully.');
    }

    /**
     * Deactivate a Darshanik Ishthal submission.
     */
    public function deactivate(Request $request, string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);

        $submission->update([
            'is_active' => false,
        ]);

        return redirect()->back()->with('success', 'Submission deactivated successfully.');
    }

    /**
     * Remove the specified Darshanik Ishthal submission.
     */
    public function destroy(string $id)
    {
        $submission = DarshanikIshthal::findOrFail($id);

        // Log the deletion action
        \Log::info('Darshanik Ishthal deleted', [
            'submission_id' => $submission->id,
            'place_name' => $submission->place_name,
            'deleted_by' => auth()->user()->name,
        ]);

        $submission->delete();

        return redirect()->route('admin.darshanik-ishthal.index')
            ->with('success', 'Submission deleted successfully.');
    }
}
