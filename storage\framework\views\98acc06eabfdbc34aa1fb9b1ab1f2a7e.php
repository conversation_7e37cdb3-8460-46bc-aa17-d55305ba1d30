

<?php $__env->startSection('title', 'पारिवारिक वंशावली'); ?>

<?php $__env->startSection('head'); ?>
    <!-- jQuery is required for OrgChart JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- OrgChart JS CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/css/jquery.orgchart.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/js/jquery.orgchart.min.js"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-white dark:bg-navy-900">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Simple Header -->
        <div class="mb-6 no-print">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        पारिवारिक वंशावली
                    </h1>
                    <p class="mt-1 text-gray-600 dark:text-gray-400">
                        <?php echo e($member->name); ?> का पूर्वजों का विवरण
                    </p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="window.print()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        प्रिंट करें
                    </button>
                    <button id="toggle-view-btn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        दृश्य बदलें
                    </button>
                    <a href="<?php echo e(route('member.dashboard')); ?>" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                        वापस
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern OrgChart JS Family Tree -->
        <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
            <div class="p-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                    पारिवारिक वंशावली चार्ट
                </h2>

                <!-- Chart Container -->
                <div id="family-tree-chart" class="relative" style="height: 600px; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p class="text-gray-600 dark:text-gray-400">पारिवारिक वृक्ष लोड हो रहा है...</p>
                        </div>
                    </div>
                </div>

                <!-- Test Chart (for debugging) -->
                <div id="test-chart" class="relative mt-4" style="height: 300px; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; display: none;">
                    <h3 class="text-center p-2 bg-gray-100 dark:bg-gray-800">टेस्ट चार्ट</h3>
                </div>

                <!-- Instructions -->
                <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">उपयोग निर्देश:</h3>
                    <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• चार्ट को ड्रैग करके घुमाएं</li>
                        <li>• ज़ूम इन/आउट करने के लिए + और - बटन का उपयोग करें</li>
                        <li>• पूरा चार्ट देखने के लिए ⌂ बटन दबाएं</li>
                        <li>• किसी भी नोड पर क्लिक करके विस्तृत जानकारी देखें</li>
                    </ul>

                    <button id="manual-init-btn" class="mt-3 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                        मैन्युअल इनिशियलाइज़
                    </button>
                    <button id="test-chart-btn" class="mt-3 ml-2 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                        टेस्ट चार्ट
                    </button>
                </div>
            </div>
        </div>

        <?php if($member->ancestors && count($member->ancestors) > 0): ?>
            <!-- Legacy Simple Family Tree (Hidden by default, can be toggled) -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg mt-6" style="display: none;" id="legacy-tree">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                        पारंपरिक सूची दृश्य
                    </h2>
                    <?php
                        // Sort ancestors by generation level (highest to lowest)
                        $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                        $generationLabels = [
                            7 => '7वीं पीढ़ी',
                            6 => '6वीं पीढ़ी',
                            5 => '5वीं पीढ़ी',
                            4 => '4वीं पीढ़ी',
                            3 => '3वीं पीढ़ी',
                            2 => '2वीं पीढ़ी (दादा-दादी)',
                            1 => '1वीं पीढ़ी (माता-पिता)'
                        ];
                    ?>

                    <?php $__currentLoopData = $generationLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                        ?>
                        <?php if($ancestorsInLevel->count() > 0): ?>
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-300 dark:border-navy-600">
                                    <?php echo e($label); ?>

                                </h3>
                                        
                                <div class="grid grid-cols-1 gap-4">
                                    <?php $__currentLoopData = $ancestorsInLevel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border border-gray-300 dark:border-navy-600 rounded p-4 bg-gray-50 dark:bg-navy-700">
                                            <div class="flex flex-col lg:flex-row gap-4">
                                                <!-- Left side - Basic ancestor details -->
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-lg text-gray-900 dark:text-white mb-2">
                                                        <?php echo e($ancestor['name'] ?? 'N/A'); ?>

                                                    </h4>

                                                    <div class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                                        <?php if(!empty($ancestor['birth_date'])): ?>
                                                            <div><strong>जन्म:</strong> <?php echo e($ancestor['birth_date']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['death_date'])): ?>
                                                            <div><strong>मृत्यु:</strong> <?php echo e($ancestor['death_date']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['birth_place'])): ?>
                                                            <div><strong>जन्म स्थान:</strong> <?php echo e($ancestor['birth_place']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['occupation'])): ?>
                                                            <div><strong>व्यवसाय:</strong> <?php echo e($ancestor['occupation']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['spouse'])): ?>
                                                            <div><strong>पत्नी:</strong> <?php echo e($ancestor['spouse']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['children_count'])): ?>
                                                            <div><strong>संतान:</strong> <?php echo e($ancestor['children_count']); ?></div>
                                                        <?php endif; ?>
                                                        <?php if(!empty($ancestor['gotra'])): ?>
                                                            <div><strong>गोत्र:</strong> <?php echo e($ancestor['gotra']); ?></div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <!-- Right side - Children details -->
                                                <?php if(!empty($ancestor['children_details'])): ?>
                                                    <div class="flex-1 lg:max-w-md">
                                                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded p-3">
                                                            <h5 class="font-semibold text-blue-900 dark:text-blue-100 mb-2 text-sm">
                                                                संतान विवरण
                                                            </h5>
                                                            <div class="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-line">
                                                                <?php echo e($ancestor['children_details']); ?>

                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                    <!-- Current Member -->
                    <div class="mt-8 pt-6 border-t-2 border-blue-500">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                            वर्तमान सदस्य
                        </h3>
                        <div class="border border-blue-300 rounded p-4 bg-blue-50 dark:bg-blue-900/20">
                            <h4 class="font-bold text-lg text-blue-900 dark:text-blue-100 mb-2">
                                <?php echo e($member->name); ?>

                            </h4>
                            <div class="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                                <div><strong>पिता:</strong> <?php echo e($member->fathers_husband_name); ?></div>
                                <?php if($member->birth_date): ?>
                                    <div><strong>जन्म:</strong> <?php echo e($member->birth_date->format('d/m/Y')); ?></div>
                                <?php endif; ?>
                                <?php if($member->address): ?>
                                    <div><strong>पता:</strong> <?php echo e(Str::limit($member->address, 50)); ?></div>
                                <?php endif; ?>
                                <?php if($member->department_name): ?>
                                    <div><strong>विभाग:</strong> <?php echo e($member->department_name); ?></div>
                                <?php endif; ?>
                                <div><strong>सदस्यता:</strong> <?php echo e($member->membership_number); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Ancestors Message -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
                <div class="px-6 py-8 text-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">कोई पूर्वज जानकारी नहीं</h3>
                    <p class="mt-2 text-gray-500 dark:text-gray-400">
                        आपके सदस्यता आवेदन में पूर्वजों की जानकारी उपलब्ध नहीं है।
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Children Section -->
        <?php if($member->children && count($member->children) > 0): ?>
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">
                        संतान विवरण
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php $__currentLoopData = $member->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-300 dark:border-navy-600 rounded p-3 bg-gray-50 dark:bg-navy-700">
                                <h4 class="font-bold text-gray-900 dark:text-white"><?php echo e($child['name'] ?? 'N/A'); ?></h4>
                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                    <div><?php echo e($child['gender'] === 'male' ? 'पुत्र' : 'पुत्री'); ?></div>
                                    <?php if(!empty($child['dob'])): ?>
                                        <div>जन्म: <?php echo e(\Carbon\Carbon::parse($child['dob'])->format('d/m/Y')); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    body {
        font-family: 'Noto Sans Devanagari', Arial, sans-serif;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .bg-white {
            background: white !important;
        }

        .border {
            border: 1px solid #000 !important;
        }

        .text-gray-900 {
            color: #000 !important;
        }

        .text-gray-600 {
            color: #333 !important;
        }

        .bg-gray-50 {
            background: #f9f9f9 !important;
        }

        .bg-blue-50 {
            background: #e6f3ff !important;
        }

        .text-blue-900 {
            color: #1e3a8a !important;
        }

        .text-blue-700 {
            color: #1d4ed8 !important;
        }

        .text-blue-800 {
            color: #1e40af !important;
        }

        .text-blue-100 {
            color: #dbeafe !important;
        }

        .text-blue-200 {
            color: #bfdbfe !important;
        }

        .border-blue-500 {
            border-color: #3b82f6 !important;
        }

        .border-blue-300 {
            border-color: #93c5fd !important;
        }

        .border-blue-200 {
            border-color: #bfdbfe !important;
        }

        .border-blue-700 {
            border-color: #1d4ed8 !important;
        }
    }
</style>

<script>
    // Pass family tree data to JavaScript
    window.familyTreeData = <?php echo json_encode($familyTreeData, 15, 512) ?>;

    // Family Tree Functions
    function getNodeColor(generation) {
        const colors = {
            0: '#dbeafe',    // Current member - blue
            1: '#dcfce7',    // Parents - green
            2: '#fef3c7',    // Grandparents - yellow
            3: '#fed7d7',    // Great-grandparents - red
            4: '#e9d5ff',    // 4th generation - purple
            5: '#fce7f3',    // 5th generation - pink
            6: '#f3e8ff',    // 6th generation - indigo
            7: '#f0f9ff'     // 7th generation - sky
        };
        return colors[generation] || '#f9fafb';
    }

    function getBorderColor(generation) {
        const colors = {
            0: '#3b82f6',    // Current member - blue
            1: '#10b981',    // Parents - green
            2: '#f59e0b',    // Grandparents - yellow
            3: '#ef4444',    // Great-grandparents - red
            4: '#8b5cf6',    // 4th generation - purple
            5: '#ec4899',    // 5th generation - pink
            6: '#6366f1',    // 6th generation - indigo
            7: '#0ea5e9'     // 7th generation - sky
        };
        return colors[generation] || '#d1d5db';
    }

    function getGenerationLabel(generation) {
        const labels = {
            0: 'वर्तमान सदस्य',
            1: '1वीं पीढ़ी (माता-पिता)',
            2: '2वीं पीढ़ी (दादा-दादी)',
            3: '3वीं पीढ़ी',
            4: '4वीं पीढ़ी',
            5: '5वीं पीढ़ी',
            6: '6वीं पीढ़ी',
            7: '7वीं पीढ़ी'
        };
        return labels[generation] || `${generation}वीं पीढ़ी`;
    }

    function convertToOrgChartFormat(nodeDataArray) {
        // Find the root node (current member)
        const rootNode = nodeDataArray.find(node => node.isRoot || node.generation === 0);

        if (!rootNode) {
            console.error('No root node found in data:', nodeDataArray);
            return {};
        }

        // For OrgChart JS, we need a simple structure with children
        const result = {
            'name': rootNode.name,
            'title': rootNode.name,
            'father': rootNode.father,
            'birth_year': rootNode.birth_year,
            'village': rootNode.village,
            'generation': rootNode.generation,
            'children': []
        };

        // Add all nodes with pid = root.id as children
        const directChildren = nodeDataArray.filter(node => node.pid === rootNode.id);

        if (directChildren.length > 0) {
            result.children = directChildren.map(child => ({
                'name': child.name,
                'title': child.name,
                'father': child.father,
                'birth_year': child.birth_year,
                'death_year': child.death_year,
                'village': child.village,
                'generation': child.generation
            }));
        }

        console.log('Converted data:', result);
        return result;
    }

    function createNodeTemplate(data) {
        return `
            <div style="text-align: center;">
                <div style="font-weight: bold; font-size: 14px; color: #1f2937; margin-bottom: 4px;">
                    ${data.name || 'अज्ञात'}
                </div>
                ${data.father ? `<div style="font-size: 12px; color: #6b7280;">पिता: ${data.father}</div>` : ''}
                ${data.birth_year ? `<div style="font-size: 12px; color: #6b7280;">जन्म: ${data.birth_year}</div>` : ''}
                ${data.death_year ? `<div style="font-size: 12px; color: #6b7280;">मृत्यु: ${data.death_year}</div>` : ''}
                ${data.village ? `<div style="font-size: 12px; color: #6b7280;">गांव: ${data.village}</div>` : ''}
                ${data.generation !== undefined ? `<div style="font-size: 11px; color: #9ca3af; margin-top: 4px;">${getGenerationLabel(data.generation)}</div>` : ''}
            </div>
        `;
    }

    function initializeFamilyTree() {
        // Get family tree data from the page
        const familyTreeData = window.familyTreeData || {};

        console.log('Raw family tree data:', familyTreeData);

        if (!familyTreeData.nodeDataArray || familyTreeData.nodeDataArray.length === 0) {
            document.getElementById('family-tree-chart').innerHTML =
                '<div class="text-center py-8 text-gray-500">कोई पारिवारिक डेटा उपलब्ध नहीं है</div>';
            return;
        }

        try {
            // Convert data to jQuery OrgChart format
            const chartData = convertToOrgChartFormat(familyTreeData.nodeDataArray);

            console.log('Chart data for OrgChart:', chartData);

            // Clear any previous chart
            $('#family-tree-chart').empty();

            // Create a simple structure for testing
            const testData = {
                'name': 'वर्तमान सदस्य',
                'title': familyTreeData.nodeDataArray[0].name,
                'children': [
                    { 'name': 'पिता', 'title': familyTreeData.nodeDataArray[0].father || 'पिता' }
                ]
            };

            // Initialize jQuery OrgChart with simple data first
            $('#family-tree-chart').orgchart({
                'data': testData,
                'nodeContent': 'title',
                'direction': 't2b',
                'pan': true,
                'zoom': true,
                'toggleSiblingsResp': true,
                'createNode': function($node, data) {
                    // Custom node styling
                    const generation = data.generation || (data.name === 'पिता' ? 1 : 0);

                    $node.addClass('family-node');
                    $node.css({
                        'background': getNodeColor(generation),
                        'border': `2px solid ${getBorderColor(generation)}`,
                        'border-radius': '8px',
                        'padding': '12px',
                        'min-width': '200px',
                        'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
                        'font-family': "'Noto Sans Devanagari', sans-serif"
                    });
                }
            });

            // Add zoom controls
            addZoomControls();

            // Add generation legend
            addGenerationLegend();

            console.log('Family tree initialized successfully');
        } catch (error) {
            console.error('Error initializing family tree:', error);
            document.getElementById('family-tree-chart').innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <p>फैमिली ट्री लोड करने में त्रुटि: ${error.message}</p>
                </div>
            `;
        }
    }

    function addZoomControls() {
        const controlsHtml = `
            <div id="tree-controls" style="
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 1000;
                display: flex;
                gap: 8px;
            ">
                <button id="zoom-in" class="tree-control-btn" title="ज़ूम इन">+</button>
                <button id="zoom-out" class="tree-control-btn" title="ज़ूम आउट">-</button>
                <button id="zoom-fit" class="tree-control-btn" title="फिट करें">⌂</button>
            </div>
        `;

        $('#family-tree-chart').prepend(controlsHtml);

        // Add event listeners for zoom controls
        $('#zoom-in').on('click', function() {
            const $chart = $('#family-tree-chart .orgchart');
            const currentScale = parseFloat($chart.css('transform').match(/scale\(([^)]+)\)/)?.[1] || 1);
            const newScale = Math.min(currentScale * 1.2, 3);
            $chart.css('transform', `scale(${newScale})`);
        });

        $('#zoom-out').on('click', function() {
            const $chart = $('#family-tree-chart .orgchart');
            const currentScale = parseFloat($chart.css('transform').match(/scale\(([^)]+)\)/)?.[1] || 1);
            const newScale = Math.max(currentScale * 0.8, 0.3);
            $chart.css('transform', `scale(${newScale})`);
        });

        $('#zoom-fit').on('click', function() {
            const $chart = $('#family-tree-chart .orgchart');
            $chart.css('transform', 'scale(1)');
            $chart.css('transform-origin', 'center center');
        });
    }

    function addGenerationLegend() {
        const legendHtml = `
            <div id="generation-legend" style="
                position: absolute;
                bottom: 10px;
                left: 10px;
                background: white;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                padding: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                font-family: 'Noto Sans Devanagari', sans-serif;
                font-size: 12px;
                max-width: 200px;
                z-index: 1000;
            ">
                <div style="font-weight: bold; margin-bottom: 8px;">पीढ़ी रंग कोड</div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 16px; height: 16px; background: ${getNodeColor(0)}; border: 1px solid ${getBorderColor(0)}; margin-right: 8px; border-radius: 2px;"></div>
                    वर्तमान सदस्य
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 16px; height: 16px; background: ${getNodeColor(1)}; border: 1px solid ${getBorderColor(1)}; margin-right: 8px; border-radius: 2px;"></div>
                    माता-पिता
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 16px; height: 16px; background: ${getNodeColor(2)}; border: 1px solid ${getBorderColor(2)}; margin-right: 8px; border-radius: 2px;"></div>
                    दादा-दादी
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 16px; height: 16px; background: ${getNodeColor(3)}; border: 1px solid ${getBorderColor(3)}; margin-right: 8px; border-radius: 2px;"></div>
                    पूर्वज
                </div>
            </div>
        `;

        $('#family-tree-chart').prepend(legendHtml);
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between modern and legacy views
        const toggleBtn = document.getElementById('toggle-view-btn');
        const modernView = document.getElementById('family-tree-chart').closest('.bg-white');
        const legacyView = document.getElementById('legacy-tree');
        let isModernView = true;

        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                if (isModernView) {
                    modernView.style.display = 'none';
                    if (legacyView) {
                        legacyView.style.display = 'block';
                    }
                    toggleBtn.textContent = 'आधुनिक दृश्य';
                    isModernView = false;
                } else {
                    modernView.style.display = 'block';
                    if (legacyView) {
                        legacyView.style.display = 'none';
                    }
                    toggleBtn.textContent = 'दृश्य बदलें';
                    isModernView = true;
                }
            });
        }

        // Manual initialization button
        const manualInitBtn = document.getElementById('manual-init-btn');
        if (manualInitBtn) {
            manualInitBtn.addEventListener('click', function() {
                console.log('Manual initialization triggered');
                initializeFamilyTree();
            });
        }

        // Test chart button
        const testChartBtn = document.getElementById('test-chart-btn');
        if (testChartBtn) {
            testChartBtn.addEventListener('click', function() {
                console.log('Test chart triggered');
                initializeTestChart();
            });
        }

        // Simple test chart to verify OrgChart is working
        function initializeTestChart() {
            const testChart = document.getElementById('test-chart');
            testChart.style.display = 'block';

            // Simple test data
            const testData = {
                'name': 'CEO',
                'title': 'Chief Executive Officer',
                'children': [
                    { 'name': 'Manager', 'title': 'Department Manager' },
                    { 'name': 'Manager', 'title': 'Department Manager' }
                ]
            };

            // Initialize test chart
            $('#test-chart').empty();
            $('#test-chart').orgchart({
                'data': testData,
                'nodeContent': 'title',
                'direction': 't2b'
            });

            console.log('Test chart initialized');
        }

        // Wait for jQuery and OrgChart to be available
        function checkLibraries() {
            console.log('Checking libraries...', {
                jquery: typeof $ !== 'undefined',
                orgchart: typeof $ !== 'undefined' && $.fn.orgchart,
                data: window.familyTreeData
            });

            if (typeof $ !== 'undefined' && $.fn.orgchart) {
                console.log('Libraries loaded, initializing family tree...');
                // Initialize family tree - always try to initialize
                initializeFamilyTree();
            } else {
                console.log('Libraries not ready, retrying...');
                // Retry after a short delay if libraries are not yet loaded
                setTimeout(checkLibraries, 500);
            }
        }
        checkLibraries();
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\cgysss-n\resources\views/member/family-tree.blade.php ENDPATH**/ ?>