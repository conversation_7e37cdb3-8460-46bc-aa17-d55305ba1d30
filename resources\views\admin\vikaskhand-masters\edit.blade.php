@extends('layouts.admin') @section('title', 'विकासखंड संपादित करें') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">विकासखंड संपादित करें</h1> <p class="mt-1 text-sm admin-text-secondary">{{ $vikaskhandMaster->sub_district_name_hin }} को संपादित करें</p> </div> <a href="{{ route('admin.vikaskhand-masters.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg> वापस जाएं </a> </div> </div> <!-- Form --> <div class="admin-card p-6"> <form method="POST" action="{{ route('admin.vikaskhand-masters.update', $vikaskhandMaster->id) }}" class="space-y-6"> @csrf @method('PUT') <!-- Sub-District LGD Code --> <div> <label for="sub_district_lgd_code" class="block text-sm font-medium admin-text-primary mb-2"> विकासखंड LGD कोड <span class="text-red-500">*</span> </label> <input type="text" id="sub_district_lgd_code" name="sub_district_lgd_code" value="{{ old('sub_district_lgd_code', $vikaskhandMaster->sub_district_lgd_code) }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sub_district_lgd_code') border-red-500 @enderror" placeholder="उदाहरण: 12345"> @error('sub_district_lgd_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Sub-District Name English --> <div> <label for="sub_district_name_eng" class="block text-sm font-medium admin-text-primary mb-2"> विकासखंड नाम (अंग्रेजी) <span class="text-red-500">*</span> </label> <input type="text" id="sub_district_name_eng" name="sub_district_name_eng" value="{{ old('sub_district_name_eng', $vikaskhandMaster->sub_district_name_eng) }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sub_district_name_eng') border-red-500 @enderror" placeholder="उदाहरण: Sadar"> @error('sub_district_name_eng') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Sub-District Name Hindi --> <div> <label for="sub_district_name_hin" class="block text-sm font-medium admin-text-primary mb-2"> विकासखंड नाम (हिंदी) <span class="text-red-500">*</span> </label> <input type="text" id="sub_district_name_hin" name="sub_district_name_hin" value="{{ old('sub_district_name_hin', $vikaskhandMaster->sub_district_name_hin) }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sub_district_name_hin') border-red-500 @enderror" placeholder="उदाहरण: सदर"> @error('sub_district_name_hin') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- District --> <div> <label for="district_lgd_code" class="block text-sm font-medium admin-text-primary mb-2"> जिला <span class="text-red-500">*</span> </label> <select id="district_lgd_code" name="district_lgd_code" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('district_lgd_code') border-red-500 @enderror"> <option value="">जिला चुनें</option> @foreach($districts as $district) <option value="{{ $district->district_lgd_code }}" {{ old('district_lgd_code', $vikaskhandMaster->district_lgd_code) === $district->district_lgd_code ? 'selected' : '' }}> {{ $district->district_name_hin }} ({{ $district->district_name_eng }}) </option> @endforeach </select> @error('district_lgd_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div> <label for="display_order" class="block text-sm font-medium admin-text-primary mb-2"> प्रदर्शन क्रम </label> <input type="number" id="display_order" name="display_order" value="{{ old('display_order', $vikaskhandMaster->display_order) }}" min="0" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('display_order') border-red-500 @enderror" placeholder="0"> @error('display_order') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror <p class="mt-1 text-sm admin-text-secondary">ड्रॉपडाउन में दिखाने के लिए क्रम (0 = पहले)</p> </div> <!-- Is Active --> <div> <div class="flex items-center"> <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $vikaskhandMaster->is_active) ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"> <label for="is_active" class="ml-2 block text-sm admin-text-primary"> सक्रिय स्थिति </label> </div> <p class="mt-1 text-sm admin-text-secondary">सक्रिय विकासखंड ही ड्रॉपडाउन में दिखाए जाएंगे</p> </div> <!-- Submit Buttons --> <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200"> <a href="{{ route('admin.vikaskhand-masters.index') }}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50:bg-navy-700 font-medium transition-colors"> रद्द करें </a> <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> अपडेट करें </button> </div> </form> </div> </div> </div> @endsection 