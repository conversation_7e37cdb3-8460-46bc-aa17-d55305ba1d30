@extends('layouts.admin') @section('title', 'Add New Activity') @section('breadcrumbs') <div class="text-sm breadcrumbs text-gray-500"> <ul> <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li> <li><a href="{{ route('admin.activities.index') }}">Activities</a></li> <li>Add New</li> </ul> </div> @endsection @section('header') <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6"> <h1 class="text-2xl font-bold">Add New Activity</h1> </div> @endsection @section('content') @if ($errors->any()) <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /> </svg> </div> <div class="ml-3"> <p class="text-sm text-red-700"> <strong>There were errors with your submission:</strong> <ul class="list-disc pl-5 mt-1"> @foreach ($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </p> </div> </div> </div> @endif <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6"> @csrf <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Category Selection --> <div> <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category <span class="text-red-600">*</span></label> <select id="category" name="category" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 sm:text-sm rounded-md"> <option value="">Select category</option> @foreach($categoryOptions as $key => $label) <option value="{{ $key }}" {{ old('category') == $key ? 'selected' : '' }}>{{ $label }}</option> @endforeach </select> @error('category') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Event Date --> <div> <label for="event_date" class="block text-sm font-medium text-gray-700 mb-1">Event Date</label> <input type="date" name="event_date" id="event_date" value="{{ old('event_date') }}" class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> @error('event_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> <!-- Title --> <div> <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title <span class="text-red-600">*</span></label> <input type="text" name="title" id="title" value="{{ old('title') }}" required class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> @error('title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Location --> <div> <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label> <input type="text" name="location" id="location" value="{{ old('location') }}" class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> @error('location') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Description --> <div class="space-y-3"> <label for="description" class="block text-sm font-medium text-gray-700 mb-2"> Description <span class="text-red-600">*</span> </label> <!-- Content Formatting Tips --> <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4"> <h4 class="text-sm font-medium text-purple-800 mb-2">🎯 Activity Description Tips:</h4> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-purple-700"> <div> <p class="font-medium mb-1">📋 Activity Details:</p> <ul class="list-disc pl-4 space-y-1"> <li>Describe the purpose and goals</li> <li>Include date, time, and location</li> <li>Mention participants and organizers</li> </ul> </div> <div> <p class="font-medium mb-1">✨ Best Practices:</p> <ul class="list-disc pl-4 space-y-1"> <li>Use engaging and descriptive language</li> <li>Highlight key achievements</li> <li>Include outcomes and impact</li> </ul> </div> </div> </div> <textarea id="description" name="description" rows="15" required class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm text-sm border-gray-300 rounded-lg resize-y" placeholder="यहाँ गतिविधि का विस्तृत विवरण लिखें... उदाहरण: गतिविधि का नाम: [गतिविधि का नाम] दिनांक: [तारीख] स्थान: [स्थान का नाम] उद्देश्य: [गतिविधि का मुख्य उद्देश्य] विवरण: [गतिविधि की विस्तृत जानकारी] परिणाम: [गतिविधि के परिणाम और प्रभाव]">{{ old('description') }}</textarea> @error('description') <p class="mt-2 text-sm text-red-600 flex items-center"> <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path> </svg> {{ $message }} </p> @enderror <!-- Character Counter --> <div class="flex justify-between items-center text-xs text-gray-500"> <span>Activity description will be formatted for better readability</span> <span id="char-counter">0 characters</span> </div> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Image Upload --> <div class="image-field"> <label for="image" class="block text-sm font-medium text-gray-700 mb-1">Image <span class="gallery-required hidden text-red-600">*</span> <span class="non-gallery-text">(Optional)</span></label> <div class="mt-1 flex items-center"> <input type="file" name="image" id="image" accept="image/*" class="focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-navy-50:bg-navy-900 file:text-navy-700:text-navy-300 hover:file:bg-navy-100:file:bg-navy-800"> </div> <p class="text-xs text-gray-500 mt-1">PNG, JPG, or JPEG up to 2MB</p> @error('image') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Video URL (Only for Gallery) --> <div class="video-url-field hidden"> <label for="video_url" class="block text-sm font-medium text-gray-700 mb-1">Video URL <span class="text-sm text-gray-500">(YouTube/Vimeo)</span></label> <input type="url" name="video_url" id="video_url" value="{{ old('video_url') }}" placeholder="https://www.youtube.com/watch?v=..." class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> <p class="text-xs text-gray-500 mt-1">Enter full URL for YouTube, Vimeo, or other video platforms</p> @error('video_url') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div> <label for="display_order" class="block text-sm font-medium text-gray-700 mb-1">Display Order</label> <input type="number" name="display_order" id="display_order" value="{{ old('display_order', 0) }}" class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p> @error('display_order') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> <!-- Is Published --> <div class="flex items-center"> <input id="is_published" name="is_published" type="checkbox" checked class="h-4 w-4 text-navy-600 focus:ring-navy-500:ring-navy-400 border-gray-300 rounded"> <label for="is_published" class="ml-2 block text-sm text-gray-900"> Publish immediately </label> </div> <!-- Category-specific Help Text --> <div class="gallery-help hidden bg-blue-50 border-l-4 border-blue-500 p-4"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clip-rule="evenodd" /> </svg> </div> <div class="ml-3"> <p class="text-sm text-blue-700"> <strong>Gallery items:</strong> For gallery items, you can upload an image and/or provide a video URL from platforms like YouTube or Vimeo. At least one of these is required. </p> </div> </div> </div> <!-- Action Buttons --> <div class="flex justify-end space-x-3 pt-5"> <a href="{{ route('admin.activities.index') }}" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:ring-navy-400"> Cancel </a> <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-navy-600 hover:bg-navy-700:bg-navy-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:ring-navy-400"> Save Activity </button> </div> </form> @endsection @push('scripts') <script> document.addEventListener('DOMContentLoaded', function() { const descriptionTextarea = document.getElementById('description'); const charCounter = document.getElementById('char-counter'); const titleInput = document.getElementById('title'); const slugInput = document.getElementById('slug'); // Character counter function updateCharCounter() { const count = descriptionTextarea.value.length; charCounter.textContent = count.toLocaleString() + ' characters'; // Color coding based on length if (count < 100) { charCounter.className = 'text-red-500'; } else if (count < 300) { charCounter.className = 'text-yellow-500'; } else { charCounter.className = 'text-green-500'; } } // Auto-generate slug from title function generateSlug() { if (titleInput && slugInput) { const title = titleInput.value; const slug = title.toLowerCase() .replace(/[^a-z0-9\u0900-\u097F\s-]/g, '') // Allow Devanagari characters .replace(/\s+/g, '-') .replace(/-+/g, '-') .trim('-'); if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') { slugInput.value = slug; slugInput.dataset.autoGenerated = 'true'; } } } // Event listeners if (descriptionTextarea) { descriptionTextarea.addEventListener('input', updateCharCounter); updateCharCounter(); // Initial count } if (titleInput) { titleInput.addEventListener('input', generateSlug); } if (slugInput) { slugInput.addEventListener('input', function() { // Mark as manually edited if user types in slug field if (this.value !== '') { this.dataset.autoGenerated = 'false'; } }); } // Form validation enhancement const form = document.querySelector('form'); if (form) { form.addEventListener('submit', function(e) { const description = descriptionTextarea.value.trim(); if (description.length < 10) { e.preventDefault(); alert('कृपया कम से कम 10 अक्षरों का विवरण लिखें।'); descriptionTextarea.focus(); return false; } }); } // Category-specific fields logic const categorySelect = document.getElementById('category'); const videoUrlField = document.querySelector('.video-url-field'); const galleryHelp = document.querySelector('.gallery-help'); const galleryRequired = document.querySelector('.gallery-required'); const nonGalleryText = document.querySelector('.non-gallery-text'); function updateFieldsBasedOnCategory() { const selectedCategory = categorySelect.value; if (selectedCategory === 'gallery') { videoUrlField.classList.remove('hidden'); galleryHelp.classList.remove('hidden'); galleryRequired.classList.remove('hidden'); nonGalleryText.classList.add('hidden'); } else { videoUrlField.classList.add('hidden'); galleryHelp.classList.add('hidden'); galleryRequired.classList.add('hidden'); nonGalleryText.classList.remove('hidden'); } } // Initial state setup updateFieldsBasedOnCategory(); // Listen for category changes categorySelect.addEventListener('change', updateFieldsBasedOnCategory); }); </script> @endpush 