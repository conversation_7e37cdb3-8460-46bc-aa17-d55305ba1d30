<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VikaskhandMaster extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sub_district_lgd_code',
        'sub_district_name_eng',
        'sub_district_name_hin',
        'district_lgd_code',
        'is_active',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active vikaskhands.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive vikaskhands.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('sub_district_name_eng');
    }

    /**
     * Get the district that this vikaskhand belongs to.
     */
    public function district()
    {
        return $this->belongsTo(DistrictMaster::class, 'district_lgd_code', 'district_lgd_code');
    }

    /**
     * Get memberships that use this vikaskhand.
     */
    public function memberships()
    {
        return $this->hasMany(Membership::class, 'vikaskhand_master_id');
    }

    /**
     * Get vaivahik panjiyans that use this vikaskhand.
     */
    public function vaivahikPanjiyans()
    {
        return $this->hasMany(VaivahikPanjiyan::class, 'vikaskhand_master_id');
    }
}
