<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\YadavVyaparGrahak;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class YadavVyaparGrahakController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = YadavVyaparGrahak::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('naam', 'like', "%{$search}%")
                  ->orWhere('sadasyata_kramank', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhere('ajivika', 'like', "%{$search}%")
                  ->orWhere('jila', 'like', "%{$search}%")
                  ->orWhere('vikaskhand', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        $yadavVyaparGrahak = $query->orderBy('created_at', 'desc')->paginate(15);
        $categories = YadavVyaparGrahak::getCategories();

        return view('admin.yadav-vyapar-grahak.index', compact('yadavVyaparGrahak', 'categories'));
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $yadavVyapar = YadavVyaparGrahak::findOrFail($id);
        return view('admin.yadav-vyapar-grahak.show', compact('yadavVyapar'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $yadavVyapar = YadavVyaparGrahak::findOrFail($id);
        $categories = YadavVyaparGrahak::getCategories();
        return view('admin.yadav-vyapar-grahak.edit', compact('yadavVyapar', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $yadavVyapar = YadavVyaparGrahak::findOrFail($id);

            $validated = $request->validate([
                'category' => 'required|in:swasthya,gharelu,salahkar,anya vyavasay',
                'sadasyata_kramank' => 'required|string|max:255',
                'sadasyata_prakar' => 'required|string|max:255',
                'naam' => 'required|string|max:255',
                'ajivika' => 'required|string|max:255',
                'ward' => 'required|string|max:255',
                'vikaskhand' => 'required|string|max:255',
                'jila' => 'required|string|max:255',
                'mobile_number' => 'required|string|size:10|regex:/^[6-9][0-9]{9}$/',
                'address' => 'required|string|max:1000',
                'photo' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
                'gmap_link' => 'nullable|string|max:500',
            ]);

            $data = $validated;

            // Handle photo upload if new file is provided
            if ($request->hasFile('photo') && $request->file('photo')->isValid()) {
                // Delete old file
                $yadavVyapar->deletePhotoFile();

                // Store new file
                $data['photo'] = $request->file('photo')->store('yadav-vyapar-grahak/photos', 'public');
            }

            $yadavVyapar->update($data);

            return redirect()
                ->route('admin.yadav-vyapar-grahak.index')
                ->with('success', 'यादव व्यापार ग्राहक रिकॉर्ड सफलतापूर्वक अपडेट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'रिकॉर्ड अपडेट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $yadavVyapar = YadavVyaparGrahak::findOrFail($id);
            $yadavVyapar->delete(); // This will also delete the photo file due to model boot method

            return redirect()
                ->route('admin.yadav-vyapar-grahak.index')
                ->with('success', 'यादव व्यापार ग्राहक रिकॉर्ड सफलतापूर्वक डिलीट हो गया।');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'रिकॉर्ड डिलीट करने में त्रुटि: ' . $e->getMessage()]);
        }
    }

    /**
     * Download photo file
     */
    public function downloadPhoto(string $id)
    {
        try {
            $yadavVyapar = YadavVyaparGrahak::findOrFail($id);
            
            if (!$yadavVyapar->photo || !Storage::exists($yadavVyapar->photo)) {
                return redirect()->back()
                    ->withErrors(['error' => 'फोटो फाइल उपलब्ध नहीं है।']);
            }

            return Storage::download($yadavVyapar->photo, $yadavVyapar->naam . '_photo.' . pathinfo($yadavVyapar->photo, PATHINFO_EXTENSION));

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'फाइल डाउनलोड करने में त्रुटि: ' . $e->getMessage()]);
        }
    }
}
