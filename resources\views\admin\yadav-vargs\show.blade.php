@extends('layouts.admin') @section('title', 'यादव वर्ग विवरण') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">यादव वर्ग विवरण</h1> <p class="mt-1 text-sm admin-text-secondary">{{ $yadavVarg->name }} की जानकारी</p> </div> <div class="flex space-x-2"> <a href="{{ route('admin.yadav-vargs.edit', $yadavVarg) }}" class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> संपादित करें </a> <a href="{{ route('admin.yadav-vargs.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg> वापस जाएं </a> </div> </div> </div> <!-- Details Card --> <div class="admin-card p-6 mb-6"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Basic Information --> <div class="space-y-4"> <h3 class="text-lg font-semibold admin-text-primary border-b border-gray-200 pb-2"> मूलभूत जानकारी </h3> <div> <label class="block text-sm font-medium admin-text-secondary">नाम</label> <p class="mt-1 text-sm admin-text-primary font-medium">{{ $yadavVarg->name }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary">अंग्रेजी नाम</label> <p class="mt-1 text-sm admin-text-primary">{{ $yadavVarg->name_english ?? 'उपलब्ध नहीं' }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary">श्रेणी</label> <p class="mt-1 text-sm admin-text-primary">{{ $yadavVarg->category ?? 'उपलब्ध नहीं' }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary">प्रदर्शन क्रम</label> <p class="mt-1 text-sm admin-text-primary">{{ $yadavVarg->display_order }}</p> </div> </div> <!-- Status and Dates --> <div class="space-y-4"> <h3 class="text-lg font-semibold admin-text-primary border-b border-gray-200 pb-2"> स्थिति और तारीखें </h3> <div> <label class="block text-sm font-medium admin-text-secondary">स्थिति</label> <div class="mt-1"> @if($yadavVarg->is_active) <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"> सक्रिय </span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"> निष्क्रिय </span> @endif </div> </div> <div> <label class="block text-sm font-medium admin-text-secondary">बनाया गया</label> <p class="mt-1 text-sm admin-text-primary">{{ $yadavVarg->created_at->format('d/m/Y H:i') }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary">अंतिम अपडेट</label> <p class="mt-1 text-sm admin-text-primary">{{ $yadavVarg->updated_at->format('d/m/Y H:i') }}</p> </div> </div> </div> <!-- Description --> @if($yadavVarg->description) <div class="mt-6 pt-6 border-t border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary mb-3">विवरण</h3> <div class="bg-gray-50 rounded-lg p-4"> <p class="text-sm admin-text-primary whitespace-pre-wrap">{{ $yadavVarg->description }}</p> </div> </div> @endif </div> <!-- Usage Statistics --> <div class="admin-card p-6"> <h3 class="text-lg font-semibold admin-text-primary border-b border-gray-200 pb-2 mb-4"> उपयोग की जानकारी </h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div class="bg-blue-50 rounded-lg p-4"> <div class="flex items-center"> <div class="flex-shrink-0"> <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path> </svg> </div> <div class="ml-4"> <p class="text-sm font-medium text-blue-600">सदस्यताओं में उपयोग</p> <p class="text-2xl font-bold text-blue-900">{{ $yadavVarg->memberships()->count() }}</p> </div> </div> </div> <div class="bg-green-50 rounded-lg p-4"> <div class="flex items-center"> <div class="flex-shrink-0"> <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> </div> <div class="ml-4"> <p class="text-sm font-medium text-green-600">वैवाहिक पंजीयन में उपयोग</p> <p class="text-2xl font-bold text-green-900">{{ $yadavVarg->vaivahikPanjiyans()->count() }}</p> </div> </div> </div> </div> @if($yadavVarg->memberships()->count() > 0 || $yadavVarg->vaivahikPanjiyans()->count() > 0) <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <p class="text-sm text-yellow-700"> <strong>सूचना:</strong> यह यादव वर्ग वर्तमान में उपयोग में है, इसलिए इसे हटाया नहीं जा सकता। </p> </div> </div> </div> @endif </div> </div> </div> @endsection 