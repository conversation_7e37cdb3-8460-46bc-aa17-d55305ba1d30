<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Organization Chart Plugin</title>
  <link rel="icon" href="img/logo.png">
  <link rel="stylesheet" href="css/jquery.orgchart.css">
  <link rel="stylesheet" href="css/style.css">
  <style type="text/css">
    #chart-container { text-align: left; }
  </style>
</head>
<body>
  <div id="chart-container"></div>

  <script type="text/javascript" src="js/jquery.min.js"></script>
  <script type="text/javascript" src="js/jquery.orgchart.js"></script>
  <script type="text/javascript">
    $(function() {

    var datascource = {
      'name': 'Lao Lao',
      'title': 'general manager',
      'children': [
        { 'name': '<PERSON> Miao', 'title': 'department manager' },
        { 'name': '<PERSON> Mia<PERSON>', 'title': 'department manager',
          'children': [
            { 'name': '<PERSON><PERSON> Hu<PERSON>', 'title': 'senior engineer' },
            { 'name': 'Hei <PERSON><PERSON>', 'title': 'senior engineer' }
          ]
        },
        { 'name': 'Hong Miao', 'title': 'department manager' },
        { 'name': 'Chun Miao', 'title': 'department manager' }
      ]
    };

    $('#chart-container').orgchart({
      'data' : datascource,
      'nodeContent': 'title',
      'direction': 'l2r'
    });

  });
  </script>
  </body>
</html>