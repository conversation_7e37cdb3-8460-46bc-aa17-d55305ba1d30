import { Selector } from 'testcafe';

const nodes = Selector('.node');

export default class Page {
  constructor () {
    this.sumiao = nodes.withText('<PERSON> Miao');
    this.tiehua = nodes.withText('<PERSON><PERSON> Hua');
    this.heihei = nodes.withText('Hei Hei');
    this.pangpang = nodes.withText('Pang Pang');
    this.dandan = nodes.withText('Dan Dan');
    this.erdan = nodes.withText('Er Dan');
    this.sandan = nodes.withText('San Dan');
    this.sidan = nodes.withText('<PERSON> Dan');
    this.wudan = nodes.withText('<PERSON> Dan');
  }
}