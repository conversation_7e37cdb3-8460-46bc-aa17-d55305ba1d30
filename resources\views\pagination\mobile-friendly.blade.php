@if ($paginator->hasPages())
    <div class="bg-white px-4 py-4 border border-gray-200 rounded-lg shadow-sm">
        <!-- Mobile-First Pagination -->
        <div class="flex items-center justify-between">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <span class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed rounded-lg">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="hidden sm:inline">पिछला</span>
                </span>
            @else
                <a href="{{ $paginator->previousPageUrl() }}" class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-lg hover:bg-gray-50 hover:text-saffron-600 focus:outline-none focus:ring-2 focus:ring-saffron-500 focus:border-saffron-500 transition ease-in-out duration-150">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="hidden sm:inline">पिछला</span>
                </a>
            @endif

            {{-- Page Information --}}
            <div class="flex items-center space-x-2">
                {{-- Mobile: Show current/total pages --}}
                <div class="sm:hidden">
                    <span class="text-sm font-medium text-gray-700">
                        {{ $paginator->currentPage() }} / {{ $paginator->lastPage() }}
                    </span>
                </div>
                
                {{-- Desktop: Show page numbers --}}
                <div class="hidden sm:flex items-center space-x-1">
                    @if($paginator->currentPage() > 3)
                        <a href="{{ $paginator->url(1) }}" class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-saffron-600 transition ease-in-out duration-150">
                            1
                        </a>
                        @if($paginator->currentPage() > 4)
                            <span class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 cursor-default">...</span>
                        @endif
                    @endif

                    @for ($i = max(1, $paginator->currentPage() - 2); $i <= min($paginator->lastPage(), $paginator->currentPage() + 2); $i++)
                        @if ($i == $paginator->currentPage())
                            <span class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-saffron-600 border border-saffron-600 cursor-default rounded">{{ $i }}</span>
                        @else
                            <a href="{{ $paginator->url($i) }}" class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-saffron-600 transition ease-in-out duration-150">{{ $i }}</a>
                        @endif
                    @endfor

                    @if($paginator->currentPage() < $paginator->lastPage() - 2)
                        @if($paginator->currentPage() < $paginator->lastPage() - 3)
                            <span class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 cursor-default">...</span>
                        @endif
                        <a href="{{ $paginator->url($paginator->lastPage()) }}" class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 hover:text-saffron-600 transition ease-in-out duration-150">
                            {{ $paginator->lastPage() }}
                        </a>
                    @endif
                </div>
            </div>

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() }}" class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-lg hover:bg-gray-50 hover:text-saffron-600 focus:outline-none focus:ring-2 focus:ring-saffron-500 focus:border-saffron-500 transition ease-in-out duration-150">
                    <span class="hidden sm:inline">अगला</span>
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </a>
            @else
                <span class="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed leading-5 rounded-lg">
                    <span class="hidden sm:inline">अगला</span>
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </span>
            @endif
        </div>

        {{-- Results Information --}}
        <div class="mt-3 text-center">
            <p class="text-xs text-gray-600">
                <span class="font-medium text-saffron-600">{{ $paginator->firstItem() }}</span>
                से
                <span class="font-medium text-saffron-600">{{ $paginator->lastItem() }}</span>
                तक दिखा रहे हैं कुल
                <span class="font-medium text-saffron-600">{{ $paginator->total() }}</span>
                परिणामों में से
            </p>
        </div>
    </div>
@endif
