@extends('layouts.app')

@section('title', 'वर्तमान सदस्यों की सूची')

@section('content')
    <div class="bg-gradient-to-b from-white to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-8">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">वर्तमान सदस्यों की सूची</h1>
                <p class="text-gray-600 max-w-2xl mx-auto">हमारे समाज के सभी सदस्यों की सूची यहां देखें</p>
            </div>

            <!-- Search and Filter -->
            <div class="max-w-7xl mx-auto mb-6">
                <div class="bg-white rounded-xl shadow-md p-4 md:p-6">
                    <form method="GET" action="{{ route('sadasya.suchana') }}" class="space-y-4">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div class="relative">
                                <input type="text" name="search" value="{{ request('search') }}"
                                       placeholder="नाम, मोबाइल, सदस्यता संख्या..."
                                       class="form-input pl-10 w-full">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                            <div>
                                <select name="membership_varg_master_id" class="form-select w-full">
                                    <option value="">सभी सदस्यता प्रकार</option>
                                    @foreach($membershipVargs as $varg)
                                        <option value="{{ $varg->id }}" {{ request('membership_varg_master_id') == $varg->id ? 'selected' : '' }}>
                                            {{ $varg->membership_varg_name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <select name="division_master_id" class="form-select w-full">
                                    <option value="">सभी संभाग</option>
                                    @foreach($divisionMasters as $division)
                                        <option value="{{ $division->id }}" {{ request('division_master_id') == $division->id ? 'selected' : '' }}>
                                            {{ $division->division_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <select name="district_master_id" class="form-select w-full">
                                    <option value="">सभी जिले</option>
                                    @foreach($districtMasters as $district)
                                        <option value="{{ $district->id }}" {{ request('district_master_id') == $district->id ? 'selected' : '' }}>
                                            {{ $district->district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <select name="vikaskhand_master_id" class="form-select w-full">
                                    <option value="">सभी विकासखंड</option>
                                    @foreach($vikaskhandMasters as $vikaskhand)
                                        <option value="{{ $vikaskhand->id }}" {{ request('vikaskhand_master_id') == $vikaskhand->id ? 'selected' : '' }}>
                                            {{ $vikaskhand->sub_district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-center gap-3">
                            <button type="submit" class="px-6 py-2 bg-navy-600 hover:bg-navy-700 text-white rounded-lg font-medium">
                                खोजें
                            </button>
                            <a href="{{ route('sadasya.suchana') }}" class="px-6 py-2 bg-gray-400 hover:bg-gray-500 text-white rounded-lg font-medium">
                                रीसेट
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Members Table -->
            <div class="hidden lg:block max-w-7xl mx-auto">
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">
                                        क्र.सं.</th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">
                                        पंजीकरण संख्या</th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">फोटो
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">नाम
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">विभाग
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">
                                        मोबाइल</th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">
                                        सदस्यता वर्ग</th>
                                    <th scope="col"
                                        class="px-6 py-4 text-left text-sm font-semibold text-navy-900 tracking-wider">
                                        स्थिति</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach ($memberships as $index => $member)
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-5 whitespace-nowrap text-base text-gray-600">{{ $index + 1 }}
                                        </td>
                                        <td class="px-6 py-5 whitespace-nowrap text-base text-gray-600">
                                            {{ $member->membership_number }}</td>
                                        <td class="px-6 py-5 whitespace-nowrap">
                                            <img src="{{ asset('storage/' . $member->photo) }}" alt="Member Photo"
                                                class="h-12 w-12 rounded-full object-cover">
                                        </td>
                                        <td class="px-6 py-5 whitespace-nowrap">
                                            <div class="text-base font-medium text-navy-900">{{ $member->name }}</div>
                                            <div class="text-sm text-gray-500 mt-1">पिता:
                                                {{ $member->fathers_husband_name }}</div>
                                        </td>
                                        <td class="px-6 py-5 whitespace-nowrap text-base text-gray-600">
                                            {{ $member->department_name }}</td>
                                        <td class="px-6 py-5 whitespace-nowrap text-base text-gray-600">
                                            {{ $member->mobile_number }}</td>
                                        <td class="px-6 py-5 whitespace-nowrap text-base text-gray-600">
                                            @if($member->membershipVarg)
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                                                    {{ $member->membershipVarg->name }}
                                                </span>
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-5 whitespace-nowrap">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $member->getStatusBadgeClass() }}">
                                                {{ $member->getStatusDisplayText() }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Mobile Cards (shown only on mobile and tablet) -->
            <div class="lg:hidden space-y-4">
                @forelse ($memberships as $index => $member)
                    <div class="bg-white rounded-xl shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow duration-200">
                        <div class="flex items-start space-x-4">
                            <!-- Member Photo -->
                            <div class="flex-shrink-0">
                                <img src="{{ asset('storage/' . $member->photo) }}" alt="{{ $member->name }}"
                                    class="h-16 w-16 rounded-full object-cover border-2 border-saffron-200 shadow-sm">
                            </div>

                            <!-- Member Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex justify-between items-start mb-2">
                                    <div class="flex-1">
                                        <h3 class="text-lg font-semibold text-navy-900 truncate">{{ $member->name }}</h3>
                                        <p class="text-sm text-gray-600 truncate">पिता: {{ $member->fathers_husband_name }}</p>
                                    </div>
                                    @if($member->membershipVarg)
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                            {{ $member->membershipVarg->name }}
                                        </span>
                                    @endif
                                </div>

                                <!-- Member Details Grid -->
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                                    <div class="flex justify-between sm:block">
                                        <span class="text-gray-500">पंजीकरण संख्या:</span>
                                        <span class="font-medium text-navy-900">{{ $member->membership_number }}</span>
                                    </div>
                                    <div class="flex justify-between sm:block">
                                        <span class="text-gray-500">विभाग:</span>
                                        <span class="font-medium text-navy-900 truncate">{{ $member->department_name }}</span>
                                    </div>
                                    <div class="flex justify-between sm:block">
                                        <span class="text-gray-500">सदस्यता वर्ग:</span>
                                        <span class="font-medium text-navy-900">
                                            @if($member->membershipVarg)
                                                {{ $member->membershipVarg->name }}
                                            @else
                                                -
                                            @endif
                                        </span>
                                    </div>
                                    <div class="flex justify-between sm:block">
                                        <span class="text-gray-500">मोबाइल:</span>
                                        <span class="font-medium text-navy-900">{{ $member->mobile_number }}</span>
                                    </div>
                                    <div class="flex justify-between sm:block">
                                        <span class="text-gray-500">स्थिति:</span>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $member->getStatusBadgeClass() }}">
                                            {{ $member->getStatusDisplayText() }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <div class="text-gray-500 text-lg mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            कोई सदस्य नहीं मिला
                        </div>
                        <p class="text-gray-400">खोज मापदंड बदलकर पुनः प्रयास करें।</p>
                    </div>
                @endforelse
            </div>


            <!-- Mobile-Friendly Pagination -->
            @if ($memberships->hasPages())
                <div class="mt-8 max-w-7xl mx-auto">
                    {{ $memberships->links('pagination.mobile-friendly') }}
                </div>
            @endif
        </div>
    </div>

    @push('styles')
        <style>
            .form-input {
                @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-saffron-500 focus:border-saffron-500 bg-white shadow-md;
            }

            .form-select {
                @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-saffron-500 focus:border-saffron-500 bg-white shadow-md appearance-none;
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.5rem center;
                background-repeat: no-repeat;
                background-size: 1.5em 1.5em;
                padding-right: 2.5rem;
            }

            .shadow-md {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }

            .shadow-sm {
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const searchInput = document.getElementById('search');
                const filterSelect = document.getElementById('filter');
                const desktopTable = document.querySelector('.lg\\:block table tbody');
                const mobileCards = document.querySelector('.lg\\:hidden');

                // Store original data
                let originalDesktopRows = [];
                let originalMobileCards = [];

                if (desktopTable) {
                    originalDesktopRows = Array.from(desktopTable.querySelectorAll('tr'));
                }

                if (mobileCards) {
                    originalMobileCards = Array.from(mobileCards.querySelectorAll('.bg-white'));
                }

                function filterContent() {
                    const searchTerm = searchInput.value.toLowerCase().trim();
                    const filterValue = filterSelect.value;

                    // Filter desktop table
                    if (desktopTable && originalDesktopRows.length > 0) {
                        originalDesktopRows.forEach(row => {
                            const name = row.querySelector('td:nth-child(4) .text-base')?.textContent.toLowerCase() || '';
                            const membershipNumber = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
                            const membershipType = row.querySelector('td:nth-child(7) span')?.textContent.toLowerCase() || '';

                            const matchesSearch = !searchTerm ||
                                name.includes(searchTerm) ||
                                membershipNumber.includes(searchTerm);

                            const matchesFilter = !filterValue ||
                                (filterValue === 'patron' && membershipType.includes('संरक्षक')) ||
                                (filterValue === 'lifetime' && membershipType.includes('आजीवन')) ||
                                (filterValue === 'regular' && membershipType.includes('वार्षिक'));

                            if (matchesSearch && matchesFilter) {
                                row.style.display = '';
                            } else {
                                row.style.display = 'none';
                            }
                        });
                    }

                    // Filter mobile cards
                    if (mobileCards && originalMobileCards.length > 0) {
                        originalMobileCards.forEach(card => {
                            const name = card.querySelector('h3')?.textContent.toLowerCase() || '';
                            const membershipNumber = card.querySelector('.grid .font-medium')?.textContent.toLowerCase() || '';
                            const membershipType = card.querySelector('.rounded-full')?.textContent.toLowerCase() || '';

                            const matchesSearch = !searchTerm ||
                                name.includes(searchTerm) ||
                                membershipNumber.includes(searchTerm);

                            const matchesFilter = !filterValue ||
                                (filterValue === 'patron' && membershipType.includes('संरक्षक')) ||
                                (filterValue === 'lifetime' && membershipType.includes('आजीवन')) ||
                                (filterValue === 'regular' && membershipType.includes('वार्षिक'));

                            if (matchesSearch && matchesFilter) {
                                card.style.display = '';
                            } else {
                                card.style.display = 'none';
                            }
                        });
                    }
                }

                // Add event listeners
                searchInput.addEventListener('input', filterContent);
                filterSelect.addEventListener('change', filterContent);

                // Add loading states
                searchInput.addEventListener('input', function() {
                    this.style.backgroundColor = '#f9fafb';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 200);
                });
            });
        </script>
    @endpush
@endsection
