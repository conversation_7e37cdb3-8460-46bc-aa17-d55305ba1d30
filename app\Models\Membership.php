<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\Hash;

class Membership extends Authenticatable
{
    use HasFactory;

    protected $table = 'memberships';

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'name', 'fathers_husband_name', 'mobile_number', 'birth_date',
        'marital_status', 'vivah_tithi', 'spouse_name', 'spouse_education',
        'spouse_education_details', 'spouse_work_details', 'family_members', 'education',
        'course_stream_name', 'caste_details', 'vibhagiy_padnaam',
        'department_name', 'office', 'karyalay_ka_pata', 'vartaman_pata',
        'isthayi_pata', 'address', 'membership_type', 'member_name_signature',
        'mobile', 'proposer_address', 'declaration', 'photo', 'signature',
        'children', 'ancestors', 'status', 'rejection_reason', 'vibhag',
        'is_active', 'email', 'password', 'membership_varg_id',
        'education_qualification_id', 'office_master_id', 'department_master_id',
        'yadav_varg_id', 'division_master_id', 'district_master_id',
        'vikaskhand_master_id'
    ];

    protected $casts = [
        'children' => 'array',
        'ancestors' => 'array',
        'declaration' => 'boolean',
        'is_active' => 'boolean',
        'birth_date' => 'date',
        'vivah_tithi' => 'date',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the name of the unique identifier for the user.
     */
    public function getAuthIdentifierName()
    {
        return 'id';
    }

    /**
     * Get the unique identifier for the user.
     */
    public function getAuthIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Get the password for the user.
     */
    public function getAuthPassword()
    {
        return $this->password;
    }

    /**
     * Get the token value for the "remember me" session.
     */
    public function getRememberToken()
    {
        return $this->remember_token;
    }

    /**
     * Set the token value for the "remember me" session.
     */
    public function setRememberToken($value)
    {
        $this->remember_token = $value;
    }

    /**
     * Get the column name for the "remember me" token.
     */
    public function getRememberTokenName()
    {
        return 'remember_token';
    }

    /**
     * Set password attribute with hashing
     */
    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = Hash::make($value);
        }
    }


    /**
     * Boot method to auto-generate membership number on creation.
     */
    protected static function booted()
    {
        static::creating(function ($membership) {
            $latestId = static::max('id') ?? 0;
            $membership->membership_number = 'CGYS' . str_pad($latestId + 1, 6, '0', STR_PAD_LEFT);

            // Set default status if not provided
            if (empty($membership->status)) {
                $membership->status = self::STATUS_PENDING;
            }
        });
    }

    /**
     * Get all status options
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
        ];
    }

    /**
     * Check if membership is pending
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if membership is approved
     */
    public function isApproved()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if membership is rejected
     */
    public function isRejected()
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Get status badge class for UI
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_APPROVED => 'bg-green-100 text-green-800',
            self::STATUS_REJECTED => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get status display text
     */
    public function getStatusDisplayText()
    {
        return match($this->status) {
            self::STATUS_PENDING => 'लंबित',
            self::STATUS_APPROVED => 'स्वीकृत',
            self::STATUS_REJECTED => 'अस्वीकृत',
            default => 'अज्ञात',
        };
    }

    /**
     * Get the membership varg that this membership belongs to.
     */
    public function membershipVarg()
    {
        return $this->belongsTo(MembershipVargMaster::class, 'membership_varg_id');
    }

    /**
     * Get the education qualification for this membership.
     */
    public function educationQualification()
    {
        return $this->belongsTo(EducationQualification::class, 'education_qualification_id');
    }

    /**
     * Get the office master for this membership.
     */
    public function officeMaster()
    {
        return $this->belongsTo(OfficeMaster::class, 'office_master_id');
    }

    /**
     * Get the department master for this membership.
     */
    public function departmentMaster()
    {
        return $this->belongsTo(DepartmentMaster::class, 'department_master_id');
    }

    /**
     * Get the yadav varg for this membership.
     */
    public function yadavVarg()
    {
        return $this->belongsTo(YadavVarg::class, 'yadav_varg_id');
    }

    /**
     * Relationship with DivisionMaster
     */
    public function divisionMaster()
    {
        return $this->belongsTo(DivisionMaster::class, 'division_master_id');
    }

    /**
     * Relationship with DistrictMaster
     */
    public function districtMaster()
    {
        return $this->belongsTo(DistrictMaster::class, 'district_master_id');
    }

    /**
     * Relationship with VikaskhandMaster
     */
    public function vikaskhandMaster()
    {
        return $this->belongsTo(VikaskhandMaster::class, 'vikaskhand_master_id');
    }
}
