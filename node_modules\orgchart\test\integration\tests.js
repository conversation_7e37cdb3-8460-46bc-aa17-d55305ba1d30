var chai = require('chai');
var sinon = require('sinon');
var sinonChai = require('sinon-chai');
var should = chai.should();
chai.use(sinonChai);
require('jsdom-global')();
var $ = require('jquery');
require('../../src/js/jquery.orgchart');

describe('orgchart -- integration tests', function () {
  document.body.innerHTML = '<div id="chart-container"></div>';
  var $container = $('#chart-container'),
  ds = {
    'id': 'n1',
    'name': 'Lao Lao',
    'children': [
      { 'id': 'n2', 'name': '<PERSON>' },
      { 'id': 'n3', 'name': '<PERSON>',
        'children': [
          { 'id': 'n5', 'name': '<PERSON><PERSON><PERSON>',
            'children' : [
              { 'id': 'n8', 'name': 'Dan Dan' }
            ]
          },
          { 'id': 'n6', 'name': '<PERSON><PERSON> <PERSON><PERSON>',
            'children': [
              { 'id': 'n9', 'name': '<PERSON>r <PERSON>' }
            ]
          },
          { 'id': 'n7', 'name': '<PERSON><PERSON>',
            'children': [
              { 'id': 'n10', 'name': '<PERSON> Dan' }
            ]
          }
        ]
      },
      { 'id': 'n4', 'name': 'Hong Miao' },
    ]
  },
  oc = {},
  hierarchy = {
    id: 'n1',
    children: [
      { id: 'n2' },
      { id: 'n3',
        children: [
          { id: 'n5',
            children: [
              { id: 'n8' }
            ]
          },
          { id: 'n6',
            children: [
              { id: 'n9'}
            ]
          },
          { id: 'n7',
            children: [
              { id: 'n10' }
            ]
          }
        ]
      },
      { id: 'n4' }
    ]
  },
  $laolao,
  $bomiao,
  $sumiao,
  $hongmiao,
  $chunmiao,
  $tiehua,
  $heihei,
  $pangpang,
  $dandan,
  $erdan;

  beforeEach(function () {
    oc = $container.orgchart({
      'data': ds
    }),
    $laolao = $('#n1'),
    $bomiao = $('#n2'),
    $sumiao = $('#n3'),
    $hongmiao = $('#n4'),
    $tiehua = $('#n5'),
    $heihei = $('#n6'),
    $pangpang = $('#n7'),
    $dandan = $('#n8'),
    $erdan = $('#n9'),
    $sandan = $('#n10');
  });
    
  afterEach(function () {
    $laolao = $bomiao = $sumiao = $hongmiao = $chunmiao = $tiehua = $heihei = $pangpang = $dandan = $erdan = $sandan = null;
    $container.empty();
  });

  it('addParent()', function () {
    oc.addParent($laolao, { 'name': 'Lao Ye', 'id': 'n0' });
    $laolao.closest('.nodes').siblings('.node').should.lengthOf(1);
    oc.$chart.find('.node:first').should.deep.equal($('#n0'));
  });

  describe('addChildren()', function () {
    it('Add child nodes to the leaf node', function () {
      oc.addChildren($bomiao, [{'name': 'Li Xin', 'id': 'n11' }]);
      $bomiao.siblings('.nodes').should.lengthOf(1);
      $bomiao.siblings('.nodes').find('.hierarchy').should.lengthOf(1);
      $bomiao.siblings('.nodes').find('.node').attr('id').should.equal('n11');
    });

    it('Add child nodes to the un-leaf node', function () {
      oc.addChildren($sumiao, [{'name': 'Li Xin', 'id': 'n11' }]);
      $sumiao.siblings('.nodes').children('.hierarchy').should.lengthOf(4);
      $sumiao.siblings('.nodes').children('.hierarchy:last').find('.node').attr('id').should.equal('n11');
    });
  });

  describe('addSiblings()', function () {
    it('Just add sibling nodes', function () {
      oc.addSiblings($sumiao, [{'name': 'Li Xin', 'id': 'n11' }]);
      $laolao.siblings('.nodes').children('.hierarchy').should.lengthOf(4);
      $laolao.siblings('.nodes').children('.hierarchy:last').find('.node').attr('id').should.equal('n11');
    });

    it('Add sibling nodes as well as parent node', function () {
      oc.addSiblings($laolao, { 'name': 'Lao Ye', 'id': 'n0', 'children': [{'name': 'Li Xin', 'id': 'n11' }] });
      $laolao.closest('.nodes').siblings('.node').should.lengthOf(1);
      oc.$chart.find('.node:first').should.deep.equal($('#n0'));
      $laolao.closest('.hierarchy').siblings().should.lengthOf(1);
      $laolao.closest('.hierarchy').siblings().find('.node').attr('id').should.equal('n11');
    });
  });

  describe('removeNodes()', function () {
    it('Remove leaf node', function () {
      oc.removeNodes($dandan);
      $tiehua.siblings('.nodes').should.lengthOf(0);
    });
    it('Remove parent node', function () {
      oc.removeNodes($tiehua);
      $sumiao.siblings('.nodes').children('.hierarchy').should.lengthOf(2);
      $('#n5').should.lengthOf(0);
      $('#n8').should.lengthOf(0);
    });
    it('Remove root node', function () {
      oc.removeNodes($laolao);
      oc.$chartContainer.is(':empty').should.be.true;
    });
  });

});