@extends('layouts.admin') @section('title', 'नया प्रमुख पदाधिकारी जोड़ें') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">नया प्रमुख पदाधिकारी जोड़ें</h1> <p class="mt-1 text-sm admin-text-secondary">नया प्रमुख पदाधिकारी बनाएं</p> </div> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg> वापस जाएं </a> </div> </div> <!-- Error Messages --> @if($errors->any()) <div class="admin-card p-4 mb-6 bg-red-50 border border-red-200"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <h3 class="text-sm font-medium text-red-800">कृपया निम्नलिखित त्रुटियों को ठीक करें:</h3> <div class="mt-2 text-sm text-red-700"> <ul class="list-disc list-inside space-y-1"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> </div> </div> </div> @endif @if(session('error')) <div class="admin-card p-4 mb-6 bg-red-50 border border-red-200"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <p class="text-sm font-medium text-red-800">{{ session('error') }}</p> </div> </div> </div> @endif <!-- Form --> <div class="admin-card p-6"> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.store') }}" enctype="multipart/form-data" class="space-y-6"> @csrf <!-- Location Selection Section --> <div class="border-b border-gray-200 pb-6"> <h3 class="text-lg font-medium admin-text-primary mb-4">स्थान चयन</h3> <div class="grid grid-cols-1 md:grid-cols-4 gap-6"> <!-- State --> <div> <label for="state_name" class="block text-sm font-medium admin-text-primary mb-2"> राज्य <span class="text-red-500">*</span> </label> <input type="text" id="state_name" name="state_name" value="{{ old('state_name', 'छत्तीसगढ़') }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('state_name') border-red-500 @enderror" placeholder="राज्य का नाम"> @error('state_name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Division --> <div> <label for="division_code" class="block text-sm font-medium admin-text-primary mb-2"> संभाग </label> <select id="division_code" name="division_code" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('division_code') border-red-500 @enderror"> <option value="">संभाग चुनें</option> @foreach($divisions as $division) <option value="{{ $division->division_code }}" {{ old('division_code') === $division->division_code ? 'selected' : '' }}> {{ $division->division_name_hin }} ({{ $division->division_name_eng }}) </option> @endforeach </select> @error('division_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- District --> <div> <label for="district_lgd_code" class="block text-sm font-medium admin-text-primary mb-2"> जिला </label> <select id="district_lgd_code" name="district_lgd_code" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('district_lgd_code') border-red-500 @enderror" disabled> <option value="">पहले संभाग चुनें</option> </select> @error('district_lgd_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Vikaskhand --> <div> <label for="vikaskhand_lgd_code" class="block text-sm font-medium admin-text-primary mb-2"> विकासखंड </label> <select id="vikaskhand_lgd_code" name="vikaskhand_lgd_code" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('vikaskhand_lgd_code') border-red-500 @enderror" disabled> <option value="">पहले जिला चुनें</option> </select> @error('vikaskhand_lgd_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> </div> <!-- Personal Information Section --> <div class="border-b border-gray-200 pb-6"> <h3 class="text-lg font-medium admin-text-primary mb-4">व्यक्तिगत जानकारी</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Naam --> <div> <label for="naam" class="block text-sm font-medium admin-text-primary mb-2"> नाम <span class="text-red-500">*</span> </label> <input type="text" id="naam" name="naam" value="{{ old('naam') }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('naam') border-red-500 @enderror" placeholder="पूरा नाम दर्ज करें"> @error('naam') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Mobile Number --> <div> <label for="mobile_number" class="block text-sm font-medium admin-text-primary mb-2"> मोबाइल नंबर <span class="text-red-500">*</span> </label> <input type="tel" id="mobile_number" name="mobile_number" value="{{ old('mobile_number') }}" required pattern="[0-9]{10}" maxlength="10" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('mobile_number') border-red-500 @enderror" placeholder="10 अंकों का मोबाइल नंबर"> @error('mobile_number') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Sangathan me Padnaam --> <div> <label for="sangathan_me_padnaam" class="block text-sm font-medium admin-text-primary mb-2"> संगठन में पदनाम <span class="text-red-500">*</span> </label> <input type="text" id="sangathan_me_padnaam" name="sangathan_me_padnaam" value="{{ old('sangathan_me_padnaam') }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sangathan_me_padnaam') border-red-500 @enderror" placeholder="जैसे: अध्यक्ष, सचिव, कोषाध्यक्ष"> @error('sangathan_me_padnaam') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Photo Upload --> <div> <label for="photo" class="block text-sm font-medium admin-text-primary mb-2"> फोटो </label> <input type="file" id="photo" name="photo" accept="image/jpeg,image/png,image/jpg" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('photo') border-red-500 @enderror"> <p class="mt-1 text-sm admin-text-secondary">JPG, PNG या JPEG अपलोड करें। अधिकतम आकार: 2MB</p> @error('photo') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> </div> <!-- Office Information Section --> <div class="border-b border-gray-200 pb-6"> <h3 class="text-lg font-medium admin-text-primary mb-4">कार्यालयीन जानकारी</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Vibhagiy Padnaam --> <div> <label for="vibhagiy_padnaam" class="block text-sm font-medium admin-text-primary mb-2"> विभागीय पदनाम </label> <input type="text" id="vibhagiy_padnaam" name="vibhagiy_padnaam" value="{{ old('vibhagiy_padnaam') }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('vibhagiy_padnaam') border-red-500 @enderror" placeholder="विभागीय पदनाम दर्ज करें"> @error('vibhagiy_padnaam') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Vibhag ka Naam --> <div> <label for="vibhag_ka_naam" class="block text-sm font-medium admin-text-primary mb-2"> विभाग का नाम </label> <input type="text" id="vibhag_ka_naam" name="vibhag_ka_naam" value="{{ old('vibhag_ka_naam') }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('vibhag_ka_naam') border-red-500 @enderror" placeholder="विभाग/कार्यालय का नाम दर्ज करें"> @error('vibhag_ka_naam') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> </div> <!-- Address Information Section --> <div class="border-b border-gray-200 pb-6"> <h3 class="text-lg font-medium admin-text-primary mb-4">पता जानकारी</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Vartaman Pata --> <div> <label for="vartaman_pata" class="block text-sm font-medium admin-text-primary mb-2"> वर्तमान पता <span class="text-red-500">*</span> </label> <textarea id="vartaman_pata" name="vartaman_pata" rows="3" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('vartaman_pata') border-red-500 @enderror" placeholder="वर्तमान पूरा पता दर्ज करें">{{ old('vartaman_pata') }}</textarea> @error('vartaman_pata') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Isthayi Pata --> <div> <label for="isthayi_pata" class="block text-sm font-medium admin-text-primary mb-2"> स्थायी पता </label> <textarea id="isthayi_pata" name="isthayi_pata" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('isthayi_pata') border-red-500 @enderror" placeholder="स्थायी पूरा पता दर्ज करें">{{ old('isthayi_pata') }}</textarea> @error('isthayi_pata') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> </div> <!-- Additional Information Section --> <div class="border-b border-gray-200 pb-6"> <h3 class="text-lg font-medium admin-text-primary mb-4">अतिरिक्त जानकारी</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Sanchipt Vishesh --> <div> <label for="sanchipt_vishesh" class="block text-sm font-medium admin-text-primary mb-2"> संक्षिप्त विशेष </label> <textarea id="sanchipt_vishesh" name="sanchipt_vishesh" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sanchipt_vishesh') border-red-500 @enderror" placeholder="संक्षिप्त विशेष जानकारी दर्ज करें">{{ old('sanchipt_vishesh') }}</textarea> @error('sanchipt_vishesh') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Sadasyata Kramank evam Prakar --> <div> <label for="sadasyata_kramank_evam_prakar" class="block text-sm font-medium admin-text-primary mb-2"> सदस्यता क्रमांक एवं प्रकार </label> <input type="text" id="sadasyata_kramank_evam_prakar" name="sadasyata_kramank_evam_prakar" value="{{ old('sadasyata_kramank_evam_prakar') }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('sadasyata_kramank_evam_prakar') border-red-500 @enderror" placeholder="सदस्यता क्रमांक एवं प्रकार दर्ज करें"> @error('sadasyata_kramank_evam_prakar') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Remark --> <div class="md:col-span-2"> <label for="remark" class="block text-sm font-medium admin-text-primary mb-2"> टिप्पणी </label> <textarea id="remark" name="remark" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('remark') border-red-500 @enderror" placeholder="कोई अतिरिक्त टिप्पणी दर्ज करें">{{ old('remark') }}</textarea> @error('remark') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> </div> <!-- Status Section --> <div> <h3 class="text-lg font-medium admin-text-primary mb-4">स्थिति</h3> <div class="flex items-center"> <input type="checkbox" id="active" name="active" value="1" {{ old('active', true) ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"> <label for="active" class="ml-2 block text-sm admin-text-primary"> सक्रिय स्थिति (निष्क्रिय करने के लिए अनचेक करें) </label> </div> @error('active') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Submit Buttons --> <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200"> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50:bg-navy-700 font-medium transition-colors"> रद्द करें </a> <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> प्रमुख पदाधिकारी जोड़ें </button> </div> </form> </div> </div> </div> @push('scripts') <script> document.addEventListener('DOMContentLoaded', function() { // Mobile number validation const mobileInput = document.getElementById('mobile_number'); mobileInput.addEventListener('input', function(e) { // Remove any non-digit characters let value = e.target.value.replace(/\D/g, ''); // Limit to 10 digits if (value.length > 10) { value = value.slice(0, 10); } e.target.value = value; }); // Photo validation const photoInput = document.getElementById('photo'); photoInput.addEventListener('change', function(e) { const file = e.target.files[0]; if (file) { // Validate file type const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']; if (!allowedTypes.includes(file.type)) { alert('कृपया एक वैध इमेज फाइल चुनें (JPG, PNG, या JPEG)।'); e.target.value = ''; return; } // Validate file size (2MB) if (file.size > 2 * 1024 * 1024) { alert('फाइल का आकार 2MB से कम होना चाहिए।'); e.target.value = ''; return; } } }); // Nested dropdown functionality const divisionSelect = document.getElementById('division_code'); const districtSelect = document.getElementById('district_lgd_code'); const vikaskhandSelect = document.getElementById('vikaskhand_lgd_code'); // Division change handler divisionSelect.addEventListener('change', function() { const divisionCode = this.value; // Reset district and vikaskhand dropdowns districtSelect.innerHTML = '<option value="">जिला चुनें</option>'; vikaskhandSelect.innerHTML = '<option value="">पहले जिला चुनें</option>'; districtSelect.disabled = !divisionCode; vikaskhandSelect.disabled = true; if (divisionCode) { // Fetch districts for selected division fetch(`{{ route('admin.pramukh-padadhikaris.ajax.districts-by-division') }}?division_code=${divisionCode}`) .then(response => response.json()) .then(districts => { districts.forEach(district => { const option = document.createElement('option'); option.value = district.district_lgd_code; option.textContent = `${district.district_name_hin} (${district.district_name_eng})`; districtSelect.appendChild(option); }); }) .catch(error => { console.error('Error fetching districts:', error); alert('जिले लोड करने में त्रुटि हुई।'); }); } }); // District change handler districtSelect.addEventListener('change', function() { const districtLgdCode = this.value; // Reset vikaskhand dropdown vikaskhandSelect.innerHTML = '<option value="">विकासखंड चुनें</option>'; vikaskhandSelect.disabled = !districtLgdCode; if (districtLgdCode) { // Fetch vikaskhands for selected district fetch(`{{ route('admin.pramukh-padadhikaris.ajax.vikaskhands-by-district') }}?district_lgd_code=${districtLgdCode}`) .then(response => response.json()) .then(vikaskhands => { vikaskhands.forEach(vikaskhand => { const option = document.createElement('option'); option.value = vikaskhand.sub_district_lgd_code; option.textContent = `${vikaskhand.sub_district_name_hin} (${vikaskhand.sub_district_name_eng})`; vikaskhandSelect.appendChild(option); }); }) .catch(error => { console.error('Error fetching vikaskhands:', error); alert('विकासखंड लोड करने में त्रुटि हुई।'); }); } }); // Form validation const form = document.querySelector('form'); form.addEventListener('submit', function(e) { const mobileNumber = mobileInput.value; if (mobileNumber.length !== 10) { e.preventDefault(); alert('कृपया 10 अंकों का वैध मोबाइल नंबर दर्ज करें।'); mobileInput.focus(); return false; } }); }); </script> @endpush @endsection 