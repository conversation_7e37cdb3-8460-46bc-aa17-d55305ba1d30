<?php

namespace Database\Seeders;

use App\Models\Activity;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class ActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample locations
        $locations = [
            'Community Hall, Delhi',
            'Malviya Nagar, New Delhi',
            'Vidya Bhawan, Mumbai',
            'Cultural Center, Jaipur',
            'Manav <PERSON>, Lucknow',
            'Gandhi Bhawan, Ahmedabad',
        ];

        // 1. Social Activities
        $this->createActivities(Activity::CATEGORY_SOCIAL, [
            [
                'title' => 'Annual Blood Donation Camp',
                'description' => '<h3>Blood Donation Drive</h3><p>Our organization conducted its annual blood donation camp in collaboration with the Red Cross Society. Over 150 community members participated, donating blood to help save lives. The event included free health check-ups and awareness sessions about the importance of regular blood donation.</p><p>Special thanks to our volunteers and medical professionals who made this event possible.</p>',
                'event_date' => Carbon::now()->subMonths(2),
                'location' => $locations[0],
                'display_order' => 1,
            ],
            [
                'title' => 'Food Distribution Drive',
                'description' => '<h3>Feeding the Community</h3><p>Our members organized a food distribution drive for underprivileged families in the neighborhood. We distributed food packets, essential groceries, and hygiene products to over 200 families. The initiative was supported by local businesses and community members.</p><p>This ongoing monthly program aims to address food insecurity in our community.</p>',
                'event_date' => Carbon::now()->subWeeks(3),
                'location' => $locations[1],
                'display_order' => 2,
            ],
            [
                'title' => 'Community Health Camp',
                'description' => '<h3>Promoting Community Health</h3><p>A free health check-up camp was organized for community members with focus on preventive healthcare. Medical professionals volunteered to provide services including general health check-ups, eye examinations, dental care, and nutritional guidance.</p><p>The camp benefited more than 300 community members, many of whom have limited access to healthcare services.</p>',
                'event_date' => Carbon::now()->subWeeks(6),
                'location' => $locations[2],
                'display_order' => 3,
            ]
        ]);

        // 2. Cultural Activities
        $this->createActivities(Activity::CATEGORY_CULTURAL, [
            [
                'title' => 'Annual Cultural Festival',
                'description' => '<h3>Celebrating Our Heritage</h3><p>Our annual cultural festival showcased the rich cultural heritage of our community. The event featured traditional dance performances, music recitals, and art exhibitions. Community members of all ages participated, making it a vibrant celebration of our traditions.</p><p>The festival attracted over 500 visitors and helped promote cultural awareness among younger generations.</p>',
                'event_date' => Carbon::now()->subMonths(1),
                'location' => $locations[3],
                'display_order' => 1,
            ],
            [
                'title' => 'Folk Music Workshop',
                'description' => '<h3>Learning Traditional Music</h3><p>A three-day workshop on traditional folk music was organized for community youth. Renowned folk musicians conducted sessions on various musical instruments, vocal techniques, and the historical significance of our folk music.</p><p>The workshop concluded with a public performance by participants, showcasing the skills they acquired.</p>',
                'event_date' => Carbon::now()->subWeeks(5),
                'location' => $locations[4],
                'display_order' => 2,
            ],
            [
                'title' => 'Traditional Art Exhibition',
                'description' => '<h3>Showcasing Artistic Heritage</h3><p>An exhibition highlighting traditional art forms was organized at the community center. Local artists displayed their works including paintings, sculptures, and handicrafts. The exhibition aimed to preserve and promote our artistic traditions.</p><p>Special workshops were conducted for school children to introduce them to these art forms.</p>',
                'event_date' => Carbon::now()->subWeeks(8),
                'location' => $locations[0],
                'display_order' => 3,
            ]
        ]);

        // 3. Educational Activities
        $this->createActivities(Activity::CATEGORY_EDUCATIONAL, [
            [
                'title' => 'Career Guidance Workshop',
                'description' => '<h3>Planning for the Future</h3><p>A career guidance workshop was organized for high school students from our community. Professional counselors provided information about various career paths, educational requirements, and scholarship opportunities.</p><p>The workshop helped students make informed decisions about their academic and professional futures.</p>',
                'event_date' => Carbon::now()->subWeeks(2),
                'location' => $locations[2],
                'display_order' => 1,
            ],
            [
                'title' => 'Community Library Initiative',
                'description' => '<h3>Promoting Literacy</h3><p>Our organization established a community library with a collection of books across various subjects and age groups. The library provides a quiet study space and hosts regular reading sessions for children.</p><p>The initiative aims to promote reading habits and improve literacy rates in our community.</p>',
                'event_date' => Carbon::now()->subMonths(3),
                'location' => $locations[1],
                'display_order' => 2,
            ],
            [
                'title' => 'Computer Literacy Program',
                'description' => '<h3>Digital Empowerment</h3><p>A six-week computer literacy program was conducted for community members with limited or no computer skills. The course covered basic computer operations, internet usage, email, and introduction to office applications.</p><p>The program aimed to bridge the digital divide and enhance employment opportunities for participants.</p>',
                'event_date' => Carbon::now()->subWeeks(10),
                'location' => $locations[5],
                'display_order' => 3,
            ]
        ]);

        // 4. Government Initiatives
        $this->createActivities(Activity::CATEGORY_GOVERNMENT, [
            [
                'title' => 'Voter Awareness Campaign',
                'description' => '<h3>Promoting Democratic Participation</h3><p>In collaboration with local authorities, our organization conducted a voter awareness campaign ahead of the upcoming elections. The campaign included information sessions about voter registration, voting procedures, and the importance of informed voting.</p><p>Special emphasis was placed on encouraging first-time voters to participate in the democratic process.</p>',
                'event_date' => Carbon::now()->subWeeks(4),
                'location' => $locations[3],
                'display_order' => 1,
            ],
            [
                'title' => 'Public Health Initiative',
                'description' => '<h3>Preventive Healthcare</h3><p>Our organization partnered with government health departments to implement a public health initiative focusing on preventive healthcare. The program included vaccination drives, health education sessions, and distribution of informational materials.</p><p>The initiative reached over 500 families in underserved areas.</p>',
                'event_date' => Carbon::now()->subMonths(2)->addWeeks(1),
                'location' => $locations[4],
                'display_order' => 2,
            ],
            [
                'title' => 'Digital Governance Workshop',
                'description' => '<h3>Understanding Online Government Services</h3><p>A workshop was conducted to educate community members about various online government services and how to access them. The session covered topics such as digital document submission, online bill payments, and accessing public records.</p><p>Participants received hands-on training in navigating government portals and mobile applications.</p>',
                'event_date' => Carbon::now()->subWeeks(7),
                'location' => $locations[0],
                'display_order' => 3,
            ]
        ]);

        // 5. Gallery Items
        $this->createActivities(Activity::CATEGORY_GALLERY, [
            [
                'title' => 'Community Celebration Photos',
                'description' => '<h3>Annual Festival Highlights</h3><p>A collection of photographs from our annual community festival showcasing cultural performances, community gatherings, and celebratory moments that define our community spirit.</p>',
                'event_date' => Carbon::now()->subMonths(1)->addDays(5),
                'location' => $locations[3],
                'display_order' => 1,
            ],
            [
                'title' => 'Service Project Documentation',
                'description' => '<h3>Making a Difference</h3><p>Visual documentation of our community service projects including before and after images of community cleanups, renovation projects, and environmental initiatives undertaken by our volunteers.</p>',
                'event_date' => Carbon::now()->subWeeks(9),
                'location' => $locations[1],
                'display_order' => 2,
            ],
            [
                'title' => 'Historical Archives',
                'description' => '<h3>Preserving Our History</h3><p>A digitized collection of historical photographs, documents, and artifacts that chronicle the evolution of our community over the decades. This archive serves as an educational resource and preserves our collective heritage.</p>',
                'event_date' => Carbon::now()->subMonths(4),
                'location' => $locations[5],
                'display_order' => 3,
            ]
        ]);
    }

    /**
     * Helper method to create activities for a specific category
     */
    private function createActivities(string $category, array $activitiesData): void
    {
        foreach ($activitiesData as $index => $data) {
            Activity::create([
                'category' => $category,
                'title' => $data['title'],
                'description' => $data['description'],
                'event_date' => $data['event_date'],
                'location' => $data['location'],
                'display_order' => $data['display_order'],
                'is_published' => true,
            ]);
        }
    }
}
