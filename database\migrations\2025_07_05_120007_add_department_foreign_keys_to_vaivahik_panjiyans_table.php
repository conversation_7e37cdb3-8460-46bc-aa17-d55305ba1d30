<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Add foreign key column (assuming there's a department field in vaivahik_panjiyans)
            $table->unsignedBigInteger('department_master_id')->nullable()->after('vartaman_karya');

            // Add foreign key constraint
            $table->foreign('department_master_id')->references('id')->on('department_masters')->onDelete('set null');

            // Add index for better performance
            $table->index('department_master_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vaivahik_panjiyans', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['department_master_id']);

            // Drop the column
            $table->dropColumn('department_master_id');
        });
    }
};
