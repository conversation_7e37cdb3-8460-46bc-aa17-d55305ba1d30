// Initialize family tree when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait for jQuery to be available
    if (typeof $ !== 'undefined') {
        initializeFamilyTree();
    } else {
        // Retry after a short delay if jQuery is not yet loaded
        setTimeout(function() {
            if (typeof $ !== 'undefined') {
                initializeFamilyTree();
            }
        }, 500);
    }
});

function initializeFamilyTree() {
    // Get family tree data from the page
    const familyTreeData = window.familyTreeData || {};

    if (!familyTreeData.nodeDataArray || familyTreeData.nodeDataArray.length === 0) {
        document.getElementById('family-tree-chart').innerHTML =
            '<div class="text-center py-8 text-gray-500">कोई पारिवारिक डेटा उपलब्ध नहीं है</div>';
        return;
    }

    // Convert data to jQuery OrgChart format
    const chartData = convertToOrgChartFormat(familyTreeData.nodeDataArray);

    // Initialize jQuery OrgChart
    $('#family-tree-chart').orgchart({
        'data': chartData,
        'nodeContent': 'title',
        'direction': 't2b',
        'pan': true,
        'zoom': true,
        'exportButton': true,
        'exportFilename': 'family-tree',
        'createNode': function($node, data) {
            // Custom node styling
            $node.html(createNodeTemplate(data));
            $node.addClass('family-node');
            $node.css({
                'background': getNodeColor(data.generation),
                'border': `2px solid ${getBorderColor(data.generation)}`,
                'border-radius': '8px',
                'padding': '12px',
                'min-width': '200px',
                'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
                'font-family': "'Noto Sans Devanagari', sans-serif"
            });
        }
    });

    // Add zoom controls
    addZoomControls();

    // Add generation legend
    addGenerationLegend();
}

function convertToOrgChartFormat(nodeDataArray) {
    // Find the root node (current member)
    const rootNode = nodeDataArray.find(node => node.isRoot || node.generation === 0);

    if (!rootNode) {
        return {};
    }

    // Convert to hierarchical structure
    return buildHierarchy(rootNode, nodeDataArray);
}

function buildHierarchy(node, allNodes) {
    const result = {
        'name': node.name,
        'title': node.name,
        'father': node.father,
        'birth_year': node.birth_year,
        'death_year': node.death_year,
        'village': node.village,
        'generation': node.generation
    };

    // Find children
    const children = allNodes.filter(n => n.pid === node.id);
    if (children.length > 0) {
        result.children = children.map(child => buildHierarchy(child, allNodes));
    }

    return result;
}

function createNodeTemplate(data) {
    return `
        <div style="text-align: center;">
            <div style="font-weight: bold; font-size: 14px; color: #1f2937; margin-bottom: 4px;">
                ${data.name || 'अज्ञात'}
            </div>
            ${data.father ? `<div style="font-size: 12px; color: #6b7280;">पिता: ${data.father}</div>` : ''}
            ${data.birth_year ? `<div style="font-size: 12px; color: #6b7280;">जन्म: ${data.birth_year}</div>` : ''}
            ${data.death_year ? `<div style="font-size: 12px; color: #6b7280;">मृत्यु: ${data.death_year}</div>` : ''}
            ${data.village ? `<div style="font-size: 12px; color: #6b7280;">गांव: ${data.village}</div>` : ''}
            ${data.generation !== undefined ? `<div style="font-size: 11px; color: #9ca3af; margin-top: 4px;">${getGenerationLabel(data.generation)}</div>` : ''}
        </div>
    `;
}

function getNodeColor(generation) {
    const colors = {
        0: '#dbeafe',    // Current member - blue
        1: '#dcfce7',    // Parents - green
        2: '#fef3c7',    // Grandparents - yellow
        3: '#fed7d7',    // Great-grandparents - red
        4: '#e9d5ff',    // 4th generation - purple
        5: '#fce7f3',    // 5th generation - pink
        6: '#f3e8ff',    // 6th generation - indigo
        7: '#f0f9ff'     // 7th generation - sky
    };
    return colors[generation] || '#f9fafb';
}

function getBorderColor(generation) {
    const colors = {
        0: '#3b82f6',    // Current member - blue
        1: '#10b981',    // Parents - green
        2: '#f59e0b',    // Grandparents - yellow
        3: '#ef4444',    // Great-grandparents - red
        4: '#8b5cf6',    // 4th generation - purple
        5: '#ec4899',    // 5th generation - pink
        6: '#6366f1',    // 6th generation - indigo
        7: '#0ea5e9'     // 7th generation - sky
    };
    return colors[generation] || '#d1d5db';
}

function getGenerationLabel(generation) {
    const labels = {
        0: 'वर्तमान सदस्य',
        1: '1वीं पीढ़ी (माता-पिता)',
        2: '2वीं पीढ़ी (दादा-दादी)',
        3: '3वीं पीढ़ी',
        4: '4वीं पीढ़ी',
        5: '5वीं पीढ़ी',
        6: '6वीं पीढ़ी',
        7: '7वीं पीढ़ी'
    };
    return labels[generation] || `${generation}वीं पीढ़ी`;
}

function addZoomControls() {
    const controlsHtml = `
        <div id="tree-controls" style="
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            gap: 8px;
        ">
            <button id="zoom-in" class="tree-control-btn" title="ज़ूम इन">+</button>
            <button id="zoom-out" class="tree-control-btn" title="ज़ूम आउट">-</button>
            <button id="zoom-fit" class="tree-control-btn" title="फिट करें">⌂</button>
        </div>
    `;

    $('#family-tree-chart').prepend(controlsHtml);

    // Add event listeners for zoom controls
    $('#zoom-in').on('click', function() {
        const $chart = $('#family-tree-chart .orgchart');
        const currentScale = parseFloat($chart.css('transform').match(/scale\(([^)]+)\)/)?.[1] || 1);
        const newScale = Math.min(currentScale * 1.2, 3);
        $chart.css('transform', `scale(${newScale})`);
    });

    $('#zoom-out').on('click', function() {
        const $chart = $('#family-tree-chart .orgchart');
        const currentScale = parseFloat($chart.css('transform').match(/scale\(([^)]+)\)/)?.[1] || 1);
        const newScale = Math.max(currentScale * 0.8, 0.3);
        $chart.css('transform', `scale(${newScale})`);
    });

    $('#zoom-fit').on('click', function() {
        const $chart = $('#family-tree-chart .orgchart');
        $chart.css('transform', 'scale(1)');
        $chart.css('transform-origin', 'center center');
    });
}

function addGenerationLegend() {
    const legendHtml = `
        <div id="generation-legend" style="
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-family: 'Noto Sans Devanagari', sans-serif;
            font-size: 12px;
            max-width: 200px;
            z-index: 1000;
        ">
            <div style="font-weight: bold; margin-bottom: 8px;">पीढ़ी रंग कोड</div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 16px; height: 16px; background: ${getNodeColor(0)}; border: 1px solid ${getBorderColor(0)}; margin-right: 8px; border-radius: 2px;"></div>
                वर्तमान सदस्य
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 16px; height: 16px; background: ${getNodeColor(1)}; border: 1px solid ${getBorderColor(1)}; margin-right: 8px; border-radius: 2px;"></div>
                माता-पिता
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 16px; height: 16px; background: ${getNodeColor(2)}; border: 1px solid ${getBorderColor(2)}; margin-right: 8px; border-radius: 2px;"></div>
                दादा-दादी
            </div>
            <div style="display: flex; align-items: center;">
                <div style="width: 16px; height: 16px; background: ${getNodeColor(3)}; border: 1px solid ${getBorderColor(3)}; margin-right: 8px; border-radius: 2px;"></div>
                पूर्वज
            </div>
        </div>
    `;

    $('#family-tree-chart').prepend(legendHtml);

    // Make legend draggable
    if ($.fn.draggable) {
        $('#generation-legend').draggable({
            containment: '#family-tree-chart',
            cursor: 'move'
        });
    }
}

// Export for global access if needed
window.FamilyTree = {
    initialize: initializeFamilyTree
};
