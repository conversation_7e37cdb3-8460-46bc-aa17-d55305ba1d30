@extends('layouts.app')

@section('title', $categoryTitle . ' - News & Announcements')
@section('meta_description', 'Latest ' . strtolower($categoryTitle) . ' news and announcements from Yadav Samaj.')

@section('content')
<div class="bg-white py-8 md:py-12">
    <div class="container mx-auto px-4">
        <!-- <PERSON> Header -->
        <div class="mb-8 md:mb-12 text-center">
            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-navy-900 mb-4">{{ $categoryTitle }}</h1>
            <div class="w-16 md:w-24 h-1 bg-saffron-500 mx-auto"></div>
            @if(isset($categoryDescription))
                <p class="mt-6 max-w-3xl mx-auto text-gray-600">{{ $categoryDescription }}</p>
            @endif
        </div>

        <!-- Year Filter -->
        <div class="mb-6 md:mb-8 max-w-4xl mx-auto">
            <!-- Mobile: Horizontal scroll -->
            <div class="md:hidden overflow-x-auto">
                <div class="flex gap-2 pb-2 min-w-max px-1">
                    <a href="{{ route('news.' . $category) }}"
                       class="flex-shrink-0 px-3 py-2 rounded-full text-sm {{ !request('year') ? 'bg-navy-600 text-white' : 'bg-gray-200 text-gray-700' }} transition-colors duration-200">
                        सभी वर्ष
                    </a>
                    @php
                        $currentYear = date('Y');
                        $years = range($currentYear, $currentYear - 4);
                    @endphp
                    @foreach($years as $year)
                        <a href="{{ route('news.' . $category, ['year' => $year]) }}"
                           class="flex-shrink-0 px-3 py-2 rounded-full text-sm {{ request('year') == $year ? 'bg-navy-600 text-white' : 'bg-gray-200 text-gray-700' }} transition-colors duration-200">
                            {{ $year }}
                        </a>
                    @endforeach
                </div>
            </div>

            <!-- Desktop: Centered flex -->
            <div class="hidden md:flex flex-wrap gap-2 justify-center">
                <a href="{{ route('news.' . $category) }}"
                   class="px-4 py-2 rounded-full {{ !request('year') ? 'bg-navy-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }} transition-colors duration-200">
                    सभी वर्ष
                </a>
                @php
                    $currentYear = date('Y');
                    $years = range($currentYear, $currentYear - 4);
                @endphp
                @foreach($years as $year)
                    <a href="{{ route('news.' . $category, ['year' => $year]) }}"
                       class="px-4 py-2 rounded-full {{ request('year') == $year ? 'bg-navy-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' }} transition-colors duration-200">
                        {{ $year }}
                    </a>
                @endforeach
            </div>
        </div>
        
        <!-- News List -->
        <div class="max-w-4xl mx-auto">
            @if(count($news) > 0)
                <div class="space-y-4 md:space-y-8">
                    @foreach($news as $newsItem)
                        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                            <!-- Mobile Layout -->
                            <div class="md:hidden">
                                @if($newsItem->getImageUrl())
                                    <div class="relative">
                                        <img src="{{ $newsItem->getImageUrl() }}" alt="{{ $newsItem->title }}" class="w-full h-48 object-cover">
                                        @if($newsItem->hasMultipleImages())
                                            <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                                </svg>
                                                {{ count($newsItem->getAllImageUrls()) }}
                                            </div>
                                        @endif
                                        @if(count($newsItem->getAllImageUrls()) > 0)
                                            <div class="absolute top-2 left-2 bg-navy-600 bg-opacity-90 text-white px-2 py-1 rounded text-xs font-medium">
                                                📰 {{ count($newsItem->getAllImageUrls()) }} {{ count($newsItem->getAllImageUrls()) === 1 ? 'Image' : 'Images' }}
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                <div class="p-4">
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <span class="inline-block bg-navy-100 text-navy-800 text-xs font-semibold px-2 py-1 rounded-full">{{ $categoryLabel }}</span>
                                        @if($newsItem->publication_date)
                                            <span class="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                                                {{ $newsItem->publication_date->format('M d, Y') }}
                                            </span>
                                        @endif
                                    </div>

                                    <h2 class="text-lg font-bold text-navy-900 mb-3 line-clamp-2">{{ $newsItem->title }}</h2>

                                    <div class="text-gray-600 mb-4 line-clamp-3 text-sm">
                                        {!! Str::limit(strip_tags($newsItem->content), 200) !!}
                                    </div>

                                    <div class="flex justify-between items-center">
                                        @if($newsItem->source)
                                            <span class="text-xs text-gray-500">स्रोत: {{ $newsItem->source }}</span>
                                        @else
                                            <span></span>
                                        @endif
                                        <a href="{{ route('news.show', $newsItem->id) }}" class="inline-flex items-center text-navy-600 hover:text-navy-800 font-medium text-sm">
                                            और पढ़ें
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Desktop Layout -->
                            <div class="hidden md:flex">
                                @if($newsItem->getImageUrl())
                                    <div class="w-1/3 relative">
                                        <img src="{{ $newsItem->getImageUrl() }}" alt="{{ $newsItem->title }}" class="w-full h-full object-cover">
                                        @if($newsItem->hasMultipleImages())
                                            <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                                </svg>
                                                {{ count($newsItem->getAllImageUrls()) }}
                                            </div>
                                        @endif
                                        @if(count($newsItem->getAllImageUrls()) > 0)
                                            <div class="absolute top-2 left-2 bg-navy-600 bg-opacity-80 text-white px-2 py-1 rounded text-xs">
                                                📰 {{ count($newsItem->getAllImageUrls()) }} {{ count($newsItem->getAllImageUrls()) === 1 ? 'Image' : 'Images' }}
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                <div class="p-6 {{ $newsItem->getImageUrl() ? 'w-2/3' : 'w-full' }}">
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <span class="inline-block bg-navy-100 text-navy-800 text-xs font-semibold px-3 py-1 rounded-full">{{ $categoryLabel }}</span>
                                        @if($newsItem->publication_date)
                                            <span class="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                                                {{ $newsItem->publication_date->format('F d, Y') }}
                                            </span>
                                        @endif
                                    </div>

                                    <h2 class="text-2xl font-bold text-navy-900 mb-4">{{ $newsItem->title }}</h2>

                                    <div class="prose text-gray-600 mb-6 line-clamp-3">
                                        {!! Str::limit(strip_tags($newsItem->content), 300) !!}
                                    </div>

                                    <div class="flex justify-between items-center">
                                        @if($newsItem->source)
                                            <span class="text-sm text-gray-500">स्रोत: {{ $newsItem->source }}</span>
                                        @else
                                            <span></span>
                                        @endif
                                        <a href="{{ route('news.show', $newsItem->id) }}" class="inline-flex items-center text-navy-600 hover:text-navy-800 font-medium text-sm">
                                            और पढ़ें
                                            <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="mt-12">
                    {{ $news->links() }}
                </div>
            @else
                <div class="text-center py-16 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900">कोई समाचार नहीं मिला</h3>
                    <p class="mt-1 text-gray-500">इस श्रेणी में अभी कोई {{ strtolower($categoryTitle) }} उपलब्ध नहीं है।</p>
                </div>
            @endif
            
            <!-- Related Categories -->
            <div class="mt-16 pt-8 border-t border-gray-200">
                <h2 class="text-2xl font-bold text-navy-900 mb-6">अन्य समाचार श्रेणियां</h2>
                
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
                    @foreach($relatedCategories as $relatedCategory => $relatedLabel)
                        <a href="{{ route('news.' . $relatedCategory) }}" class="bg-white border border-gray-200 p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 text-center">
                            <span class="text-navy-600 font-medium">{{ $relatedLabel }}</span>
                        </a>
                    @endforeach
                    
                    <a href="{{ route('news.index') }}" class="bg-white border border-gray-200 p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 text-center">
                        <span class="text-navy-600 font-medium">सभी समाचार</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
