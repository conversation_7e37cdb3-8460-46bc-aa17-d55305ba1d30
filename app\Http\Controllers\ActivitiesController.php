<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use Illuminate\Http\Request;

class ActivitiesController extends Controller
{
    /**
     * Display the main activities listing page.
     */
    public function index()
    {
        $recentActivities = Activity::where('is_published', true)
            ->orderBy('event_date', 'desc')
            ->take(10)
            ->get();
            
        $featured = Activity::getFeaturedActivities();
            
        return view('activities.index', compact('recentActivities', 'featured'));
    }
    
    /**
     * Display social activities.
     */
    public function social(Request $request)
    {
        $year = $request->input('year');
        $activities = Activity::getByCategory(Activity::CATEGORY_SOCIAL, $year);
        return view('activities.social', compact('activities'));
    }
    
    /**
     * Display cultural activities.
     */
    public function cultural(Request $request)
    {
        $year = $request->input('year');
        $activities = Activity::getByCategory(Activity::CATEGORY_CULTURAL, $year);
        return view('activities.cultural', compact('activities'));
    }
    
    /**
     * Display educational activities.
     */
    public function educational(Request $request)
    {
        $year = $request->input('year');
        $activities = Activity::getByCategory(Activity::CATEGORY_EDUCATIONAL, $year);
        return view('activities.educational', compact('activities'));
    }
    
    /**
     * Display government initiatives.
     */
    public function government(Request $request)
    {
        $year = $request->input('year');
        $activities = Activity::getByCategory(Activity::CATEGORY_GOVERNMENT, $year);
        return view('activities.government', compact('activities'));
    }
    
    /**
     * Display gallery items.
     */
    public function gallery(Request $request)
    {
        $year = $request->input('year');
        $activities = Activity::getByCategory(Activity::CATEGORY_GALLERY, $year);
        return view('activities.gallery', compact('activities'));
    }
    
    /**
     * Display a single activity.
     */
    public function show($id)
    {
        $activity = Activity::findOrFail($id);

        if (!$activity->is_published) {
            abort(404);
        }

        // Get related activities from the same category
        $relatedActivities = Activity::where('category', $activity->category)
            ->where('id', '!=', $activity->id)
            ->where('is_published', true)
            ->orderBy('event_date', 'desc')
            ->take(5)
            ->get();

        // Get recent activities from all categories for additional suggestions
        $recentActivities = Activity::where('is_published', true)
            ->where('id', '!=', $activity->id)
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get();

        return view('activities.show', compact('activity', 'relatedActivities', 'recentActivities'));
    }
}
