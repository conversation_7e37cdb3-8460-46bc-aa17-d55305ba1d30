<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Drop old English field names since we're using Hindi fields only
            $table->dropColumn([
                'name',
                'post_in_community', 
                'address',
                'office_name',
                'office_post'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Add back old fields if needed
            $table->string('name')->nullable();
            $table->string('post_in_community')->nullable();
            $table->text('address')->nullable();
            $table->string('office_name')->nullable();
            $table->string('office_post')->nullable();
        });
    }
};
