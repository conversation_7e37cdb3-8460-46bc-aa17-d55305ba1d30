<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('yadav_vyapar_grahak', function (Blueprint $table) {
            $table->id();
            $table->enum('category', ['swasthya', 'gharelu', 'salahkar', 'anya vyavasay']);
            $table->string('sadasyata_kramank');
            $table->string('sadasyata_prakar');
            $table->string('naam');
            $table->string('ajivika');
            $table->string('ward');
            $table->string('vikaskhand');
            $table->string('jila');
            $table->string('mobile_number');
            $table->text('address');
            $table->string('photo')->nullable(); // File path for uploaded photo
            $table->string('gmap_link')->nullable(); // Google Maps location link
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('yadav_vyapar_grahak');
    }
};
