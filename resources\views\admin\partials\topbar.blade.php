<header class="bg-white shadow-lg lg:pr-0 border-b border-gray-200"> <div class="px-4 sm:px-6 lg:px-8"> <div class="flex justify-between h-16"> <!-- Left section --> <div class="flex items-center"> <!-- Mobile menu button --> <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden -ml-2 mr-2 inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-navy-900 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-navy-900 transition duration-150 ease-in-out" > <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /> </svg> </button> <!-- Search input --> <div class="hidden md:block"> <div class="relative"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /> </svg> </div> <input type="text" placeholder="Search..." class="admin-form-input pl-10 pr-4 py-2 w-64" > </div> </div> </div> <!-- Right section --> <div class="flex items-center space-x-4"> <!-- Notifications dropdown --> <div x-data="{ open: false }" @click.away="open = false" class="relative"> <button @click="open = !open" class="p-1 rounded-full text-gray-700 hover:text-saffron-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-saffron-500 relative" > <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /> </svg> <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span> </button> <div x-show="open" x-cloak x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none divide-y divide-gray-100 z-50" > <div class="px-4 py-3"> <h3 class="text-sm font-medium text-gray-800">Notifications</h3> </div> <div class="py-1 max-h-60 overflow-y-auto"> <a href="#" class="block px-4 py-3 text-sm text-gray-800 hover:bg-gray-100"> <div class="flex items-start"> <div class="flex-shrink-0 bg-saffron-500 h-8 w-8 rounded-full flex items-center justify-center"> <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /> </svg> </div> <div class="ml-3 w-0 flex-1"> <p class="font-medium text-gray-900">New user registered</p> <p class="mt-1 text-gray-600">John Doe just created an account</p> <p class="mt-1 text-xs text-gray-500">10 minutes ago</p> </div> </div> </a> <a href="#" class="block px-4 py-3 text-sm text-gray-800 hover:bg-gray-100"> <div class="flex items-start"> <div class="flex-shrink-0 bg-green-500 h-8 w-8 rounded-full flex items-center justify-center"> <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div class="ml-3 w-0 flex-1"> <p class="font-medium text-gray-900">Task completed</p> <p class="mt-1 text-gray-600">Website backup completed successfully</p> <p class="mt-1 text-xs text-gray-500">1 hour ago</p> </div> </div> </a> </div> <div class="py-1"> <a href="#" class="block px-4 py-2 text-sm text-center font-medium text-saffron-600 hover:bg-gray-100"> View all notifications </a> </div> </div> </div> <!-- Profile dropdown --> <div x-data="{ open: false }" @click.away="open = false" class="relative"> <button @click="open = !open" class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-saffron-500" > <img class="h-8 w-8 rounded-full bg-gray-300" src="https://ui-avatars.com/api/?name=Admin+User" alt="User avatar" /> <span class="hidden md:inline-block text-gray-800 font-medium">Admin User</span> <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /> </svg> </button> <div x-show="open" x-cloak x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50" > <a href="" class="block px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"> Your Profile </a> <a href="" class="block px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"> Settings </a> <form method="POST" action="{{ route('admin.logout') }}" class="block"> @csrf <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"> Sign out </button> </form> </div> </div> </div> </div> </div> </header> <!-- Mobile search (visible only on mobile) --> <div class="lg:hidden px-4 py-3 bg-gray-100 border-t border-gray-200"> <div class="relative"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /> </svg> </div> <input type="text" placeholder="Search..." class="admin-form-input pl-10 pr-4 py-2 w-full" > </div> </div>