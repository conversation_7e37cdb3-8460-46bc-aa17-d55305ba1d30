<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create {email?} {name?} {password?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? $this->ask('Email address?');
        $name = $this->argument('name') ?? $this->ask('Full name?');
        $password = $this->argument('password') ?? $this->secret('Password? (min 8 characters)');

        // Validate input
        if (strlen($password) < 8) {
            $this->error('Password must be at least 8 characters');
            return 1;
        }

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            if ($existingUser->is_admin) {
                $this->error('An admin with this email already exists');
                return 1;
            } else {
                // Convert existing user to admin
                $existingUser->is_admin = true;
                $existingUser->save();
                $this->info("User {$email} has been upgraded to admin");
                return 0;
            }
        }

        // Create new admin user
        User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'is_admin' => true,
        ]);

        $this->info("Admin user {$email} created successfully");

        return 0;
    }
} 