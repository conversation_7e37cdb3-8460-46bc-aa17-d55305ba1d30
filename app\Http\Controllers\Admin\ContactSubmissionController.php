<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;

class ContactSubmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ContactSubmission::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $submissions = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.contact-submissions.index', compact('submissions'));
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactSubmission $contactSubmission)
    {
        // Mark as read if it's pending
        if ($contactSubmission->status === 'pending') {
            $contactSubmission->markAsRead();
        }

        return view('admin.contact-submissions.show', compact('contactSubmission'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContactSubmission $contactSubmission)
    {
        $request->validate([
            'status' => 'required|in:pending,read,replied',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $data = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];

        // Set timestamps based on status
        if ($request->status === 'read' && $contactSubmission->read_at === null) {
            $data['read_at'] = now();
        }

        if ($request->status === 'replied' && $contactSubmission->replied_at === null) {
            $data['replied_at'] = now();
        }

        $contactSubmission->update($data);

        return redirect()->back()->with('success', 'संपर्क सबमिशन सफलतापूर्वक अपडेट किया गया।');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactSubmission $contactSubmission)
    {
        $contactSubmission->delete();

        return redirect()->route('admin.contact-submissions.index')
            ->with('success', 'संपर्क सबमिशन सफलतापूर्वक हटाया गया।');
    }
}
