@extends('layouts.admin') @section('title', 'Edit Content') @section('breadcrumbs') <div class="text-sm breadcrumbs text-gray-500"> <ul> <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li> <li><a href="{{ route('admin.about-content.index') }}">About Content</a></li> <li>Edit Content</li> </ul> </div> @endsection @section('header') <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6"> <h1 class="text-2xl font-bold">Edit Content</h1> </div> @endsection @section('content') @if ($errors->any()) <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /> </svg> </div> <div class="ml-3"> <p class="text-sm text-red-700"> <strong>There were errors with your submission:</strong> <ul class="list-disc pl-5 mt-1"> @foreach ($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </p> </div> </div> </div> @endif <form action="{{ route('admin.about-content.update', $content->id) }}" method="POST" enctype="multipart/form-data" class="space-y-6"> @csrf @method('PUT') <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Page Selection --> <div> <label for="page_key" class="block text-sm font-medium text-gray-700 mb-1">Page <span class="text-red-600">*</span></label> <select id="page_key" name="page_key" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 sm:text-sm rounded-md"> <option value="">Select page</option> @foreach($pageOptions as $key => $label) <option value="{{ $key }}" {{ (old('page_key', $content->page_key) == $key) ? 'selected' : '' }}>{{ $label }}</option> @endforeach </select> @error('page_key') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Section Key --> <div> <label for="section_key" class="block text-sm font-medium text-gray-700 mb-1">Section Key (Optional)</label> <input type="text" name="section_key" id="section_key" value="{{ old('section_key', $content->section_key) }}" class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="E.g. mission, vision, leadership"> @error('section_key') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> <!-- Title --> <div> <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title <span class="text-red-600">*</span></label> <input type="text" name="title" id="title" value="{{ old('title', $content->title) }}" required class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> @error('title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Content --> <div class="space-y-3"> <label for="content" class="block text-sm font-medium text-gray-700 mb-2"> Content <span class="text-red-600">*</span> </label> <!-- Content Formatting Tips --> <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"> <h4 class="text-sm font-medium text-blue-800 mb-2">📝 Content Formatting Tips:</h4> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-blue-700"> <div> <p class="font-medium mb-1">✨ Structure your content:</p> <ul class="list-disc pl-4 space-y-1"> <li>Use double line breaks for new paragraphs</li> <li>Keep paragraphs short and readable</li> <li>Use clear headings and sections</li> </ul> </div> <div> <p class="font-medium mb-1">📋 Best Practices:</p> <ul class="list-disc pl-4 space-y-1"> <li>Write in clear, simple language</li> <li>Use bullet points for lists</li> <li>Include relevant details and examples</li> </ul> </div> </div> </div> <textarea id="content" name="content" rows="20" required class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm text-sm border-gray-300 rounded-lg resize-y" placeholder="यहाँ अपना कंटेंट लिखें...">{{ old('content', $content->content) }}</textarea> @error('content') <p class="mt-2 text-sm text-red-600 flex items-center"> <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path> </svg> {{ $message }} </p> @enderror <!-- Character Counter --> <div class="flex justify-between items-center text-xs text-gray-500"> <span>Content will be automatically formatted for better readability</span> <span id="char-counter">0 characters</span> </div> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Image Upload --> <div> <label for="image" class="block text-sm font-medium text-gray-700 mb-1">Image</label> @if($content->image_path) <div class="mb-3"> <div class="mt-1 w-40 h-auto rounded overflow-hidden bg-gray-100"> <img src="{{ asset($content->image_path) }}" alt="{{ $content->title }}" class="w-full h-auto object-cover"> </div> <p class="text-xs text-gray-500 mt-1">Current image. Upload a new one to replace.</p> </div> @endif <div class="mt-1 flex items-center"> <input type="file" name="image" id="image" accept="image/*" class="focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full text-sm text-gray-900 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-navy-50:bg-navy-900 file:text-navy-700:text-navy-300 hover:file:bg-navy-100:file:bg-navy-800"> </div> <p class="text-xs text-gray-500 mt-1">PNG, JPG, or JPEG up to 2MB</p> @error('image') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div> <label for="display_order" class="block text-sm font-medium text-gray-700 mb-1">Display Order</label> <input type="number" name="display_order" id="display_order" value="{{ old('display_order', $content->display_order) }}" class="mt-1 focus:ring-navy-500:ring-navy-400 focus:border-navy-500:border-navy-400 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"> <p class="text-xs text-gray-500 mt-1">Lower numbers appear first</p> @error('display_order') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> </div> <!-- Is Published --> <div class="flex items-center"> <input id="is_published" name="is_published" type="checkbox" {{ old('is_published', $content->is_published) ? 'checked' : '' }} class="h-4 w-4 text-navy-600 focus:ring-navy-500:ring-navy-400 border-gray-300 rounded"> <label for="is_published" class="ml-2 block text-sm text-gray-900"> Published </label> </div> <!-- Action Buttons --> <div class="flex justify-end space-x-3 pt-5"> <a href="{{ route('admin.about-content.index', ['page_key' => $content->page_key]) }}" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:ring-navy-400"> Cancel </a> <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-navy-600 hover:bg-navy-700:bg-navy-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:ring-navy-400"> Update Content </button> </div> </form> @endsection @push('scripts') <script> document.addEventListener('DOMContentLoaded', function() { const contentTextarea = document.getElementById('content'); const charCounter = document.getElementById('char-counter'); const titleInput = document.getElementById('title'); const slugInput = document.getElementById('slug'); // Character counter function updateCharCounter() { const count = contentTextarea.value.length; charCounter.textContent = count.toLocaleString() + ' characters'; // Color coding based on length if (count < 100) { charCounter.className = 'text-red-500'; } else if (count < 500) { charCounter.className = 'text-yellow-500'; } else { charCounter.className = 'text-green-500'; } } // Auto-generate slug from title (only if slug is empty) function generateSlug() { if (titleInput && slugInput && !slugInput.value.trim()) { const title = titleInput.value; const slug = title.toLowerCase() .replace(/[^a-z0-9\u0900-\u097F\s-]/g, '') // Allow Devanagari characters .replace(/\s+/g, '-') .replace(/-+/g, '-') .trim('-'); slugInput.value = slug; } } // Event listeners if (contentTextarea) { contentTextarea.addEventListener('input', updateCharCounter); updateCharCounter(); // Initial count } if (titleInput) { titleInput.addEventListener('input', generateSlug); } // Form validation enhancement const form = document.querySelector('form'); if (form) { form.addEventListener('submit', function(e) { const content = contentTextarea.value.trim(); if (content.length < 10) { e.preventDefault(); alert('कृपया कम से कम 10 अक्षरों का कंटेंट लिखें।'); contentTextarea.focus(); return false; } }); } }); </script> @endpush 