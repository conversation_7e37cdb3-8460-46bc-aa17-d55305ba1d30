@extends('layouts.app')

@section('title', 'मल्टीनेशनल कंपनी विवरण')

@section('content')
    <div class="bg-gradient-to-b to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">{{ $entry->naam }}</h1>
                <p class="text-gray-600">मल्टीनेशनल कंपनी की संपूर्ण जानकारी</p>
            </div>

            <!-- Back Button -->
            <div class="max-w-4xl mx-auto mb-6">
                <a href="{{ route('multinational-company-reports') }}" 
                   class="inline-flex items-center text-purple-600 hover:text-purple-800 font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    वापस रिपोर्ट सूची में जाएं
                </a>
            </div>

            <!-- Details Card -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <!-- Header -->
                    <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="text-2xl font-bold mb-2">{{ $entry->naam }}</h2>
                                <p class="text-purple-100">जानकारी जमा की गई: {{ $entry->created_at->format('d/m/Y') }}</p>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                सत्यापित
                            </span>
                        </div>
                    </div>

                    <div class="p-6 md:p-8 space-y-8">
                        <!-- Personal Information -->
                        <div class="bg-blue-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                व्यक्तिगत जानकारी
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">नाम:</label>
                                    <p class="text-gray-800 font-semibold">{{ $entry->naam }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">पिता का नाम:</label>
                                    <p class="text-gray-800 font-semibold">{{ $entry->pita_ka_naam }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">उम्र:</label>
                                    <p class="text-gray-800 font-semibold">{{ $entry->umra }} वर्ष</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">मोबाइल नंबर:</label>
                                    <p class="text-gray-800 font-semibold">
                                        <a href="tel:{{ $entry->mobile }}" class="text-purple-600 hover:text-purple-800">
                                            {{ $entry->mobile }}
                                        </a>
                                    </p>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-600 mb-1">पता:</label>
                                    <p class="text-gray-700">{{ $entry->pata }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div class="bg-purple-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                कंपनी की जानकारी
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">कंपनी का नाम:</label>
                                    <p class="text-gray-800 font-semibold">{{ $entry->company_name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">स्थान:</label>
                                    <p class="text-gray-800 font-semibold">{{ $entry->location }}</p>
                                </div>
                                @if($entry->package)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-600 mb-1">पैकेज:</label>
                                        <p class="text-gray-800 font-semibold">{{ $entry->package }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Qualifications -->
                        <div class="bg-green-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                शैक्षणिक योग्यता
                            </h3>
                            <div>
                                <label class="block text-sm font-medium text-gray-600 mb-1">अहर्ताएं:</label>
                                <p class="text-gray-700 leading-relaxed">{{ $entry->ahartayen }}</p>
                            </div>
                        </div>

                        <!-- Media Information -->
                        @if($entry->media_link)
                            <div class="bg-yellow-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    मीडिया लिंक
                                </h3>
                                <div class="flex items-center space-x-4">
                                    <div class="flex-1">
                                        <p class="text-gray-700 mb-2">3 मिनट का वीडियो देखें</p>
                                        <p class="text-sm text-gray-500">{{ $entry->media_link }}</p>
                                    </div>
                                    <a href="{{ $entry->media_link }}" 
                                       target="_blank"
                                       class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                        वीडियो देखें
                                    </a>
                                </div>
                            </div>
                        @endif

                        <!-- Biodata Download -->
                        @if($entry->biodata)
                            <div class="bg-indigo-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    बायोडाटा
                                </h3>
                                <div class="flex items-center space-x-4">
                                    <div class="flex-1">
                                        <p class="text-gray-700 mb-2">उम्मीदवार का पूरा बायोडाटा डाउनलोड करें</p>
                                        <p class="text-sm text-gray-500">फाइल फॉर्मेट: {{ strtoupper(pathinfo($entry->biodata, PATHINFO_EXTENSION)) }}</p>
                                    </div>
                                    <a href="{{ asset('storage/' . $entry->biodata) }}" 
                                       download
                                       class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        डाउनलोड करें
                                    </a>
                                </div>
                            </div>
                        @endif

                        <!-- Contact Actions -->
                        <div class="bg-purple-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">संपर्क करें</h3>
                            <div class="flex flex-wrap gap-4">
                                <a href="tel:{{ $entry->mobile }}" 
                                   class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    कॉल करें
                                </a>
                                <a href="https://wa.me/91{{ $entry->mobile }}" target="_blank"
                                   class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                    WhatsApp पर संदेश
                                </a>
                                @if($entry->media_link)
                                    <a href="{{ $entry->media_link }}" 
                                       target="_blank"
                                       class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                        वीडियो देखें
                                    </a>
                                @endif
                                @if($entry->biodata)
                                    <a href="{{ asset('storage/' . $entry->biodata) }}" 
                                       download
                                       class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        बायोडाटा डाउनलोड
                                    </a>
                                @endif
                            </div>
                        </div>

                        <!-- Application Status -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                जानकारी की स्थिति
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">जानकारी जमा की गई:</label>
                                    <p class="text-gray-800">{{ $entry->created_at->format('d/m/Y H:i:s') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">अंतिम अपडेट:</label>
                                    <p class="text-gray-800">{{ $entry->updated_at->format('d/m/Y H:i:s') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
