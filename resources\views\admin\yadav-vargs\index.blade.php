@extends('layouts.admin') @section('title', 'यादव वर्ग प्रबंधन') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">यादव वर्ग प्रबंधन</h1> <p class="mt-1 text-sm admin-text-secondary">यादव समुदाय के विभिन्न वर्गों का प्रबंधन करें</p> </div> <div class="mt-4 sm:mt-0"> <a href="{{ route('admin.yadav-vargs.create') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path> </svg> नया यादव वर्ग जोड़ें </a> </div> </div> </div> <!-- Search and Filter --> <div class="admin-card p-6 mb-6"> <form method="GET" action="{{ route('admin.yadav-vargs.index') }}" class="flex flex-col lg:flex-row gap-4"> <div class="flex-1"> <input type="text" name="search" value="{{ request('search') }}" placeholder="नाम, अंग्रेजी नाम, विवरण या श्रेणी से खोजें..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> </div> <div class="flex flex-col sm:flex-row gap-2"> <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी स्थितियां</option> <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>सक्रिय</option> <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>निष्क्रिय</option> </select> <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> खोजें </button> @if(request()->hasAny(['search', 'status'])) <a href="{{ route('admin.yadav-vargs.index') }}" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> साफ़ करें </a> @endif </div> </form> </div> <!-- Success/Error Messages --> @if(session('success')) <div class="admin-card p-4 mb-6 bg-green-50 border-l-4 border-green-400"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <p class="text-sm text-green-700">{{ session('success') }}</p> </div> </div> </div> @endif @if($errors->any()) <div class="admin-card p-4 mb-6 bg-red-50 border-l-4 border-red-400"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> @foreach($errors->all() as $error) <p class="text-sm text-red-700">{{ $error }}</p> @endforeach </div> </div> </div> @endif <!-- Data Table --> <div class="admin-card overflow-hidden"> <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">नाम</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">अंग्रेजी नाम</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">श्रेणी</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">क्रम</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">बनाया गया</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @forelse($yadavVargs as $yadavVarg) <tr class="hover:bg-gray-50:bg-navy-800"> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm font-medium admin-text-primary">{{ $yadavVarg->name }}</div> @if($yadavVarg->description) <div class="text-sm admin-text-secondary">{{ Str::limit($yadavVarg->description, 50) }}</div> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $yadavVarg->name_english ?? '-' }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $yadavVarg->category ?? '-' }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $yadavVarg->display_order }} </td> <td class="px-6 py-4 whitespace-nowrap"> @if($yadavVarg->is_active) <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"> सक्रिय </span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"> निष्क्रिय </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $yadavVarg->created_at->format('d/m/Y') }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex space-x-2"> <a href="{{ route('admin.yadav-vargs.show', $yadavVarg) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" title="देखें"> <i class="fas fa-eye"></i> </a> <a href="{{ route('admin.yadav-vargs.edit', $yadavVarg) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" title="संपादित करें"> <i class="fas fa-edit"></i> </a> <button onclick="confirmDelete({{ $yadavVarg->id }})" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" title="हटाएं"> <i class="fas fa-trash"></i> </button> </div> </td> </tr> @empty <tr> <td colspan="7" class="px-6 py-4 text-center text-sm admin-text-secondary"> कोई यादव वर्ग नहीं मिला। </td> </tr> @endforelse </tbody> </table> </div> <!-- Pagination --> @if($yadavVargs->hasPages()) <div class="px-6 py-4 border-t border-gray-200"> {{ $yadavVargs->links() }} </div> @endif </div> </div> </div> <!-- Delete Confirmation Modal --> <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"> <div class="mt-3 text-center"> <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100"> <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path> </svg> </div> <h3 class="text-lg font-medium admin-text-primary mt-2">यादव वर्ग हटाएं</h3> <p class="text-sm admin-text-secondary mt-2"> क्या आप वाकई इस यादव वर्ग को हटाना चाहते हैं? यह कार्य पूर्ववत नहीं किया जा सकता। </p> <div class="flex justify-center space-x-4 mt-4"> <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> रद्द करें </button> <form id="deleteForm" method="POST" class="inline"> @csrf @method('DELETE') <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"> हटाएं </button> </form> </div> </div> </div> </div> @endsection @push('scripts') <script> function confirmDelete(yadavVargId) { const deleteForm = document.getElementById('deleteForm'); deleteForm.action = `/admin/yadav-vargs/${yadavVargId}`; document.getElementById('deleteModal').classList.remove('hidden'); } function closeDeleteModal() { document.getElementById('deleteModal').classList.add('hidden'); } // Close modal when clicking outside document.getElementById('deleteModal').addEventListener('click', function(e) { if (e.target === this) { closeDeleteModal(); } }); </script> @endpush 