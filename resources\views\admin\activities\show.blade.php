@extends('layouts.admin') @section('title', $activity->title) @section('breadcrumbs') <div class="text-sm breadcrumbs text-gray-500"> <ul> <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li> <li><a href="{{ route('admin.activities.index', ['category' => $activity->category]) }}">Activities</a></li> <li>View Activity</li> </ul> </div> @endsection @section('header') <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6"> <h1 class="text-2xl font-bold">{{ $activity->title }}</h1> <div class="flex flex-row gap-2"> <a href="{{ route('admin.activities.edit', $activity->id) }}" class="btn btn-secondary"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor"> <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" /> </svg> Edit </a> <form action="{{ route('admin.activities.destroy', $activity->id) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this activity?');"> @csrf @method('DELETE') <button type="submit" class="btn btn-danger"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /> </svg> Delete </button> </form> </div> </div> @endsection @section('content') <div class="bg-white shadow overflow-hidden sm:rounded-lg"> <div class="px-4 py-5 sm:px-6 border-b border-gray-200"> <h3 class="text-lg leading-6 font-medium text-gray-900">Activity Details</h3> <p class="mt-1 max-w-2xl text-sm text-gray-500"> {{ $categoryOptions[$activity->category] ?? 'Unknown Category' }} </p> </div> <div class="px-4 py-5 sm:p-6"> @if($activity->image_path) <div class="mb-6"> <div class="w-full md:w-1/2 lg:w-1/3 rounded overflow-hidden"> <img src="{{ asset($activity->image_path) }}" alt="{{ $activity->title }}" class="w-full h-auto"> </div> </div> @endif <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2"> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Title</dt> <dd class="mt-1 text-sm text-gray-900">{{ $activity->title }}</dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Status</dt> <dd class="mt-1 text-sm"> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $activity->is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}"> {{ $activity->is_published ? 'Published' : 'Draft' }} </span> </dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Event Date</dt> <dd class="mt-1 text-sm text-gray-900"> {{ $activity->event_date ? $activity->event_date->format('F d, Y') : 'Not specified' }} </dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Location</dt> <dd class="mt-1 text-sm text-gray-900"> {{ $activity->location ?? 'Not specified' }} </dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Created At</dt> <dd class="mt-1 text-sm text-gray-900"> {{ $activity->created_at->format('F d, Y H:i') }} </dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Last Updated</dt> <dd class="mt-1 text-sm text-gray-900"> {{ $activity->updated_at->format('F d, Y H:i') }} </dd> </div> <div class="sm:col-span-1"> <dt class="text-sm font-medium text-gray-500">Display Order</dt> <dd class="mt-1 text-sm text-gray-900"> {{ $activity->display_order }} </dd> </div> </dl> <div class="mt-6 sm:col-span-2"> <dt class="text-sm font-medium text-gray-500">Description</dt> <dd class="mt-1 text-sm text-gray-900 prose max-w-none"> {!! $activity->description !!} </dd> </div> </div> <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 border-t border-gray-200"> <a href="{{ route('admin.activities.index', ['category' => $activity->category]) }}" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:ring-navy-400"> Back to Activities </a> </div> </div> @endsection 