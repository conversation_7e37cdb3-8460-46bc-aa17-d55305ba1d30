@extends('layouts.member')

@section('title', 'आवेदन स्थिति')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Mobile Header -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-navy-700 lg:hidden">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="{{ route('member.dashboard') }}" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-navy-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white">आवेदन स्थिति</h1>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Desktop Back Button -->
        <div class="hidden lg:block mb-6">
            <a href="{{ route('member.dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                डैशबोर्ड पर वापस जाएं
            </a>
        </div>

        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                आवेदन स्थिति
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                अपने सदस्यता आवेदन की वर्तमान स्थिति देखें
            </p>
        </div>

        <!-- Status Timeline -->
        <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <div class="flow-root">
                    <ul class="-mb-8">
                        <!-- Application Submitted -->
                        <li>
                            <div class="relative pb-8">
                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-navy-600" aria-hidden="true"></span>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-navy-800">
                                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-900 dark:text-white font-medium">आवेदन जमा किया गया</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">आपका सदस्यता आवेदन सफलतापूर्वक जमा हो गया है</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                            {{ $member->created_at->format('d M Y, h:i A') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <!-- Under Review -->
                        <li>
                            <div class="relative pb-8">
                                @if(!$member->isPending())
                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-navy-600" aria-hidden="true"></span>
                                @endif
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full {{ $member->isPending() ? 'bg-yellow-500' : 'bg-green-500' }} flex items-center justify-center ring-8 ring-white dark:ring-navy-800">
                                            @if($member->isPending())
                                                <svg class="h-5 w-5 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            @else
                                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-900 dark:text-white font-medium">
                                                {{ $member->isPending() ? 'समीक्षा के अधीन' : 'समीक्षा पूर्ण' }}
                                            </p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $member->isPending() ? 'आपका आवेदन व्यवस्थापक की समीक्षा में है' : 'व्यवस्थापक द्वारा समीक्षा पूर्ण की गई' }}
                                            </p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                            @if($member->isPending())
                                                प्रगति में
                                            @else
                                                {{ $member->updated_at->format('d M Y, h:i A') }}
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <!-- Final Status -->
                        <li>
                            <div class="relative">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full {{ $member->isApproved() ? 'bg-green-500' : ($member->isRejected() ? 'bg-red-500' : 'bg-gray-300 dark:bg-navy-600') }} flex items-center justify-center ring-8 ring-white dark:ring-navy-800">
                                            @if($member->isApproved())
                                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                                </svg>
                                            @elseif($member->isRejected())
                                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            @else
                                                <svg class="h-5 w-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-900 dark:text-white font-medium">
                                                @if($member->isApproved())
                                                    आवेदन स्वीकृत
                                                @elseif($member->isRejected())
                                                    आवेदन अस्वीकृत
                                                @else
                                                    निर्णय की प्रतीक्षा
                                                @endif
                                            </p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                @if($member->isApproved())
                                                    बधाई हो! आपका सदस्यता आवेदन स्वीकार कर लिया गया है
                                                @elseif($member->isRejected())
                                                    खुशी है कि आपका आवेदन अस्वीकार कर दिया गया है
                                                @else
                                                    अंतिम निर्णय की प्रतीक्षा में
                                                @endif
                                            </p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                            @if($member->isApproved() || $member->isRejected())
                                                {{ $member->updated_at->format('d M Y, h:i A') }}
                                            @else
                                                प्रतीक्षा में
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Rejection Reason (if rejected) -->
        @if($member->isRejected() && $member->rejection_reason)
            <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-300">
                            अस्वीकृति का कारण
                        </h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-400">
                            {{ $member->rejection_reason }}
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Current Status Card -->
        <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                    वर्तमान स्थिति
                </h3>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="px-3 py-1 text-sm font-medium rounded-full {{ $member->getStatusBadgeClass() }}">
                            {{ $member->getStatusDisplayText() }}
                        </span>
                        <span class="ml-3 text-sm text-gray-500 dark:text-gray-400">
                            सदस्यता संख्या: {{ $member->membership_number }}
                        </span>
                    </div>
                    @if($member->isApproved())
                        <div class="flex space-x-2">
                            <a href="{{ route('member.preview-card') }}" target="_blank"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition duration-200">
                                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                कार्ड देखें
                            </a>
                            <a href="{{ route('member.download-card') }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition duration-200">
                                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                PDF डाउनलोड करें
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300">
                        सहायता की आवश्यकता है?
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-400">
                        <p>यदि आपके पास अपने आवेदन के बारे में कोई प्रश्न है, तो कृपया व्यवस्थापक से संपर्क करें।</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@php
use Illuminate\Support\Facades\Storage;
@endphp
@endsection
