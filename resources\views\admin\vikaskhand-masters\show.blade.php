@extends('layouts.admin') @section('title', 'विकासखंड विवरण') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">विकासखंड विवरण</h1> <p class="mt-1 text-sm admin-text-secondary">{{ $vikaskhandMaster->sub_district_name_hin }} का विवरण</p> </div> <div class="flex space-x-3"> <a href="{{ route('admin.vikaskhand-masters.edit', $vikaskhandMaster->id) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> संपादित करें </a> <a href="{{ route('admin.vikaskhand-masters.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg> वापस जाएं </a> </div> </div> </div> <!-- Details --> <div class="admin-card p-6"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- Sub-District LGD Code --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">विकासखंड LGD कोड</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->sub_district_lgd_code }}</p> </div> <!-- Sub-District Name Hindi --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">विकासखंड नाम (हिंदी)</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->sub_district_name_hin }}</p> </div> <!-- Sub-District Name English --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">विकासखंड नाम (अंग्रेजी)</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->sub_district_name_eng }}</p> </div> <!-- District --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">जिला</label> @if($vikaskhandMaster->district) <p class="text-lg admin-text-primary"> {{ $vikaskhandMaster->district->district_name_hin }} <span class="text-sm admin-text-secondary">({{ $vikaskhandMaster->district->district_name_eng }})</span> </p> @if($vikaskhandMaster->district->division) <p class="text-sm admin-text-secondary mt-1"> संभाग: {{ $vikaskhandMaster->district->division->division_name_hin }} </p> @endif @else <p class="text-lg admin-text-primary">-</p> @endif </div> <!-- Status --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">स्थिति</label> @if($vikaskhandMaster->is_active) <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"> <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg> सक्रिय </span> @else <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800"> <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> निष्क्रिय </span> @endif </div> <!-- Display Order --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">प्रदर्शन क्रम</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->display_order }}</p> </div> <!-- Created At --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">बनाया गया</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->created_at->format('d/m/Y H:i') }}</p> </div> <!-- Updated At --> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">अंतिम अपडेट</label> <p class="text-lg admin-text-primary">{{ $vikaskhandMaster->updated_at->format('d/m/Y H:i') }}</p> </div> </div> </div> </div> </div> @endsection 