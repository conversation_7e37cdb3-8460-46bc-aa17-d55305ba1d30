<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Panel')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Hind:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Additional Styles -->
    <style>
        [x-cloak] { display: none !important; }
        
        /* Enhanced Admin styles for better readability */
        .admin-content {
            @apply bg-white shadow-lg rounded-lg p-5;
        }

        .admin-text-primary {
            @apply text-navy-900 font-medium !important;
        }

        .admin-text-secondary {
            @apply text-gray-700 !important;
        }

        .admin-form-input {
            @apply border-gray-300 bg-white text-navy-900 focus:border-saffron-500 focus:ring focus:ring-saffron-400 focus:ring-opacity-50 rounded-md shadow-sm;
        }

        .admin-highlight {
            @apply text-saffron-600 font-medium;
        }

        .admin-card {
            @apply bg-white border border-gray-100 rounded-lg shadow-md;
        }

        /* Force proper text colors in admin content areas */
        .admin-card .admin-text-primary,
        .admin-content .admin-text-primary {
            color: #1e3a8a !important; /* navy-900 */
        }

        .admin-card .admin-text-secondary,
        .admin-content .admin-text-secondary {
            color: #374151 !important; /* gray-700 */
        }

        /* Ensure sidebar is always dark */
        .admin-sidebar {
            @apply bg-navy-900 text-white;
        }

        /* Force visibility for all text elements in admin areas */
        .admin-card h1, .admin-card h2, .admin-card h3, .admin-card h4, .admin-card h5, .admin-card h6 {
            color: #1e3a8a !important; /* navy-900 */
        }

        .admin-card p, .admin-card span, .admin-card div {
            color: #374151 !important; /* gray-700 */
        }

        .admin-card label {
            color: #1e3a8a !important; /* navy-900 */
        }

        /* Ensure buttons have proper colors */
        .admin-card button, .admin-card a {
            color: inherit !important;
        }

        /* Fix input text colors */
        .admin-form-input {
            color: #1e3a8a !important; /* navy-900 */
            background-color: #ffffff !important;
        }

        /* Force input and textarea text visibility */
        input[type="text"], input[type="number"], input[type="file"], textarea, select {
            color: #1e3a8a !important; /* navy-900 */
            background-color: #ffffff !important;
        }

        /* Fix placeholder text */
        input::placeholder, textarea::placeholder {
            color: #6b7280 !important; /* gray-500 */
        }

        /* Ensure all text in admin content areas is visible */
        main .bg-white, main .admin-card, main .admin-content {
            color: #1f2937 !important; /* gray-800 */
        }

        main .bg-white *, main .admin-card *, main .admin-content * {
            color: inherit !important;
        }

        /* Override for specific admin text classes */
        main .admin-text-primary {
            color: #1e3a8a !important; /* navy-900 */
        }

        main .admin-text-secondary {
            color: #6b7280 !important; /* gray-500 */
        }

        /* Additional text visibility fixes */
        .admin-card span, .admin-card p, .admin-card li, .admin-card div {
            color: #374151 !important; /* gray-700 */
        }

        /* Force label visibility */
        label, .form-label {
            color: #1e3a8a !important; /* navy-900 */
            font-weight: 500 !important;
        }

        /* Fix any remaining white text issues */
        main * {
            color: inherit !important;
        }

        /* Override for specific elements that should be dark */
        main .admin-text-primary {
            color: #1e3a8a !important; /* navy-900 */
        }

        main .admin-text-secondary {
            color: #6b7280 !important; /* gray-500 */
        }


    </style>
</head>
<body class="font-hindi antialiased bg-gray-100">
    <div class="min-h-screen flex flex-col" x-data="{ sidebarOpen: false }">
        <!-- Top navigation -->
        @include('admin.partials.topbar')
        
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar navigation -->
            @include('admin.partials.sidebar')
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-4 md:p-6">
                <div class="max-w-7xl mx-auto">
                    @yield('breadcrumbs')

                    <div class="bg-white shadow-lg rounded-lg p-5 mt-4 border text-gray-900">
                        @yield('header')

                        @if(session('success'))
                            <div class="bg-green-100 text-green-800 font-medium p-4 rounded-md mb-6 border-l-4 border-green-500">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="bg-red-100 text-red-800 font-medium p-4 rounded-md mb-6 border-l-4 border-red-500">
                                {{ session('error') }}
                            </div>
                        @endif

                        @yield('content')
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    

    
    @stack('scripts')
</body>
</html> 