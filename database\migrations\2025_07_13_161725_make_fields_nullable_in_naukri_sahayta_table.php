<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('naukri_sahayta', function (Blueprint $table) {
            // Make fields nullable except naam and mobile_number
            $table->string('pita_ka_naam')->nullable()->change();
            $table->text('pata')->nullable()->change();
            $table->integer('umra')->nullable()->change();
            $table->text('ahartayen')->nullable()->change();
            $table->text('ruchi')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('naukri_sahayta', function (Blueprint $table) {
            // Revert fields back to not nullable
            $table->string('pita_ka_naam')->nullable(false)->change();
            $table->text('pata')->nullable(false)->change();
            $table->integer('umra')->nullable(false)->change();
            $table->text('ahartayen')->nullable(false)->change();
            $table->text('ruchi')->nullable(false)->change();
        });
    }
};
