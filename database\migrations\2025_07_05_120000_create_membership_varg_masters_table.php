<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_varg_masters', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'सामान्य सदस्य', 'आजीवन सदस्य', 'संरक्षक सदस्य'
            $table->string('name_english')->nullable(); // English name for reference
            $table->text('description')->nullable(); // Description of membership type
            $table->decimal('fee', 10, 2)->nullable(); // Membership fee if applicable
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_varg_masters');
    }
};
