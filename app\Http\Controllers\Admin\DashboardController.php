<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Membership;
use App\Models\Activity;
use App\Models\News;
use App\Models\User;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        $stats = [
            'users' => User::count(),
            'activities' => Activity::count(),
            'news' => News::count(),
            'memberships' => [
                'total' => Membership::count(),
                'pending' => Membership::where('status', Membership::STATUS_PENDING)->count(),
                'approved' => Membership::where('status', Membership::STATUS_APPROVED)->count(),
                'rejected' => Membership::where('status', Membership::STATUS_REJECTED)->count(),
            ],
            'recent_memberships' => Membership::latest()->take(5)->get(),
        ];

        return view('admin.dashboard', compact('stats'));
    }
}