<?php

namespace App\Http\Controllers\VaivahikAuth;

use App\Http\Controllers\Controller;
use App\Models\VaivahikPanjiyan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Show the login form for vaivahik panjiyan users.
     */
    public function showLoginForm()
    {
        return view('vaivahik-auth.login');
    }

    /**
     * Handle vaivahik panjiyan user login request.
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        // Find user by email
        $user = VaivahikPanjiyan::where('owner_email', $credentials['email'])->first();

        if (!$user || !Hash::check($credentials['password'], $user->owner_password)) {
            throw ValidationException::withMessages([
                'email' => ['ईमेल और पासवर्ड मेल नहीं खाते।'],
            ]);
        }

        // Check if profile is active
        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'email' => ['आपका खाता निष्क्रिय है। कृपया व्यवस्थापक से संपर्क करें।'],
            ]);
        }

        Auth::guard('vaivahik')->login($user, $request->boolean('remember'));

        $request->session()->regenerate();

        return redirect()->intended(route('vaivahik.dashboard'))
            ->with('success', 'सफलतापूर्वक लॉगिन हो गए। स्वागत है, ' . $user->naam . '!');
    }

    /**
     * Handle vaivahik panjiyan user logout request.
     */
    public function logout(Request $request)
    {
        Auth::guard('vaivahik')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')
            ->with('success', 'सफलतापूर्वक लॉगआउट हो गए। धन्यवाद!');
    }

    /**
     * Show registration form - Redirect to main vaivahik panjiyan form
     */
    public function showRegistrationForm()
    {
        return redirect()->route('sadasya.vaivahik-panjiyan.new')
            ->with('info', 'कृपया वैवाहिक पंजीयन फॉर्म भरें। इससे आपका खाता भी बन जाएगा।');
    }

    /**
     * Handle registration - Redirect to main vaivahik panjiyan form
     */
    public function register()
    {
        return redirect()->route('sadasya.vaivahik-panjiyan.new')
            ->with('info', 'कृपया वैवाहिक पंजीयन फॉर्म भरें। इससे आपका खाता भी बन जाएगा।');
    }
}
