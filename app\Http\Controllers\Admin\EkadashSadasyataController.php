<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EkadashSadasyata;

class EkadashSadasyataController extends Controller
{
    /**
     * Display a listing of Ekadash Sadasyata applications.
     */
    public function index(Request $request)
    {
        $status = $request->query('status', 'all');

        $query = EkadashSadasyata::query()
            ->with(['educationQualification', 'officeMaster', 'departmentMaster', 'yadavVarg', 'divisionMaster', 'districtMaster', 'vikaskhandMaster'])
            ->orderBy('created_at', 'desc');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $applications = $query->paginate(15);

        return view('admin.ekadash-sadasyata.index', [
            'applications' => $applications,
            'status' => $status,
            'statusOptions' => [
                'all' => 'All Applications',
                EkadashSadasyata::STATUS_PENDING => 'Pending',
                EkadashSadasyata::STATUS_APPROVED => 'Approved',
                EkadashSadasyata::STATUS_REJECTED => 'Rejected',
            ]
        ]);
    }

    /**
     * Display the specified Ekadash Sadasyata application.
     */
    public function show(string $id)
    {
        $application = EkadashSadasyata::with(['educationQualification', 'officeMaster', 'departmentMaster', 'yadavVarg', 'divisionMaster', 'districtMaster', 'vikaskhandMaster'])
            ->findOrFail($id);

        return view('admin.ekadash-sadasyata.show', compact('application'));
    }

    /**
     * Approve an Ekadash Sadasyata application.
     */
    public function approve(Request $request, string $id)
    {
        $application = EkadashSadasyata::findOrFail($id);

        if ($application->status !== EkadashSadasyata::STATUS_PENDING) {
            return redirect()->back()->with('error', 'Only pending applications can be approved.');
        }

        $application->update([
            'status' => EkadashSadasyata::STATUS_APPROVED,
            'rejection_reason' => null,
        ]);

        // Log the approval action
        \Log::info('Ekadash Sadasyata approved', [
            'application_id' => $application->id,
            'membership_number' => $application->membership_number,
            'member_name' => $application->name,
            'approved_by' => auth()->user()->name,
        ]);

        return redirect()->back()->with('success', 'Application approved successfully.');
    }

    /**
     * Reject an Ekadash Sadasyata application.
     */
    public function reject(Request $request, string $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        $application = EkadashSadasyata::findOrFail($id);

        if ($application->status !== EkadashSadasyata::STATUS_PENDING) {
            return redirect()->back()->with('error', 'Only pending applications can be rejected.');
        }

        $application->update([
            'status' => EkadashSadasyata::STATUS_REJECTED,
            'rejection_reason' => $request->rejection_reason,
        ]);

        // Log the rejection action
        \Log::info('Ekadash Sadasyata rejected', [
            'application_id' => $application->id,
            'membership_number' => $application->membership_number,
            'member_name' => $application->name,
            'rejection_reason' => $request->rejection_reason,
            'rejected_by' => auth()->user()->name,
        ]);

        return redirect()->back()->with('success', 'Application rejected successfully.');
    }

    /**
     * Reset an Ekadash Sadasyata application status to pending.
     */
    public function reset(Request $request, string $id)
    {
        $application = EkadashSadasyata::findOrFail($id);

        if ($application->status === EkadashSadasyata::STATUS_PENDING) {
            return redirect()->back()->with('error', 'Application is already pending.');
        }

        $application->update([
            'status' => EkadashSadasyata::STATUS_PENDING,
            'rejection_reason' => null,
        ]);

        // Log the reset action
        \Log::info('Ekadash Sadasyata reset to pending', [
            'application_id' => $application->id,
            'membership_number' => $application->membership_number,
            'member_name' => $application->name,
            'reset_by' => auth()->user()->name,
        ]);

        return redirect()->back()->with('success', 'Application status reset to pending.');
    }

    /**
     * Remove the specified Ekadash Sadasyata application.
     */
    public function destroy(string $id)
    {
        $application = EkadashSadasyata::findOrFail($id);

        // Delete associated files
        if ($application->photo && \Storage::disk('public')->exists($application->photo)) {
            \Storage::disk('public')->delete($application->photo);
        }

        if ($application->signature && \Storage::disk('public')->exists($application->signature)) {
            \Storage::disk('public')->delete($application->signature);
        }

        // Log the deletion action
        \Log::info('Ekadash Sadasyata deleted', [
            'application_id' => $application->id,
            'membership_number' => $application->membership_number,
            'member_name' => $application->name,
            'deleted_by' => auth()->user()->name,
        ]);

        $application->delete();

        return redirect()->route('admin.ekadash-sadasyata.index')
            ->with('success', 'Application deleted successfully.');
    }
}
