<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'category',
        'title',
        'description',
        'image_path',
        'video_url',
        'event_date',
        'location',
        'display_order',
        'is_published',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'event_date' => 'date',
        'is_published' => 'boolean',
    ];
    
    /**
     * Constants for activity categories to avoid magic strings
     */
    const CATEGORY_SOCIAL = 'social';
    const CATEGORY_CULTURAL = 'cultural';
    const CATEGORY_EDUCATIONAL = 'educational';
    const CATEGORY_GOVERNMENT = 'government';
    const CATEGORY_GALLERY = 'gallery';
    
    /**
     * Get all activities by category, ordered by display_order and event_date
     */
    public static function getByCategory($category, $year = null)
    {
        $query = self::where('category', $category)
            ->where('is_published', true);
            
        if ($year) {
            $query->whereYear('event_date', $year);
        }
            
        return $query->orderBy('display_order')
            ->orderBy('event_date', 'desc')
            ->paginate(10);
    }
    
    /**
     * Get available category options for dropdown
     */
    public static function getCategoryOptions()
    {
        return [
            self::CATEGORY_SOCIAL => 'Social Activities',
            self::CATEGORY_CULTURAL => 'Cultural Activities',
            self::CATEGORY_EDUCATIONAL => 'Educational Activities',
            self::CATEGORY_GOVERNMENT => 'Government Initiatives',
            self::CATEGORY_GALLERY => 'Gallery',
        ];
    }
    
    /**
     * Get the image path with fallback to category-specific placeholder
     */
    public function getImageUrl()
    {
        if ($this->image_path) {
            return asset($this->image_path);
        }
        
        // Return category-specific placeholder
        return asset('images/placeholders/activity-' . $this->category . '.jpg');
    }
    
    /**
     * Get featured activities for all categories for the index page
     */
    public static function getFeaturedActivities()
    {
        $featured = [];
        
        foreach ([
            self::CATEGORY_SOCIAL,
            self::CATEGORY_CULTURAL, 
            self::CATEGORY_EDUCATIONAL,
            self::CATEGORY_GOVERNMENT,
            self::CATEGORY_GALLERY
        ] as $category) {
            $featured[$category] = self::where('category', $category)
                ->where('is_published', true)
                ->orderBy('display_order')
                ->orderBy('event_date', 'desc')
                ->first();
        }
        
        return $featured;
    }

    /**
     * Get category display name in Hindi
     */
    public function getCategoryDisplayName()
    {
        $categoryNames = [
            self::CATEGORY_SOCIAL => 'सामाजिक',
            self::CATEGORY_CULTURAL => 'सांस्कृतिक',
            self::CATEGORY_EDUCATIONAL => 'शैक्षिक',
            self::CATEGORY_GOVERNMENT => 'सरकारी',
            self::CATEGORY_GALLERY => 'गैलरी',
        ];

        return $categoryNames[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Get category color class for badges
     */
    public function getCategoryColorClass()
    {
        $colorClasses = [
            self::CATEGORY_SOCIAL => 'green',
            self::CATEGORY_CULTURAL => 'saffron',
            self::CATEGORY_EDUCATIONAL => 'blue',
            self::CATEGORY_GOVERNMENT => 'navy',
            self::CATEGORY_GALLERY => 'purple',
        ];

        return $colorClasses[$this->category] ?? 'blue';
    }
}
