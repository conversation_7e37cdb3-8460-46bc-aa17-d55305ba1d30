<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class YadavVarg extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_english',
        'description',
        'category',
        'is_active',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active yadav vargs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive yadav vargs.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    /**
     * Get memberships that use this yadav varg.
     */
    public function memberships()
    {
        return $this->hasMany(Membership::class, 'yadav_varg_id');
    }

    /**
     * Get vaivahik panjiyans that use this yadav varg.
     */
    public function vaivahikPanjiyans()
    {
        return $this->hasMany(VaivahikPanjiyan::class, 'yadav_varg_id');
    }
}
