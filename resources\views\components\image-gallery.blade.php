@if(count($images) > 0)
<div class="image-gallery" data-gallery-id="{{ $galleryId }}">
    @if(count($images) === 1)
        <!-- Single Image -->
        <div class="relative">
            <img src="{{ $images[0] }}" alt="{{ $title }}" class="w-full h-64 md:h-96 object-cover rounded-lg cursor-pointer"
                 @if($showLightbox) onclick="openLightbox('{{ $galleryId }}', 0)" @endif>
        </div>
    @else
        <!-- Multiple Images Gallery -->
        <div class="relative">
            <!-- Main Gallery Display -->
            <div id="{{ $galleryId }}-gallery" class="relative h-64 md:h-96 w-full overflow-hidden rounded-lg">
                @foreach($images as $index => $image)
                    <div class="gallery-slide absolute inset-0 w-full h-full transition-opacity duration-500 {{ $index === 0 ? 'opacity-100' : 'opacity-0' }}"
                         data-slide="{{ $index }}">
                        <img src="{{ $image }}" alt="{{ $title }}" class="w-full h-full object-cover cursor-pointer"
                             @if($showLightbox) onclick="openLightbox('{{ $galleryId }}', {{ $index }})" @endif>
                    </div>
                @endforeach
            </div>

            <!-- Gallery Controls -->
            @if(count($images) > 1)
                <button onclick="previousSlide('{{ $galleryId }}')"
                        class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200 z-10">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button onclick="nextSlide('{{ $galleryId }}')"
                        class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all duration-200 z-10">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Gallery Indicators -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                    @foreach($images as $index => $image)
                        <button onclick="showSlide('{{ $galleryId }}', {{ $index }})"
                                class="gallery-indicator w-2 h-2 rounded-full transition-all duration-200 {{ $index === 0 ? 'bg-white' : 'bg-white bg-opacity-50' }}"
                                data-slide="{{ $index }}"></button>
                    @endforeach
                </div>

                <!-- Image Counter -->
                <div class="absolute top-4 right-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full text-sm z-10">
                    <span class="current-slide">1</span> / {{ count($images) }}
                </div>
            @endif
        </div>

        <!-- Thumbnail Strip for Mobile -->
        <div class="md:hidden mt-4 bg-gray-100 dark:bg-navy-700 p-2 rounded-lg">
            <div class="flex space-x-2 overflow-x-auto">
                @foreach($images as $index => $image)
                    <button onclick="showSlide('{{ $galleryId }}', {{ $index }})"
                            class="thumbnail-btn flex-shrink-0 w-16 h-16 rounded overflow-hidden border-2 transition-all duration-200 {{ $index === 0 ? 'border-navy-500' : 'border-transparent' }}"
                            data-slide="{{ $index }}">
                        <img src="{{ $image }}" alt="{{ $title }}" class="w-full h-full object-cover">
                    </button>
                @endforeach
            </div>
        </div>
    @endif
</div>

@if($showLightbox)
    <!-- Lightbox Modal -->
    <div id="{{ $galleryId }}-lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden" style="display: none;">
        <div class="flex items-center justify-center w-full h-full">
        <div class="relative max-w-7xl max-h-full w-full h-full flex items-center justify-center p-4">
            <!-- Close Button -->
            <button onclick="closeLightbox('{{ $galleryId }}')"
                    class="absolute top-4 right-4 text-white hover:text-gray-300 z-60">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Lightbox Image -->
            <img id="{{ $galleryId }}-lightbox-image" src="" alt="{{ $title }}"
                 class="max-w-full max-h-full object-contain">

            @if(count($images) > 1)
                <!-- Lightbox Controls -->
                <button onclick="previousLightboxSlide('{{ $galleryId }}')"
                        class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-60">
                    <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button onclick="nextLightboxSlide('{{ $galleryId }}')"
                        class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-60">
                    <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Lightbox Counter -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-4 py-2 rounded-full">
                    <span id="{{ $galleryId }}-lightbox-counter">1</span> / {{ count($images) }}
                </div>
            @endif
        </div>
    </div>
@endif
@endif

@push('scripts')
<script>
    // Gallery functionality
    if (typeof window.galleries === 'undefined') {
        window.galleries = {};
    }

    // Initialize this specific gallery
    (function() {
        const galleryId = '{{ $galleryId }}';
        const images = @json($images);

        window.galleries[galleryId] = {
            currentSlide: 0,
            totalSlides: images.length,
            images: images
        };
    })();

    function initGallery(galleryId) {
        if (!window.galleries[galleryId]) {
            const slides = document.querySelectorAll(`#${galleryId}-gallery .gallery-slide`);
            window.galleries[galleryId] = {
                currentSlide: 0,
                totalSlides: slides.length,
                images: []
            };
        }
        return window.galleries[galleryId];
    }

    window.showSlide = function(galleryId, index) {
        const gallery = initGallery(galleryId);

        // Hide all slides
        const slides = document.querySelectorAll(`#${galleryId}-gallery .gallery-slide`);
        slides.forEach(slide => {
            slide.classList.remove('opacity-100');
            slide.classList.add('opacity-0');
        });

        // Show current slide
        if (slides[index]) {
            slides[index].classList.remove('opacity-0');
            slides[index].classList.add('opacity-100');
        }

        // Update indicators
        const indicators = document.querySelectorAll(`[data-gallery-id="${galleryId}"] .gallery-indicator`);
        indicators.forEach((indicator, i) => {
            if (i === index) {
                indicator.classList.remove('bg-opacity-50');
                indicator.classList.add('bg-white');
            } else {
                indicator.classList.add('bg-opacity-50');
                indicator.classList.remove('bg-white');
            }
        });

        // Update thumbnails
        const thumbnails = document.querySelectorAll(`[data-gallery-id="${galleryId}"] .thumbnail-btn`);
        thumbnails.forEach((thumb, i) => {
            if (i === index) {
                thumb.classList.remove('border-transparent');
                thumb.classList.add('border-navy-500');
            } else {
                thumb.classList.add('border-transparent');
                thumb.classList.remove('border-navy-500');
            }
        });

        // Update counter
        const counter = document.querySelector(`[data-gallery-id="${galleryId}"] .current-slide`);
        if (counter) counter.textContent = index + 1;

        gallery.currentSlide = index;
    };

    window.nextSlide = function(galleryId) {
        const gallery = initGallery(galleryId);
        const next = (gallery.currentSlide + 1) % gallery.totalSlides;
        showSlide(galleryId, next);
    };

    window.previousSlide = function(galleryId) {
        const gallery = initGallery(galleryId);
        const prev = (gallery.currentSlide - 1 + gallery.totalSlides) % gallery.totalSlides;
        showSlide(galleryId, prev);
    };

    // Lightbox functionality
    window.openLightbox = function(galleryId, index) {
        const gallery = initGallery(galleryId);
        const lightbox = document.getElementById(`${galleryId}-lightbox`);
        const lightboxImage = document.getElementById(`${galleryId}-lightbox-image`);

        if (lightbox && lightboxImage && gallery.images[index]) {
            lightboxImage.src = gallery.images[index];
            lightbox.style.display = 'block';
            lightbox.classList.remove('hidden');
            gallery.currentSlide = index;

            // Update lightbox counter
            const counter = document.getElementById(`${galleryId}-lightbox-counter`);
            if (counter) counter.textContent = index + 1;

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    };

    window.closeLightbox = function(galleryId) {
        const lightbox = document.getElementById(`${galleryId}-lightbox`);
        if (lightbox) {
            lightbox.style.display = 'none';
            lightbox.classList.add('hidden');
            document.body.style.overflow = '';
        }
    };

    window.nextLightboxSlide = function(galleryId) {
        const gallery = initGallery(galleryId);
        const next = (gallery.currentSlide + 1) % gallery.totalSlides;
        openLightbox(galleryId, next);
    };

    window.previousLightboxSlide = function(galleryId) {
        const gallery = initGallery(galleryId);
        const prev = (gallery.currentSlide - 1 + gallery.totalSlides) % gallery.totalSlides;
        openLightbox(galleryId, prev);
    };

    // Keyboard navigation for lightbox
    document.addEventListener('keydown', function(e) {
        const openLightbox = document.querySelector('[id$="-lightbox"]:not(.hidden)');
        if (openLightbox) {
            const galleryId = openLightbox.id.replace('-lightbox', '');

            if (e.key === 'Escape') {
                closeLightbox(galleryId);
            } else if (e.key === 'ArrowLeft') {
                previousLightboxSlide(galleryId);
            } else if (e.key === 'ArrowRight') {
                nextLightboxSlide(galleryId);
            }
        }
    });

    // Close lightbox when clicking outside image
    document.addEventListener('click', function(e) {
        if (e.target.id && e.target.id.endsWith('-lightbox')) {
            const galleryId = e.target.id.replace('-lightbox', '');
            closeLightbox(galleryId);
        }
    });
</script>
@endpush