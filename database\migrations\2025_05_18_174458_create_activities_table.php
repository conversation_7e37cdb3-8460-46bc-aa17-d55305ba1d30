<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->string('category'); // social, cultural, educational, government, gallery
            $table->string('title');
            $table->text('description');
            $table->string('image_path')->nullable(); // Optional image
            $table->date('event_date')->nullable(); // Date of the event/activity
            $table->string('location')->nullable(); // Location of activity/event
            $table->integer('display_order')->default(0); // For ordering activities
            $table->boolean('is_published')->default(true);
            $table->timestamps();
            
            // Add index for faster searching
            $table->index(['category', 'is_published']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activities');
    }
};
