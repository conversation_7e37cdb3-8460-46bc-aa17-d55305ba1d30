@extends('layouts.admin') @section('title', 'विकासखंड प्रबंधन') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">विकासखंड प्रबंधन</h1> <p class="mt-1 text-sm admin-text-secondary">विकासखंडों का प्रबंधन करें</p> </div> <div class="mt-4 sm:mt-0"> <a href="{{ route('admin.vikaskhand-masters.create') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path> </svg> नया विकासखंड जोड़ें </a> </div> </div> </div> <!-- Search and Filter --> <div class="admin-card p-6 mb-6"> <form method="GET" action="{{ route('admin.vikaskhand-masters.index') }}" class="space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4"> <div class="flex-1"> <input type="text" name="search" value="{{ request('search') }}" placeholder="विकासखंड कोड, अंग्रेजी नाम या हिंदी नाम खोजें..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> </div> <div> <select name="district_lgd_code" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी जिले</option> @foreach($districts as $district) <option value="{{ $district->district_lgd_code }}" {{ request('district_lgd_code') === $district->district_lgd_code ? 'selected' : '' }}> {{ $district->district_name_hin }} </option> @endforeach </select> </div> <div> <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी स्थिति</option> <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>सक्रिय</option> <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>निष्क्रिय</option> </select> </div> <div class="flex space-x-2"> <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> खोजें </button> <a href="{{ route('admin.vikaskhand-masters.index') }}" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> रीसेट </a> </div> </form> </div> <!-- Table --> <div class="admin-card overflow-hidden"> <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">विकासखंड</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">LGD कोड</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">अंग्रेजी नाम</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">जिला</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @forelse($vikaskhands as $vikaskhand) <tr class="hover:bg-gray-50:bg-navy-700"> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm font-medium admin-text-primary">{{ $vikaskhand->sub_district_name_hin }}</div> </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $vikaskhand->sub_district_lgd_code }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $vikaskhand->sub_district_name_eng }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $vikaskhand->district ? $vikaskhand->district->district_name_hin : '-' }} </td> <td class="px-6 py-4 whitespace-nowrap"> @if($vikaskhand->is_active) <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"> सक्रिय </span> @else <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"> निष्क्रिय </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex items-center space-x-3"> <a href="{{ route('admin.vikaskhand-masters.show', $vikaskhand->id) }}" class="text-blue-600 hover:text-blue-900:text-blue-300" title="विवरण देखें"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg> </a> <a href="{{ route('admin.vikaskhand-masters.edit', $vikaskhand->id) }}" class="text-indigo-600 hover:text-indigo-900:text-indigo-300" title="संपादित करें"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> </a> <form method="POST" action="{{ route('admin.vikaskhand-masters.destroy', $vikaskhand->id) }}" class="inline" onsubmit="return confirm('क्या आप वाकई इस विकासखंड को हटाना चाहते हैं?')"> @csrf @method('DELETE') <button type="submit" class="text-red-600 hover:text-red-900:text-red-300" title="हटाएं"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path> </svg> </button> </form> </div> </td> </tr> @empty <tr> <td colspan="6" class="px-6 py-4 text-center text-sm admin-text-secondary"> कोई विकासखंड नहीं मिला। </td> </tr> @endforelse </tbody> </table> </div> <!-- Pagination --> @if($vikaskhands->hasPages()) <div class="px-6 py-4 border-t border-gray-200"> {{ $vikaskhands->links() }} </div> @endif </div> </div> </div> @endsection 