<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="छ. ग. यादव शासकीय सेवक संघ - छत्तीसगढ़ का प्रमुख यादव सरकारी कर्मचारी संगठन। शासकीय सेवकों के हित और विकास के लिए समर्पित। रायपुर में स्थित। | CG Yadav Government Service Committee - Leading organization for Yadav government employees in Chhattisgarh. Dedicated to welfare and development of government servants. Located in Raipur.">
    <meta name="keywords"
        content="छ. ग. यादव शासकीय सेवक संघ, cg yadav government service committee, छत्तीसगढ़ यादव शासकीय संगठन, chhattisgarh yadav government organization, यादव सरकारी कर्मचारी संघ, yadav government employees association, शासकीय सेवक यादव समिति, government servant yadav committee, रायपुर यादव शासकीय समिति, raipur yadav official committee, छत्तीसगढ़ यादव सेवा समिति, chhattisgarh yadav seva samiti, यादव अधिकारी संगठन, yadav officers organization">
    <meta name="author" content="यादव समाज | Yadav Samaj">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="google" content="notranslate">
    <meta name="language" content="hi, en">
    <link rel="alternate" hreflang="hi" href="{{ url()->current() }}?lang=hi">
    <link rel="alternate" hreflang="en" href="{{ url()->current() }}?lang=en">
    <link rel="alternate" hreflang="x-default" href="{{ url()->current() }}">
    <meta property="og:title"
        content="छ. ग. यादव शासकीय सेवक संघ | CG Yadav Government Service Committee - @yield('title', 'होमपेज | Homepage')">
    <meta property="og:description"
        content="छ. ग. यादव शासकीय सेवक संघ - छत्तीसगढ़ का प्रमुख यादव सरकारी कर्मचारी संगठन। शासकीय सेवकों के हित और विकास के लिए समर्पित। रायपुर में स्थित। | CG Yadav Government Service Committee - Leading organization for Yadav government employees in Chhattisgarh. Dedicated to welfare and development of government servants. Located in Raipur.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:image" content="{{ asset('images/logo-yadav-samaj.png') }}">
    <meta property="og:site_name" content="छ. ग. यादव शासकीय सेवक संघ | CG Yadav Government Service Committee">
    <meta property="og:locale" content="hi_IN" />
    <meta property="og:locale:alternate" content="en_US" />

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title"
        content="छ. ग. यादव शासकीय सेवक संघ | CG Yadav Government Service Committee - @yield('title', 'होमपेज | Homepage')">
    <meta name="twitter:description"
        content="छ. ग. यादव शासकीय सेवक संघ - छत्तीसगढ़ का प्रमुख यादव सरकारी कर्मचारी संगठन। शासकीय सेवकों के हित और विकास के लिए समर्पित। रायपुर में स्थित। | CG Yadav Government Service Committee - Leading organization for Yadav government employees in Chhattisgarh. Dedicated to welfare and development of government servants. Located in Raipur.">
    <meta name="twitter:image" content="{{ asset('images/logo-yadav-samaj.png') }}">

    <meta name="geo.region" content="IN-CT" />
    <meta name="geo.placename" content="Raipur, Chhattisgarh" />
    <meta name="geo.position" content="21.2514;81.6296" />
    <meta name="ICBM" content="21.2514, 81.6296" />

    <meta name="theme-color" content="#0a1f44">
    <meta name="msapplication-TileColor" content="#0a1f44">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="canonical" href="{{ url()->current() }}">
    <title>छ. ग. यादव शासकीय सेवक संघ | CG Yadav Government Service Committee - @yield('title', 'होमपेज | Homepage') |
        छत्तीसगढ़ का
        प्रमुख यादव सरकारी कर्मचारी संगठन | Leading Organization for Yadav Government Employees in Chhattisgarh</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Bootstrap Icons (for social/contact icons) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Fonts and Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        /* Fix hover gap issue - create seamless hover areas */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 200px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 50;
            display: none;
            margin-top: -2px;
            /* Eliminate gap */
            padding-top: 4px;
            /* Add padding to create seamless hover area */
        }

        /* Show dropdown on hover with seamless transition */
        .dropdown:hover .dropdown-menu {
            display: block;
        }

        /* Dropdown submenu styles with gap fix */
        .dropdown-submenu {
            position: relative;
        }

        .dropdown-submenu-content {
            position: absolute;
            left: 100%;
            top: -4px;
            /* Align with parent item */
            min-width: 200px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 51;
            display: none;
            margin-left: -4px;
            /* Eliminate gap */
            padding-left: 6px;
            /* Create seamless hover area */
        }

        /* Show submenu on hover */
        .dropdown-submenu:hover .dropdown-submenu-content {
            display: block;
        }

        /* Enhanced dropdown item styling */
        .dropdown-submenu-content .dropdown-item {
            padding: 0.75rem 1rem;
            display: block;
            color: #1e293b;
            text-decoration: none;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 2px 4px;
        }

        .dropdown-submenu-content .dropdown-item:hover {
            background: linear-gradient(135deg, #fff8ee 0%, #ffefd7 100%);
            color: #ff9933;
            transform: translateX(4px);
        }

        /* Compact navigation for small laptops */
        @media (min-width: 1024px) and (max-width: 1280px) {
            .nav-link {
                font-size: 0.875rem !important;
                padding: 0.5rem 0.75rem !important;
            }

            .dropdown-menu {
                min-width: 180px !important;
            }

            .dropdown-item {
                font-size: 0.875rem !important;
                padding: 0.5rem 0.75rem !important;
            }
        }

        .dropdown-submenu-content .dropdown-item:last-child {
            border-bottom: none;
        }

        /* Marquee animations for latest activities */
        .marquee-container {
            position: relative;
        }

        .marquee-content {
            display: inline-block;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        @keyframes marquee-slow {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        .animate-marquee {
            animation: marquee 20s linear infinite;
        }

        .animate-marquee-slow {
            animation: marquee-slow 30s linear infinite;
        }

        /* Pause animation on hover */
        .marquee-container:hover .marquee-content {
            animation-play-state: paused;
        }

        /* Responsive marquee speed */
        @media (max-width: 768px) {
            .animate-marquee {
                animation-duration: 15s;
            }

            .animate-marquee-slow {
                animation-duration: 25s;
            }
        }
    </style>
</head>

<body class="font-hindi bg-white text-gray-800 min-h-screen flex flex-col antialiased">




    {{-- Modern Header Section with Enhanced Details --}}
    <div class="modern-govt-header">
        <!-- Top Info Bar -->
        <div class="bg-gradient-to-r from-navy-800 via-navy-700 to-navy-800 text-white py-2 hidden md:block">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center text-sm">
                    <!-- Left: Date & Time -->
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-saffron-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span id="current-date" class="font-medium"></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-saffron-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span id="current-time" class="font-medium"></span>
                        </div>
                    </div>

                    <!-- Right: Contact Info -->
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-saffron-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <span class="font-medium">+91 98765 43210</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-saffron-400" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span class="font-medium"><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="bg-gradient-to-br from-white via-gray-50 to-white py-1 border-b-3 border-saffron-500 shadow-lg">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between">
                    <!-- Enhanced Left Logo -->
                    <div class="flex-shrink-0 group">
                        <div class="relative">
                            <div
                                class="w-20 h-20 lg:w-24 lg:h-24 modern-logo-container flex items-center justify-center overflow-hidden transform transition-transform duration-300 group-hover:scale-105">
                                <img src="{{ asset('images/logo-yadav-samaj.png') }}" alt="यादव समाज Logo"
                                    class="w-full h-full object-cover rounded-full shadow-lg">
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Main Title Section -->
                    <div class="text-center flex-1 px-4">
                        <div class="relative">
                            <h1
                                class="modern-govt-title mb-2 bg-gradient-to-r from-navy-800 via-navy-700 to-navy-800 bg-clip-text text-transparent">
                                छ. ग. यादव शासकीय सेवक संघ
                            </h1>
                            {{-- <div class="hidden lg:block">
                                <p class="modern-govt-description text-saffron-600 font-medium">
                                    उपसमिति - एकादश उत्थान यादव कल्याण
                                </p>
                                <div class="flex justify-center mt-2">
                                    <div class="w-24 h-1 bg-gradient-to-r from-saffron-400 to-orange-500 rounded-full">
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>

                    <!-- Enhanced Right Logo -->
                    <div class="flex-shrink-0 group">
                        <div class="relative">
                            <div
                                class="w-20 h-20 lg:w-24 lg:h-24 modern-logo-container flex items-center justify-center overflow-hidden transform transition-transform duration-300 group-hover:scale-105">
                                <img src="{{ asset('images/cgyss-logo2.jpg') }}" alt="Yadav Samaj Logo 2"
                                    class="w-full h-full object-cover rounded-full shadow-lg">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <header
        class="navbar-header bg-gradient-to-r from-navy-50 via-white to-navy-50 shadow-lg sticky top-0 z-50 border-b-2 border-saffron-200 backdrop-blur-sm">
        <div class="container-custom"></div>
        <nav class="flex items-center justify-between py-2">
            <!-- Mobile Logo (Only visible on mobile) -->
            <div class="flex items-center lg:hidden">
                <a href="/" class="flex items-center group">
                    <div class="flex-shrink-0 mr-3">
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-saffron-500 to-saffron-600 rounded-lg flex items-center justify-center overflow-hidden">
                            <img src="{{ asset('images/logo-yadav-samaj.png') }}" alt="यादव समाज Logo"
                                class="w-full h-full object-cover rounded-lg">
                        </div>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-navy-900 leading-tight">36 Ysss</h1>
                        <p class="text-xs text-navy-600">रायपुर</p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation - Compact Government Menu Format -->
            <div class="hidden lg:flex items-center justify-center flex-1">
                <div class="flex items-center space-x-0.5 xl:space-x-1 2xl:space-x-2">

                    <a href="/" class="nav-link {{ request()->is('/') ? 'nav-link-active' : '' }}">होमपेज</a>

                    <!-- हमारे बारे में Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('humare-bare-me*') ? 'nav-link-active' : '' }}">
                            हमारे बारे में
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/humare-bare-me/uddeshya-aur-itihas" class="dropdown-item">
                                    उद्देश्य और इतिहास
                                </a>
                                <!-- प्रमुख पदाधिकारी with submenu -->
                                <div class="dropdown-submenu">
                                    <a href="/humare-bare-me/pramukh-padadhikari"
                                        class="dropdown-item flex items-center justify-between">
                                        प्रमुख पदाधिकारी
                                        <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                    <div class="dropdown-submenu-content">
                                        <div class="py-2">
                                            <a href="/humare-bare-me/pramukh-padadhikari" class="dropdown-item text-sm">
                                                सभी पदाधिकारी
                                            </a>
                                            <a href="/pramukh-padadhikari/sambhag-wise" class="dropdown-item text-sm">
                                                संभाग स्तर
                                            </a>
                                            <a href="/pramukh-padadhikari/jila-wise" class="dropdown-item text-sm">
                                                जिला स्तर
                                            </a>
                                            <a href="/pramukh-padadhikari/vikaskhand-wise"
                                                class="dropdown-item text-sm">
                                                विकासखंड स्तर
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <a href="/humare-bare-me/sanrachna-karyapranali" class="dropdown-item">
                                    संरचना/कार्यप्रणाली
                                </a>
                                <a href="/humare-bare-me/sanghatan-dastavej" class="dropdown-item">
                                    संगठन के दस्तावेज
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- गतिविधियाँ Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('activities*') ? 'nav-link-active' : '' }}">
                            गतिविधियाँ
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/activities" class="dropdown-item">सभी गतिविधियाँ</a>
                                <a href="/activities/social" class="dropdown-item">सामाजिक कार्य</a>
                                <a href="/activities/cultural" class="dropdown-item">सांस्कृतिक कार्यक्रम</a>
                                <a href="/activities/educational" class="dropdown-item">शैक्षिक कार्यक्रम</a>
                                <a href="/activities/gallery" class="dropdown-item">गैलरी</a>
                            </div>
                        </div>
                    </div>

                    <!-- विवाह-बंधन -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('vaivahik*') ? 'nav-link-active' : '' }}">
                            विवाह-बंधन
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/vaivahik-sadasya-panjiyan" class="dropdown-item">अविवाहित युवक-युवतियां</a>
                            </div>
                        </div>
                    </div>

                    <!-- सदस्यता Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('sadasya*') ? 'nav-link-active' : '' }}">
                            सदस्यता
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/sadasya/aavedan" class="dropdown-item">सदस्यता आवेदन</a>
                                <a href="/sadasya/prakriya" class="dropdown-item">सदस्यता प्रक्रिया</a>
                                <a href="/sadasya/suchana" class="dropdown-item">सदस्यता सूचना</a>
                            </div>
                        </div>
                    </div>

                    <!-- एकादश सदस्यता Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('ekadash-sadasyata*') ? 'nav-link-active' : '' }}">
                            एकादश सदस्यता
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/ekadash-sadasyata/aavedan" class="dropdown-item">एकादश सदस्यता आवेदन</a>
                                <a href="/ekadash-sadasyata/members" class="dropdown-item">सदस्य सूची</a>
                            </div>
                        </div>
                    </div>

                    <!-- यादव व्यापार एवं यादव ग्राहक Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('yadav-vyapar*') || request()->is('anya-seva/yadav-vyapar-grahak*') ? 'nav-link-active' : '' }}">
                            यादव व्यापार एवं यादव ग्राहक
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>

                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="{{ route('anya-seva.yadav-vyapar-grahak') }}" class="dropdown-item">जानकारी
                                    जोड़ें</a>
                                <a href="{{ route('yadav-vyapar-grahak-reports') }}" class="dropdown-item">रिपोर्ट
                                    देखें</a>
                            </div>
                        </div>
                    </div>

                       <!-- अन्य सेवाएं Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('others*') || request()->is('dastavej*') || (request()->is('anya-seva*') && !request()->is('anya-seva/yadav-vyapar-grahak*')) || request()->is('darshanik-ishthal*') ? 'nav-link-active' : '' }}">
                            अन्य सेवाएं
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <!-- दार्शनिक स्थल Submenu -->
                                <div class="dropdown-submenu">
                                    <button class="dropdown-item flex items-center justify-between w-full">
                                        दार्शनिक स्थल
                                        <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div class="dropdown-submenu-content">
                                        <a href="{{ route('anya-seva.darshanik-ishthal') }}"
                                            class="dropdown-item">जानकारी जोड़ें</a>
                                        <a href="{{ route('darshanik-ishthal-reports') }}" class="dropdown-item">रिपोर्ट
                                            देखें</a>
                                    </div>
                                </div>

                                <!-- नौकरी सहायता Submenu -->
                                <div class="dropdown-submenu">
                                    <button class="dropdown-item flex items-center justify-between w-full">
                                        नौकरी/रोजगार/स्वरोजगार सहायता
                                        <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div class="dropdown-submenu-content">
                                        <a href="{{ route('anya-seva.naukri-sahayta') }}" class="dropdown-item">आवेदन
                                            करें</a>
                                        <a href="{{ route('naukri-sahayta-reports') }}" class="dropdown-item">रिपोर्ट
                                            देखें</a>
                                    </div>
                                </div>

                                <!-- मल्टीनेशनल कंपनी Submenu -->
                                <div class="dropdown-submenu">
                                    <button class="dropdown-item flex items-center justify-between w-full">
                                        मल्टीनेशनल कंपनी
                                        <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div class="dropdown-submenu-content">
                                        <a href="{{ route('anya-seva.multinational-company') }}"
                                            class="dropdown-item">जानकारी जोड़ें</a>
                                        <a href="{{ route('multinational-company-reports') }}"
                                            class="dropdown-item">रिपोर्ट देखें</a>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>

                    <!-- समाचार एवं सूचनाएँ Dropdown -->
                    <div class="relative dropdown group">
                        <button
                            class="nav-link flex items-center {{ request()->is('news*') ? 'nav-link-active' : '' }}">
                            समाचार/सूचनाएँ
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="h-3 w-3 ml-1 transition-transform duration-300 group-hover:rotate-180"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block">
                            <div class="py-2">
                                <a href="/news" class="dropdown-item">सभी समाचार</a>
                                <a href="/news/general" class="dropdown-item">सामान्य समाचार</a>
                                <a href="/news/meetings" class="dropdown-item">बैठकें</a>
                                <a href="/news/appointments" class="dropdown-item">नियुक्तियाँ</a>
                                <a href="/news/press" class="dropdown-item">प्रेस विज्ञप्ति</a>
                            </div>
                        </div>
                    </div>



                    <!-- संपर्क -->
                    <a href="/sampark"
                        class="nav-link {{ request()->is('sampark*') ? 'nav-link-active' : '' }}">संपर्क</a>
                 

                </div>
            </div>
            <!-- User Status & CTA Section (Desktop Only) -->
            <div class="hidden lg:flex items-center space-x-4">
                <!-- Member Login Status -->
                @auth('member')
                    <div class="relative dropdown group">
                        <button
                            class="flex items-center space-x-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="text-left">
                                <div class="text-sm font-medium text-navy-800">
                                    {{ Auth::guard('member')->user()->full_name }}</div>
                                <div class="text-xs text-blue-600">सदस्य</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block right-0">
                            <div class="py-2">
                                <a href="{{ route('member.dashboard') }}" class="dropdown-item">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                                    </svg>
                                    डैशबोर्ड
                                </a>
                                <a href="{{ route('member.profile') }}" class="dropdown-item">
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    प्रोफाइल
                                </a>
                                <div class="border-t border-gray-100 my-1"></div>
                                <form method="POST" action="{{ route('member.logout') }}" class="inline">
                                    @csrf
                                    <button type="submit"
                                        class="dropdown-item text-red-600 hover:bg-red-50 w-full text-left">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"
                                                clip-rule="evenodd" />
                                        </svg>
                                        लॉगआउट
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Member Login Button -->
                    <a href="{{ route('member.login') }}"
                        class="flex items-center space-x-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm font-medium text-blue-600">सदस्य लॉगिन</span>
                    </a>
                @endauth

                <!-- Vaivahik Login Status -->
                @auth('vaivahik')
                    <div class="relative dropdown group">
                        <button
                            class="flex items-center space-x-2 px-4 py-2 bg-pink-50 hover:bg-pink-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" />
                                </svg>
                            </div>
                            <div class="text-left">
                                <div class="text-sm font-medium text-navy-800">{{ Auth::guard('vaivahik')->user()->naam }}
                                </div>
                                <div class="text-xs text-pink-600">वैवाहिक सदस्य</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div class="dropdown-menu group-hover:block right-0">
                            <div class="py-2">
                                <a href="{{ route('vaivahik.dashboard') }}" class="dropdown-item">
                                    <svg class="w-4 h-4 mr-2 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                                    </svg>
                                    डैशबोर्ड
                                </a>
                                <a href="{{ route('vaivahik.edit-profile') }}" class="dropdown-item">
                                    <svg class="w-4 h-4 mr-2 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    प्रोफाइल संपादित करें
                                </a>
                                <div class="border-t border-gray-100 my-1"></div>
                                <form method="POST" action="{{ route('vaivahik.logout') }}" class="inline">
                                    @csrf
                                    <button type="submit"
                                        class="dropdown-item text-red-600 hover:bg-red-50 w-full text-left">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"
                                                clip-rule="evenodd" />
                                        </svg>
                                        लॉगआउट
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Vaivahik Login Button -->
                    <a href="{{ route('vaivahik.login') }}"
                        class="flex items-center space-x-2 px-4 py-2 bg-pink-50 hover:bg-pink-100 rounded-lg transition-colors">
                        <svg class="w-4 h-4 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" />
                        </svg>
                        <span class="text-sm font-medium text-pink-600">वैवाहिक लॉगिन</span>
                    </a>
                @endauth
            </div>

            <!-- Mobile Menu Button -->
            <div class="lg:hidden">
                <button id="mobile-menu-button"
                    class="hamburger flex flex-col justify-center items-center w-12 h-12 bg-gradient-to-br from-navy-50 to-navy-100 rounded-lg hover:from-saffron-50 hover:to-saffron-100 transition-all duration-300 shadow-md"
                    aria-label="Menu">
                    <div class="w-6 h-0.5 bg-navy-700 mb-1.5 transition-colors"></div>
                    <div class="w-6 h-0.5 bg-navy-700 mb-1.5 transition-colors"></div>
                    <div class="w-6 h-0.5 bg-navy-700 transition-colors"></div>
                </button>
            </div>
        </nav>
        </div>
    </header>

    <!-- Mobile Menu & Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-navy-900 bg-opacity-50 z-40 hidden"></div>
    <div id="mobile-menu"
        class="fixed top-0 left-0 h-full w-4/5 max-w-sm bg-white z-50 overflow-y-auto shadow-elevated border-r border-gray-100">
        <div class="p-5 border-b border-gray-100 flex justify-between items-center">
            <div class="flex items-center">
                <div
                    class="w-12 h-12 bg-gradient-to-br from-saffron-500 to-saffron-600 rounded-lg flex items-center justify-center overflow-hidden mr-3">
                    <img src="{{ asset('images/logo-yadav-samaj.png') }}" alt="यादव समाज Logo"
                        class="w-full h-full object-cover rounded-lg">
                </div>
                <div>
                    <div class="text-lg font-bold text-navy-900">छ. ग. यादव शासकीय सेवक समिति</div>
                    <div class="text-sm text-navy-600">रायपुर</div>
                </div>
            </div>
            <button id="mobile-menu-close" class="w-8 h-8 flex items-center justify-center rounded-md hover:bg-navy-50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-navy-800" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="p-5">
            <!-- Mobile Navigation - Matches Government Menu -->
            <nav class="space-y-3">
                <a href="/" class="block py-2 border-b border-gray-100 text-navy-800 hover:text-saffron-500">होमपेज</a>

                <!-- हमारे बारे में Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>हमारे बारे में</span>
                    </button>
                    <div class="pl-4 hidden">
                        <a href="/humare-bare-me/uddeshya-aur-itihas"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">उद्देश्य और
                            इतिहास</a>

                        <!-- प्रमुख पदाधिकारी Mobile Submenu -->
                        <div class="py-2 border-b border-gray-100">
                            <button
                                class="dropdown-toggle flex justify-between items-center w-full text-navy-700 hover:text-blue-600">
                                <span>प्रमुख पदाधिकारी</span>
                            </button>
                            <div class="pl-4 hidden">
                                <a href="/humare-bare-me/pramukh-padadhikari"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-blue-600">सभी
                                    पदाधिकारी</a>
                                <a href="/pramukh-padadhikari/sambhag-wise"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-blue-600">संभाग
                                    स्तर</a>
                                <a href="/pramukh-padadhikari/jila-wise"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-blue-600">जिला
                                    स्तर</a>
                                <a href="/pramukh-padadhikari/vikaskhand-wise"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-blue-600">विकासखंड
                                    स्तर</a>
                            </div>
                        </div>

                        <a href="/humare-bare-me/sanrachna-karyapranali"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">संरचना/कार्यप्रणाली</a>
                        <a href="/humare-bare-me/sanghatan-dastavej"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">संगठन के
                            दस्तावेज</a>
                    </div>
                </div>

                <!-- गतिविधियाँ Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>गतिविधियाँ</span>
                    </button>
                    <div class="pl-4 hidden">
                        <a href="/activities"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">सभी
                            गतिविधियाँ</a>
                        <a href="/activities/social"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">सामाजिक
                            कार्य</a>
                        <a href="/activities/cultural"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">सांस्कृतिक
                            कार्यक्रम</a>
                        <a href="/activities/gallery"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-blue-600">गैलरी</a>
                    </div>
                </div>

                <!-- विवाह-बंधन -->
                <a href="/vaivahik-sadasya-panjiyan"
                    class="block py-2 border-b border-gray-100 text-navy-800 hover:text-blue-600">विवाह-बंधन</a>

                <!-- समाचार एवं सूचनाएँ Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>समाचार/सूचनाएँ</span>
                    </button>
                    <div class="pl-4 hidden">
                        <a href="/news"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सभी
                            समाचार</a>
                        <a href="/news/general"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सामान्य
                            समाचार</a>
                        <a href="/news/meetings"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">बैठकें</a>
                    </div>
                </div>

                <!-- सदस्यता Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>सदस्यता</span>
                    </button>
                    <div class="pl-4 hidden">
                        <a href="/sadasya/aavedan"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सदस्यता
                            आवेदन</a>
                        <a href="/sadasya/prakriya"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सदस्यता
                            प्रक्रिया</a>
                        <a href="/sadasya/suchana"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सदस्यता
                            सूचि</a>
                    </div>
                </div>

                <!-- एकादश सदस्यता Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>एकादश सदस्यता</span>
                    </button>
                    <div class="pl-4 hidden">
                        <a href="/ekadash-sadasyata/aavedan"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">एकादश
                            सदस्यता आवेदन</a>
                        <a href="/ekadash-sadasyata/members"
                            class="block py-2 border-b border-gray-100 text-navy-700 hover:text-saffron-500">सदस्य
                            सूची</a>
                    </div>
                </div>

                <!-- संपर्क -->
                <a href="/sampark"
                    class="block py-2 border-b border-gray-100 text-navy-800 hover:text-saffron-500">संपर्क</a>

                <!-- अन्य सेवाएं Dropdown -->
                <div>
                    <button
                        class="dropdown-toggle flex justify-between items-center w-full py-2 border-b border-gray-100 text-navy-800">
                        <span>अन्य सेवाएं</span>
                    </button>
                    <div class="pl-4 hidden">

                        <!-- दार्शनिक स्थल Mobile Submenu -->
                        <div class="py-2 border-b border-gray-100">
                            <button
                                class="dropdown-toggle flex justify-between items-center w-full text-navy-700 hover:text-saffron-500">
                                <span>दार्शनिक स्थल</span>
                            </button>
                            <div class="pl-4 hidden">
                                <a href="{{ route('anya-seva.darshanik-ishthal') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">जानकारी
                                    जोड़ें</a>
                                <a href="{{ route('darshanik-ishthal-reports') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">रिपोर्ट
                                    देखें</a>
                            </div>
                        </div>

                        <!-- नौकरी सहायता Mobile Submenu -->
                        <div class="py-2 border-b border-gray-100">
                            <button
                                class="dropdown-toggle flex justify-between items-center w-full text-navy-700 hover:text-saffron-500">
                                <span>नौकरी/रोजगार/स्वरोजगार सहायता</span>
                            </button>
                            <div class="pl-4 hidden">
                                <a href="{{ route('anya-seva.naukri-sahayta') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">आवेदन
                                    करें</a>
                                <a href="{{ route('naukri-sahayta-reports') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">रिपोर्ट
                                    देखें</a>
                            </div>
                        </div>

                        <!-- मल्टीनेशनल कंपनी Mobile Submenu -->
                        <div class="py-2 border-b border-gray-100">
                            <button
                                class="dropdown-toggle flex justify-between items-center w-full text-navy-700 hover:text-saffron-500">
                                <span>मल्टीनेशनल कंपनी</span>
                            </button>
                            <div class="pl-4 hidden">
                                <a href="{{ route('anya-seva.multinational-company') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">जानकारी
                                    जोड़ें</a>
                                <a href="{{ route('multinational-company-reports') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500">रिपोर्ट
                                    देखें</a>
                            </div>
                        </div>

                        <!-- यादव व्यापार Mobile Submenu -->
                        <div class="py-2 border-b border-gray-100">
                            <button
                                class="dropdown-toggle flex justify-between items-center w-full text-navy-700 hover:text-saffron-500 {{ Request::routeIs('anya-seva.yadav-vyapar-grahak*') || Request::routeIs('yadav-vyapar-grahak-reports*') ? 'text-saffron-500 font-medium' : '' }}">
                                <span>यादव व्यापार एवं यादव ग्राहक</span>
                            </button>
                            <div class="pl-4 hidden">
                                <a href="{{ route('anya-seva.yadav-vyapar-grahak') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500 {{ Request::routeIs('anya-seva.yadav-vyapar-grahak') ? 'text-saffron-500 font-medium' : '' }}">जानकारी
                                    जोड़ें</a>
                                <a href="{{ route('yadav-vyapar-grahak-reports') }}"
                                    class="block py-2 border-b border-gray-100 text-navy-600 hover:text-saffron-500 {{ Request::routeIs('yadav-vyapar-grahak-reports') ? 'text-saffron-500 font-medium' : '' }}">रिपोर्ट
                                    देखें</a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- Mobile User Status Section -->
            <div class="mt-6 space-y-3">
                @auth('member')
                    <!-- Member Status -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <div class="font-medium text-navy-800">{{ Auth::guard('member')->user()->full_name }}</div>
                                <div class="text-sm text-blue-600">सदस्य</div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <a href="{{ route('member.dashboard') }}"
                                class="block w-full text-center py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                                डैशबोर्ड
                            </a>
                            <form method="POST" action="{{ route('member.logout') }}">
                                @csrf
                                <button type="submit"
                                    class="block w-full text-center py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">
                                    लॉगआउट
                                </button>
                            </form>
                        </div>
                    </div>
                @endauth

                @auth('vaivahik')
                    <!-- Vaivahik Status -->
                    <div class="bg-pink-50 rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" />
                                </svg>
                            </div>
                            <div>
                                <div class="font-medium text-navy-800">{{ Auth::guard('vaivahik')->user()->naam }}</div>
                                <div class="text-sm text-pink-600">वैवाहिक सदस्य</div>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <a href="{{ route('vaivahik.dashboard') }}"
                                class="block w-full text-center py-2 bg-pink-500 text-white rounded-md hover:bg-pink-600 transition-colors">
                                डैशबोर्ड
                            </a>
                            <form method="POST" action="{{ route('vaivahik.logout') }}">
                                @csrf
                                <button type="submit"
                                    class="block w-full text-center py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">
                                    लॉगआउट
                                </button>
                            </form>
                        </div>
                    </div>
                @endauth

                @guest('member')
                    @guest('vaivahik')
                        <!-- Login Options for Guests -->
                        <div class="space-y-2">
                            <a href="{{ route('member.login') }}" class="btn-secondary btn-with-icon w-full">
                                <span>सदस्य लॉगिन</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                            <a href="{{ route('vaivahik.login') }}" class="btn-secondary btn-with-icon w-full">
                                <span>वैवाहिक लॉगिन</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path
                                        d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" />
                                </svg>
                            </a>
                            <a href="{{ route('sadasya.aavedan') }}" class="btn-primary btn-with-icon w-full">
                                <span>सदस्य बनें</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    @endguest
                @endguest
            </div>
        </div>
    </div>

    {{-- Success/Error Messages --}}
    @if(session('success'))
        <div id="success-notification"
            class="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd" />
                </svg>
                {{ session('success') }}
            </div>
        </div>
    @endif

    @if(session('error'))
        <div id="error-notification" class="fixed top-4 right-4 z-50 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd" />
                </svg>
                {{ session('error') }}
            </div>
        </div>
    @endif

    {{-- Main Content Load Here --}}

    <main class="flex-grow frontend-content">
        @yield('content')
    </main>

    {{-- Main End --}}

    <footer class="bg-gradient-to-r from-navy-900 to-navy-800 text-white pt-12 relative overflow-hidden">
        <!-- Decorative Pattern -->
        <div class="absolute top-0 left-0 w-full h-full opacity-5 bg-pattern-saffron pointer-events-none"></div>

        <!-- Top Wave Separator -->
        <div class="absolute top-0 left-0 w-full overflow-hidden">
            <svg class="fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"
                style="height: 60px; width: 100%;">
                <path
                    d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z">
                </path>
            </svg>
        </div>

        <div class="container-custom relative z-10">

            <div class="container py-3">
                <div class="row">
                    <!-- About Section 1-->
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-warning text-white rounded-circle d-flex justify-content-center align-items-center me-3"
                                style="width: 40px; height: 40px;">
                                <img src="{{ asset('images/logo-yadav-samaj.png') }}" alt="यादव समाज Logo"
                                    class="w-15 h-10 object-cover rounded-full">
                            </div>
                            <h5 class="mb-0 text-white">छ. ग. यादव शासकीय सेवक संघ</h5>
                        </div>
                        <p class="text-light">
                            यादव समाज एक सामाजिक संगठन है जो समुदाय की सेवा और उत्थान के लिए प्रतिबद्ध है। हमारा
                            उद्देश्य सभी यादव परिवारों को एकजुट करना और उनके विकास में सहायता करना है।
                        </p>
                        <hr>
                        <div class="d-flex gap-2 mt-3">
                            <h6 class=" text-white w-15 h-9 flex items-center justify-center">Follow Us</h5>
                                <!-- Facebook -->
                                <a href="{{ config('constants.SOCIAL_MEDIA.FACEBOOK') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Facebook SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <!-- Twitter -->
                                <a href="{{ config('constants.SOCIAL_MEDIA.TWITTER') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Twitter SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84">
                                        </path>
                                    </svg>
                                </a>
                                <!-- Instagram -->
                                <a href="{{ config('constants.SOCIAL_MEDIA.INSTAGRAM') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Instagram SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <!-- YouTube -->
                                <a href="{{ config('constants.SOCIAL_MEDIA.YOUTUBE') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- YouTube SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                        </div>
                    </div>

                    <!-- Important Links -->
                    <div class="col-md-3 mb-3">
                        <h5 class="text-white border-bottom pb-2 mb-3">महत्वपूर्ण लिंक</h5>
                        <div class="row">
                            <div class="col-6">
                                <ul class="list-unstyled text-light">
                                    <li><a href="/humare-bare-me/uddeshya-aur-itihas"
                                            class="text-decoration-none text-light">हमारे बारे में</a></li>
                                    <li><a href="/humare-bare-me/sanghatan-dastavej"
                                            class="text-decoration-none text-light">संगठन दस्तावेज</a></li>
                                    <li><a href="/vaivahik-sadasya-panjiyan"
                                            class="text-decoration-none text-light">विवाह-बंधन</a></li>
                                    <li><a href="/sadasya-aavedan" class="text-decoration-none text-light">सदस्यता</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled text-light">
                                    <li><a href="/news" class="text-decoration-none text-light">समाचार/सूचनाएँ</a></li>
                                    <li><a href="/anya-seva/naukri-sahayta"
                                            class="text-decoration-none text-light">नौकरी सहायता</a></li>
                                    <li><a href="/activities" class="text-decoration-none text-light">गतिविधियाँ</a>
                                    </li>
                                    <li><a href="/sampark"
                                            class="text-decoration-none text-light">संपर्क</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Section -->
                    <div class="col-md-3 mb-3">
                        <h5 class="text-white border-bottom pb-2 mb-3">संपर्क करें</h5>
                        <ul class="list-unstyled text-light">
                            <li class="mb-2">
                                <i class="bi bi-geo-alt-fill me-2 text-warning"></i>{{ config('constants.ADDRESS') }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-telephone-fill me-2 text-warning"></i>{{ config('constants.MOBILE') }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-envelope-fill me-2 text-warning"></i>{{ config('constants.EMAIL') }}
                            </li>
                        </ul>
                    </div>

                    <!-- About Section 2 -->
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-warning text-white rounded-circle d-flex justify-content-center align-items-center me-3"
                                style="width: 40px; height: 40px;">
                                <img src="{{ asset('images/cgyss-logo2.jpg') }}" alt="यादव समाज Logo"
                                    class=" object-cover rounded-full">
                            </div>
                            <h5 class="mb-0 text-white">एकादश उत्थान यादव कल्याण</h5>
                        </div>
                        <p class="text-light">
                            यादव समाज एक सामाजिक संगठन है जो समुदाय की सेवा और उत्थान के लिए प्रतिबद्ध है। हमारा
                            उद्देश्य सभी यादव परिवारों को एकजुट करना और उनके विकास में सहायता करना है।
                        </p>
                        <hr>
                        <div class="d-flex gap-2 mt-3">
                            <h6 class=" text-white w-15 h-9 flex items-center justify-center">Follow Us</h5>
                                <!-- Facebook -->
                                <a href="{{ config('constants.SOCIAL_MEDIA_LINK.FACEBOOK') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Facebook SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <!-- Twitter -->
                                <a href="{{ config('constants.SOCIAL_MEDIA_LINK.TWITTER') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Twitter SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path
                                            d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84">
                                        </path>
                                    </svg>
                                </a>
                                <!-- Instagram -->
                                <a href="{{ config('constants.SOCIAL_MEDIA_LINK.INSTAGRAM') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- Instagram SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <!-- YouTube -->
                                <a href="{{ config('constants.SOCIAL_MEDIA_LINK.YOUTUBE') }}"
                                    class="w-9 h-9 flex items-center justify-center rounded-full bg-white bg-opacity-10 hover:bg-saffron-500 hover:scale-110 transition-all duration-300 text-white">
                                    <!-- YouTube SVG -->
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd"
                                            d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                        </div>
                    </div>

                </div>
            </div>



            <hr>
            <!-- Footer Bottom -->
            <div class="flex flex-col md:flex-row justify-between items-center my-2">
                <p>&copy; {{ date('Y') }} छ. ग. यादव शासकीय सेवक संघ. सर्वाधिकार सुरक्षित.</p>
                <div class=" md:mt-0 space-x-2">
                    <a href="#"
                        class="text-gray-400 hover:text-saffron-300 transition-colors mx-3 hover-underline">गोपनीयता
                        नीति</a>
                    <a href="#" class="text-gray-400 hover:text-saffron-300 transition-colors mx-3 hover-underline">नियम
                        और
                        शर्तें</a>
                </div>
            </div>
        </div>
        <hr />
        <div class="container-fluid  bg-dark text-white text-center">
            <strong class="py-1"> Designed and Developed By
                <a href="https://marutiitzone.com/" class="text-saffron" style="color:coral" target="_blank"
                    rel="noopener noreferrer"
                    class="text-white hover:text-green-400 font-medium transition-colors duration-200">
                    Maruti IT Zone
                </a></strong>
        </div>
    </footer>

    <!-- Enhanced Navbar & Notification Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Live Date and Time Update
            function updateDateTime() {
                const now = new Date();

                // Format date in Hindi
                const dateOptions = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    locale: 'hi-IN'
                };

                // Format time
                const timeOptions = {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                };

                const dateElement = document.getElementById('current-date');
                const timeElement = document.getElementById('current-time');

                if (dateElement) {
                    const formattedDate = now.toLocaleDateString('hi-IN', dateOptions);
                    dateElement.textContent = formattedDate;
                }

                if (timeElement) {
                    const formattedTime = now.toLocaleTimeString('en-IN', timeOptions);
                    timeElement.textContent = formattedTime;
                }
            }

            // Update immediately and then every second
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // Navbar scroll effects
            const navbarHeader = document.querySelector('.navbar-header');
            let lastScrollTop = 0;

            window.addEventListener('scroll', function () {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 50) {
                    navbarHeader.style.background = 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(248, 250, 252, 0.95) 100%)';
                    navbarHeader.style.backdropFilter = 'blur(15px)';
                    navbarHeader.style.borderBottomColor = '#ff9933';
                    navbarHeader.style.boxShadow = '0 4px 25px rgba(0, 0, 0, 0.15)';
                    navbarHeader.style.transform = 'translateY(0)';
                } else {
                    navbarHeader.style.background = 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%)';
                    navbarHeader.style.backdropFilter = 'blur(5px)';
                    navbarHeader.style.borderBottomColor = '#e5e7eb';
                    navbarHeader.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                }

                lastScrollTop = scrollTop;
            });

            // Mobile menu functionality
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileOverlay = document.getElementById('mobile-overlay');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function () {
                    mobileMenu.classList.add('active');
                    mobileOverlay.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', function () {
                    mobileMenu.classList.remove('active');
                    mobileOverlay.classList.add('hidden');
                    document.body.style.overflow = '';
                });
            }

            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', function () {
                    mobileMenu.classList.remove('active');
                    mobileOverlay.classList.add('hidden');
                    document.body.style.overflow = '';
                });
            }



            // Auto-hide notifications after 5 seconds
            const successNotification = document.getElementById('success-notification');
            const errorNotification = document.getElementById('error-notification');

            if (successNotification) {
                setTimeout(() => {
                    successNotification.style.opacity = '0';
                    setTimeout(() => successNotification.remove(), 300);
                }, 5000);
            }

            if (errorNotification) {
                setTimeout(() => {
                    errorNotification.style.opacity = '0';
                    setTimeout(() => errorNotification.remove(), 300);
                }, 5000);
            }
        });
    </script>

</body>

</html>
