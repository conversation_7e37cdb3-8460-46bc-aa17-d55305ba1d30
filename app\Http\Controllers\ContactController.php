<?php

namespace App\Http\Controllers;

use App\Models\ContactSubmission;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Show the contact page
     */
    public function index()
    {
        return view('sampark.index');
    }

    /**
     * Handle contact form submission
     */
    public function submit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'suggestion' => 'nullable|string|max:2000',
        ], [
            'name.required' => 'नाम आवश्यक है।',
            'name.max' => 'नाम 255 अक्षरों से कम होना चाहिए।',
            'email.required' => 'ईमेल आवश्यक है।',
            'email.email' => 'कृपया वैध ईमेल दर्ज करें।',
            'email.max' => 'ईमेल 255 अक्षरों से कम होना चाहिए।',
            'phone.max' => 'फोन नंबर 20 अक्षरों से कम होना चाहिए।',
            'subject.required' => 'विषय आवश्यक है।',
            'subject.max' => 'विषय 255 अक्षरों से कम होना चाहिए।',
            'message.required' => 'संदेश आवश्यक है।',
            'message.max' => 'संदेश 2000 अक्षरों से कम होना चाहिए।',
            'suggestion.max' => 'सुझाव 2000 अक्षरों से कम होना चाहिए।',
        ]);

        ContactSubmission::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'message' => $request->message,
            'suggestion' => $request->suggestion,
        ]);

        return redirect()->back()->with('success', 'आपका संदेश सफलतापूर्वक भेज दिया गया है। हम जल्द ही आपसे संपर्क करेंगे।');
    }
}
