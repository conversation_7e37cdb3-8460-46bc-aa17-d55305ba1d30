<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Add state_name field with default value
            $table->string('state_name')->default('छत्तीसगढ़')->after('id');

            // Make division_code nullable
            $table->string('division_code')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pramukh_padadhikaris', function (Blueprint $table) {
            // Remove state_name field
            $table->dropColumn('state_name');

            // Make division_code required again
            $table->string('division_code')->nullable(false)->change();
        });
    }
};
