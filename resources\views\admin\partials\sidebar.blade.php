<!-- Mobile sidebar backdrop --> <div x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden" @click="sidebarOpen = false" x-cloak ></div> <!-- Sidebar --> <aside :class="{'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen}" class="fixed top-0 left-0 z-30 h-full w-64 transform bg-navy-900 admin-sidebar text-white transition duration-300 ease-in-out lg:translate-x-0 lg:static lg:h-auto" > <!-- Sidebar header --> <div class="flex items-center justify-between px-4 py-5 lg:py-6 border-b border-navy-800"> <a href="{{ route('admin.dashboard') }}" class="flex items-center space-x-2"> <span class="text-2xl font-bold text-saffron-400">Admin</span> </a> <button @click="sidebarOpen = false" class="text-white hover:text-saffron-400 focus:outline-none lg:hidden"> <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> <!-- Navigation --> <nav class="mt-5 px-2 space-y-1"> <a href="{{ route('admin.dashboard') }}" class="group flex items-center px-3 py-3 text-base leading-6 font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 transition ease-in-out duration-150 {{ request()->routeIs('admin.dashboard') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400 pl-2' : '' }}"> <svg class="mr-3 h-5 w-5 {{ request()->routeIs('admin.dashboard') ? 'text-saffron-400' : 'text-white group-hover:text-saffron-400' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /> </svg> Dashboard </a> <a href="" class="group flex items-center px-3 py-3 text-base leading-6 font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 transition ease-in-out duration-150 {{ request()->routeIs('admin.users.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400 pl-2' : '' }}"> <svg class="mr-3 h-5 w-5 {{ request()->routeIs('admin.users.*') ? 'text-saffron-400' : 'text-white group-hover:text-saffron-400' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /> </svg> Users </a> <div x-data="{ open: {{ request()->routeIs('admin.about-content.*') || request()->routeIs('admin.news.*') || request()->routeIs('admin.activities.*') || request()->routeIs('admin.documents.*') || request()->routeIs('admin.memberships.*') || request()->routeIs('admin.ekadash-sadasyata.*') || request()->routeIs('admin.vaivahik-users.*') || request()->routeIs('admin.pramukh-padadhikaris.*') || request()->routeIs('admin.organization-documents.*') || request()->routeIs('admin.membership-varg-masters.*') || request()->routeIs('admin.education-qualifications.*') || request()->routeIs('admin.office-masters.*') || request()->routeIs('admin.department-masters.*') || request()->routeIs('admin.division-masters.*') || request()->routeIs('admin.district-masters.*') || request()->routeIs('admin.vikaskhand-masters.*') || request()->routeIs('admin.naukri-sahayta.*') || request()->routeIs('admin.multinational-companies.*') || request()->routeIs('admin.yadav-vyapar-grahak.*') || request()->routeIs('admin.darshanik-ishthal.*') ? 'true' : 'false' }} }" class="space-y-1"> <button @click="open = !open" class="group flex items-center w-full px-3 py-3 text-base font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 focus:outline-none transition ease-in-out duration-150" > <svg class="mr-3 h-5 w-5 text-white group-hover:text-saffron-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" /> </svg> Content <svg class="ml-auto h-4 w-4 transition-transform" :class="{ 'rotate-90': open }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /> </svg> </button> <div x-show="open" x-cloak class="pl-6 space-y-1"> <a href="{{ route('admin.about-content.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.about-content.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> About Us </a> <a href="{{ route('admin.activities.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.activities.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Activities </a> <a href="{{ route('admin.news.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.news.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> News & Announcements </a> <a href="{{ route('admin.memberships.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.memberships.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Membership Applications </a> <a href="{{ route('admin.ekadash-sadasyata.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.ekadash-sadasyata.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Ekadash Sadasyata </a> <a href="{{ route('admin.vaivahik-users.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.vaivahik-users.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Vaivahik Users </a> <!-- Pramukh Padadhikari with Submenus --> <div x-data="{ open: {{ request()->routeIs('admin.pramukh-padadhikaris.*') ? 'true' : 'false' }} }" class="space-y-1"> <button @click="open = !open" class="group flex items-center w-full px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 focus:outline-none transition ease-in-out duration-150 {{ request()->routeIs('admin.pramukh-padadhikaris.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}" > <svg class="mr-3 h-4 w-4 text-white group-hover:text-saffron-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /> </svg> प्रमुख पदाधिकारी <svg class="ml-auto h-4 w-4 transition-transform" :class="{ 'rotate-90': open }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /> </svg> </button> <div x-show="open" x-cloak class="pl-6 space-y-1"> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="group flex items-center px-3 py-2 text-xs font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.pramukh-padadhikaris.index') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> <svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path> </svg> सभी पदाधिकारी </a> <a href="{{ route('admin.pramukh-padadhikaris.sambhag-wise') }}" class="group flex items-center px-3 py-2 text-xs font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.pramukh-padadhikaris.sambhag-wise') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> <svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path> </svg> संभाग स्तर </a> <a href="{{ route('admin.pramukh-padadhikaris.jila-wise') }}" class="group flex items-center px-3 py-2 text-xs font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.pramukh-padadhikaris.jila-wise') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> <svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path> </svg> जिला स्तर </a> <a href="{{ route('admin.pramukh-padadhikaris.vikaskhand-wise') }}" class="group flex items-center px-3 py-2 text-xs font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.pramukh-padadhikaris.vikaskhand-wise') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> <svg class="mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path> </svg> विकासखंड स्तर </a> </div> </div> <a href="{{ route('admin.organization-documents.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.organization-documents.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Organization Documents </a> <a href="{{ route('admin.naukri-sahayta.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.naukri-sahayta.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> नौकरी/रोजगार सहायता </a> <a href="{{ route('admin.multinational-companies.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.multinational-companies.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> मल्टीनेशनल कंपनी </a> <a href="{{ route('admin.yadav-vyapar-grahak.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.yadav-vyapar-grahak.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> यादव व्यापार ग्राहक </a> <a href="{{ route('admin.darshanik-ishthal.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.darshanik-ishthal.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> Darshanik Ishthal </a> </div> </div> <!-- Master Data Management --> <div x-data="{ open: {{ request()->routeIs('admin.membership-varg-masters.*') || request()->routeIs('admin.education-qualifications.*') || request()->routeIs('admin.office-masters.*') || request()->routeIs('admin.department-masters.*') || request()->routeIs('admin.yadav-vargs.*') || request()->routeIs('admin.division-masters.*') || request()->routeIs('admin.district-masters.*') || request()->routeIs('admin.vikaskhand-masters.*') ? 'true' : 'false' }} }" class="space-y-1"> <button @click="open = !open" class="group flex items-center w-full px-3 py-3 text-base font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 focus:outline-none transition ease-in-out duration-150" > <svg class="mr-3 h-5 w-5 text-white group-hover:text-saffron-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" /> </svg> Master Data <svg class="ml-auto h-4 w-4 transition-transform" :class="{ 'rotate-90': open }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /> </svg> </button> <div x-show="open" x-cloak class="pl-6 space-y-1"> <a href="{{ route('admin.membership-varg-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.membership-varg-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> सदस्यता वर्ग </a> <a href="{{ route('admin.education-qualifications.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.education-qualifications.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> शैक्षणिक योग्यता </a> <a href="{{ route('admin.office-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.office-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> कार्यालय प्रकार </a> <a href="{{ route('admin.department-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.department-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> विभाग </a> <a href="{{ route('admin.yadav-vargs.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.yadav-vargs.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> यादव वर्ग </a> <a href="{{ route('admin.division-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.division-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> संभाग प्रबंधन </a> <a href="{{ route('admin.district-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.district-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> जिला प्रबंधन </a> <a href="{{ route('admin.vikaskhand-masters.index') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 {{ request()->routeIs('admin.vikaskhand-masters.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400' : '' }}"> विकासखंड प्रबंधन </a> </div> </div> <a href="{{ route('admin.contact-submissions.index') }}" class="group flex items-center px-3 py-3 text-base leading-6 font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 transition ease-in-out duration-150 {{ request()->routeIs('admin.contact-submissions.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400 pl-2' : '' }}"> <svg class="mr-3 h-5 w-5 {{ request()->routeIs('admin.contact-submissions.*') ? 'text-saffron-400' : 'text-white group-hover:text-saffron-400' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /> </svg> संपर्क सबमिशन </a> <a href="" class="group flex items-center px-3 py-3 text-base leading-6 font-medium rounded-md text-white hover:bg-navy-800 hover:text-saffron-400 transition ease-in-out duration-150 {{ request()->routeIs('admin.settings.*') ? 'bg-navy-800 text-saffron-400 border-l-4 border-saffron-400 pl-2' : '' }}"> <svg class="mr-3 h-5 w-5 {{ request()->routeIs('admin.settings.*') ? 'text-saffron-400' : 'text-white group-hover:text-saffron-400' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /> </svg> Settings </a> </nav> </aside> 