<!DOCTYPE html>
<html lang="hi">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>सदस्यता कार्ड - {{ $member->name }}</title>
    <style>
        @page {
            margin: 15mm;
            size: A4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', 'Noto Sans Devanagari', sans-serif;
            font-size: 11px;
            line-height: 1.5;
            color: #000;
            background: white;
        }
        
        .letterhead {
            background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
            padding: 15px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .letterhead::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -10%;
            width: 120%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: skewX(-15deg);
        }

        .letterhead-content {
            position: relative;
            z-index: 2;
            display: table;
            width: 100%;
        }

        .logo-section {
            display: table-cell;
            width: 80px;
            vertical-align: middle;
            padding-right: 15px;
        }

        .logo {
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #2c5f6f;
            font-size: 10px;
            text-align: center;
            line-height: 1.2;
        }

        .header-text {
            display: table-cell;
            vertical-align: middle;
            color: white;
            text-align: center;
        }

        .organization-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .sub-details {
            font-size: 12px;
            margin-bottom: 3px;
            opacity: 0.95;
        }

        .address {
            font-size: 11px;
            opacity: 0.9;
        }
        
        .form-content {
            padding: 20px;
            position: relative;
        }

        .form-header {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }

        .form-left {
            display: table-cell;
            width: 70%;
            vertical-align: top;
        }

        .form-right {
            display: table-cell;
            width: 30%;
            vertical-align: top;
            text-align: right;
            padding-left: 20px;
        }

        .photo-section {
            border: 2px solid #333;
            width: 120px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #666;
            text-align: center;
            margin-left: auto;
            background: #f9f9f9;
        }

        .qr-section {
            margin-top: 10px;
            text-align: center;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            border: 1px solid #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            margin: 0 auto 5px;
            background: #f9f9f9;
        }

        .subject-line {
            font-weight: bold;
            margin-bottom: 15px;
            text-decoration: underline;
        }

        .content-text {
            margin-bottom: 15px;
            text-align: justify;
            line-height: 1.6;
        }
        
        .form-fields {
            margin-bottom: 20px;
        }

        .field-row {
            display: table;
            width: 100%;
            margin-bottom: 12px;
            border-bottom: 1px dotted #333;
            padding-bottom: 5px;
        }

        .field-number {
            display: table-cell;
            width: 25px;
            font-weight: bold;
            vertical-align: top;
        }

        .field-label {
            display: table-cell;
            width: 200px;
            vertical-align: top;
            padding-right: 10px;
        }

        .field-value {
            display: table-cell;
            vertical-align: top;
            border-bottom: 1px dotted #333;
            min-height: 20px;
            padding-left: 10px;
            font-weight: bold;
        }

        .declaration-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }

        .declaration-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            text-decoration: underline;
        }

        .declaration-text {
            text-align: justify;
            line-height: 1.5;
            font-size: 10px;
        }

        .proposer-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }

        .proposer-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            text-decoration: underline;
        }
        
        .signature-footer {
            margin-top: 30px;
            display: table;
            width: 100%;
        }

        .signature-left {
            display: table-cell;
            width: 50%;
            text-align: left;
            vertical-align: bottom;
        }

        .signature-right {
            display: table-cell;
            width: 50%;
            text-align: right;
            vertical-align: bottom;
        }

        .signature-line {
            width: 150px;
            border-bottom: 1px solid #333;
            margin-bottom: 5px;
            height: 40px;
        }

        .signature-label {
            font-size: 10px;
            font-weight: bold;
        }

        .office-use {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #333;
            background: #f9f9f9;
        }

        .office-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 60px;
            color: rgba(0,0,0,0.03);
            font-weight: bold;
            z-index: 1;
            pointer-events: none;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="watermark">छत्तीसगढ़ यादव समाज</div>

    <!-- Official Letterhead -->
    <div class="letterhead">
        <div class="letterhead-content">
            <div class="logo-section">
                <div class="logo">
                    छ.ग. यादव<br>
                    शासकीय सेवक समिति<br>
                    <small>YSSS 36</small>
                </div>
            </div>
            <div class="header-text">
                <div class="organization-name">छत्तीसगढ़ यादव शासकीय सेवक समिति</div>
                <div class="sub-details">पं. क्रमांक. {{ $member->membership_number }} दिनांक {{ $member->created_at->format('d.m.Y') }}</div>
                <div class="address">पता : A-26, गायत्री नगर, रायपुर</div>
            </div>
        </div>
    </div>

    <!-- Form Content -->
    <div class="form-content">
        <div class="form-header">
            <div class="form-left">
                <div style="margin-bottom: 10px;">
                    <strong>प्रति,</strong><br>
                    <span style="margin-left: 20px;">अध्यक्ष / सचिव</span><br>
                    <span style="margin-left: 20px;">छ.ग यादव शासकीय सेवक समिति</span><br>
                    <span style="margin-left: 20px;">गायत्री नगर रायपुर</span>
                </div>

                <div class="subject-line">
                    <strong>विषय:- छ.ग. यादव शासकीय सेवक समिति की सदस्यता ग्रहण करने बाबत्।</strong>
                </div>
            </div>

            <div class="form-right">
                <div style="border: 1px solid #333; padding: 5px; margin-bottom: 10px; font-size: 9px; text-align: center;">
                    <div>सदस्यता संख्या {{ substr($member->membership_number, -7) }} का कार्ड</div>
                    <div>आजीवन सदस्य 5,000 रुपए कार्ड</div>
                    <div>साधारण सदस्य 1200 वार्षिक</div>
                </div>

                <div class="photo-section">
                    @if($member->photo && file_exists(public_path('storage/' . $member->photo)))
                        <img src="{{ public_path('storage/' . $member->photo) }}" style="width: 100%; height: 100%; object-fit: cover;" alt="Member Photo">
                    @else
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 12px; color: #666;">
                            Passport photo<br>size
                        </div>
                    @endif
                </div>

                <div class="qr-section">
                    <div class="qr-code">QR Code</div>
                    <div style="font-size: 8px;">सदस्यता सत्यापन हेतु क्यू आर कोड</div>
                </div>
            </div>
        </div>

        <div class="content-text">
            <strong>महोदय,</strong><br>
            निवेदन है कि मैं छत्तीसगढ़ / केन्द्र शासन के कार्मिक हूं मैं समिति की गतिविधियों एवं कार्य योजना से परिचित हूं। (सदस्यक/आजीवन / साधारण सदस्य बनकर यादव समाज की सेवा करना चाहता हूं।
        </div>

        <div style="margin-bottom: 15px;">
            <strong>मेरी संपूर्ण जानकारी निम्न है:-</strong>
        </div>

        <div class="form-fields">
            <div class="field-row">
                <div class="field-number">1.</div>
                <div class="field-label">नाम/पिता का नाम</div>
                <div class="field-value">{{ $member->name }} / {{ $member->fathers_husband_name }}</div>
            </div>

            <div class="field-row">
                <div class="field-number">2.</div>
                <div class="field-label">पद/पदेश स्थान</div>
                <div class="field-value">{{ $member->department_name ?? 'N/A' }}</div>
            </div>

            <div class="field-row">
                <div class="field-number">3.</div>
                <div class="field-label">जन्म तिथि</div>
                <div class="field-value">{{ $member->birth_date ? $member->birth_date->format('d/m/Y') : 'N/A' }}</div>
            </div>

            <div class="field-row">
                <div class="field-number">4.</div>
                <div class="field-label">मोबाइल / वैवाहिक स्थिति</div>
                <div class="field-value">{{ $member->mobile_number }} / {{ $member->marital_status ?? 'N/A' }}</div>
            </div>

            <div class="field-row">
                <div class="field-number">5.</div>
                <div class="field-label">संस्थान/कार्यालय A पुत्र/पुत्री का नाम एवं जन्म तिथि</div>
                <div class="field-value">{{ $member->office ?? 'N/A' }}</div>
            </div>

            <div class="field-row" style="border-bottom: none;">
                <div class="field-number"></div>
                <div class="field-label">B पुत्र/पुत्री का नाम एवं जन्म तिथि</div>
                <div class="field-value">
                    @if($member->children && count($member->children) > 0)
                        @foreach($member->children as $index => $child)
                            {{ $child['name'] ?? 'N/A' }} ({{ $child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') : 'N/A' }})
                            @if(!$loop->last), @endif
                        @endforeach
                    @else
                        N/A
                    @endif
                </div>
            </div>

            <div class="field-row">
                <div class="field-number">6.</div>
                <div class="field-label">सदस्य का संपूर्ण पता जी.</div>
                <div class="field-value">{{ $member->address ?? 'N/A' }}</div>
            </div>

            <div class="field-row">
                <div class="field-number">7.</div>
                <div class="field-label">सदस्य बनने का आधार</div>
                <div class="field-value">{{ $member->caste_details ?? 'यादव समुदाय' }}</div>
            </div>
        </div>

        <!-- Declaration Section -->
        <div class="declaration-section">
            <div class="declaration-title">घोषणा पत्र</div>
            <div class="declaration-text">
                मैं घोषणा करता/करती हूं कि उपरोक्त जानकारी सत्य है। असत्य पाये जाने की स्थिति में मेरी सदस्यता समाप्त करने का अधिकार प्रबंधकारिणी समिति का होगा।
            </div>
        </div>

        <!-- Proposer Section -->
        <div class="proposer-section">
            <div class="proposer-title">प्रस्तावक सदस्य</div>
            <div style="margin-bottom: 15px;">
                <strong>सदस्य का नाम:</strong> {{ $member->member_name_signature ?? 'N/A' }}<br>
                <strong>पदनाम:</strong> {{ $member->vibhag ?? 'N/A' }}<br>
                <strong>कार्यालय पता/जी. नं:</strong> {{ $member->proposer_address ?? 'N/A' }}
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature-footer">
            <div class="signature-left">
                <div style="margin-bottom: 10px;">
                    @if($member->signature && file_exists(public_path('storage/' . $member->signature)))
                        <img src="{{ public_path('storage/' . $member->signature) }}" style="width: 120px; height: 40px; object-fit: contain; border-bottom: 1px solid #333;" alt="Member Signature">
                    @else
                        <div class="signature-line"></div>
                    @endif
                </div>
                <div class="signature-label">सदस्य हस्ताक्षर</div>
                <div style="margin-top: 20px;">
                    <div class="signature-line"></div>
                    <div class="signature-label">अधिकारी का कार्यक्षेत्र:-</div>
                </div>
            </div>

            <div class="signature-right">
                <div class="signature-line"></div>
                <div class="signature-label">दिनांक: {{ $member->created_at->format('d/m/Y') }}</div>
                <div style="margin-top: 20px;">
                    <div class="signature-line"></div>
                    <div class="signature-label">हस्ताक्षर सचिव</div>
                </div>
            </div>
        </div>

        <!-- Office Use Section -->
        <div class="office-use">
            <div class="office-title">हस्ताक्षर अध्यक्ष</div>
            <div style="height: 40px;"></div>
        </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 30px; text-align: center; font-size: 9px; color: #666; border-top: 1px solid #ddd; padding-top: 10px;">
        <p><strong>यह एक आधिकारिक दस्तावेज है। कृपया इसे सुरक्षित रखें।</strong></p>
        <p>छत्तीसगढ़ यादव शासकीय सेवक समिति | जेनरेट किया गया: {{ now()->format('d M Y, h:i A') }}</p>
        <p>सदस्यता संख्या: {{ $member->membership_number }} | स्थिति: {{ $member->getStatusDisplayText() }}</p>
    </div>
</body>
</html>
