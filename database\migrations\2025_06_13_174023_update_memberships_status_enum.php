<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, modify the enum to include 'pending' and other values
        DB::statement("ALTER TABLE memberships MODIFY COLUMN status ENUM('pending', 'approved', 'rejected', 'INACTIVE') DEFAULT 'pending'");

        // Then update any existing invalid status values
        DB::statement("UPDATE memberships SET status = 'pending' WHERE status = 'INACTIVE'");

        // Finally, remove the INACTIVE option from enum
        DB::statement("ALTER TABLE memberships MODIFY COLUMN status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the original enum (but this won't work properly, so we'll just keep the new enum)
        // DB::statement("ALTER TABLE memberships MODIFY COLUMN status ENUM('approved', 'rejected') DEFAULT 'pending'");
    }
};
