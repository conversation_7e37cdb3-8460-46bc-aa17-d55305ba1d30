@extends('layouts.admin') @section('title', 'जिला वार प्रमुख पदाधिकारी') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">जिला वार प्रमुख पदाधिकारी</h1> <p class="mt-1 text-sm admin-text-secondary">जिले के अनुसार प्रमुख पदाधिकारियों की सूची</p> </div> <div class="mt-4 sm:mt-0 flex space-x-3"> <a href="{{ route('admin.pramukh-padadhikaris.create') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"> <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path> </svg> नया जोड़ें </a> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> सभी देखें </a> </div> </div> </div> <!-- Search and Filter --> <div class="admin-card p-6 mb-6"> <form method="GET" action="{{ route('admin.pramukh-padadhikaris.jila-wise') }}" class="space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4"> <div class="flex-1"> <input type="text" name="search" value="{{ request('search') }}" placeholder="नाम, पदनाम या जिला खोजें..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> </div> <div> <select name="division_code" id="division_filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी संभाग</option> @foreach($divisions as $division) <option value="{{ $division->division_code }}" {{ request('division_code') === $division->division_code ? 'selected' : '' }}> {{ $division->division_name_hin }} </option> @endforeach </select> </div> <div> <select name="district_lgd_code" id="district_filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी जिले</option> @foreach($districts as $district) <option value="{{ $district->district_lgd_code }}" {{ request('district_lgd_code') === $district->district_lgd_code ? 'selected' : '' }}> {{ $district->district_name_hin }} </option> @endforeach </select> </div> <div> <select name="status" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input"> <option value="">सभी स्थिति</option> <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>सक्रिय</option> <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>निष्क्रिय</option> </select> </div> <div class="flex space-x-2"> <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> खोजें </button> <a href="{{ route('admin.pramukh-padadhikaris.jila-wise') }}" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> रीसेट </a> </div> </form> </div> <!-- Table --> <div class="admin-card overflow-hidden"> <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">फोटो</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">नाम एवं पदनाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">जिला</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">संभाग</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">विकासखंड</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">विभागीय पदनाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">पता</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">संपर्क</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @forelse($pramukhPadadhikaris as $pramukh) <tr class="hover:bg-gray-50:bg-navy-700"> <!-- Photo Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="flex-shrink-0 h-12 w-12"> @if($pramukh->photo) <img class="h-12 w-12 rounded-lg object-cover border border-gray-200" src="{{ asset('storage/' . $pramukh->photo) }}" alt="{{ $pramukh->naam }}"> @else <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center"> <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/> </svg> </div> @endif </div> </td> <!-- Name & Post Column --> <td class="px-6 py-4 whitespace-nowrap"> <div> <div class="text-sm font-medium text-gray-900">{{ $pramukh->naam }}</div> <div class="text-sm text-gray-500">{{ $pramukh->sangathan_me_padnaam }}</div> </div> </td> <!-- District Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $pramukh->district ? $pramukh->district->district_name_hin : '-' }} </div> @if($pramukh->district) <div class="text-sm text-gray-500">{{ $pramukh->district->district_name_eng }}</div> @endif </td> <!-- Division Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $pramukh->division ? $pramukh->division->division_name_hin : '-' }} </div> </td> <!-- Vikaskhand Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $pramukh->vikaskhand ? $pramukh->vikaskhand->sub_district_name_hin : '-' }} </div> </td> <!-- Vibhagiy Padnaam Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $pramukh->vibhagiy_padnaam ?? '-' }} </div> @if($pramukh->vibhag_ka_naam) <div class="text-sm text-gray-500">{{ $pramukh->vibhag_ka_naam }}</div> @endif </td> <!-- Address Column --> <td class="px-6 py-4"> @if($pramukh->vartaman_pata) <div class="text-sm text-gray-900"> <span class="font-medium">वर्तमान:</span> {{ Str::limit($pramukh->vartaman_pata, 40) }} </div> @endif @if($pramukh->isthayi_pata) <div class="text-sm text-gray-600 mt-1"> <span class="font-medium">स्थायी:</span> {{ Str::limit($pramukh->isthayi_pata, 40) }} </div> @endif @if(!$pramukh->vartaman_pata && !$pramukh->isthayi_pata) <div class="text-sm text-gray-400">-</div> @endif </td> <!-- Contact Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900">{{ $pramukh->formatted_mobile }}</div> </td> <!-- Status Column --> <td class="px-6 py-4 whitespace-nowrap"> @if($pramukh->active) <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"> सक्रिय </span> @else <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"> निष्क्रिय </span> @endif </td> <!-- Actions Column --> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex items-center space-x-3"> <a href="{{ route('admin.pramukh-padadhikaris.show', $pramukh->id) }}" class="text-blue-600 hover:text-blue-900:text-blue-300" title="विवरण देखें"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg> </a> <a href="{{ route('admin.pramukh-padadhikaris.edit', $pramukh->id) }}" class="text-indigo-600 hover:text-indigo-900:text-indigo-300" title="संपादित करें"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> </a> </div> </td> </tr> @empty <tr> <td colspan="10" class="px-6 py-4 text-center text-sm text-gray-500"> कोई प्रमुख पदाधिकारी नहीं मिला। </td> </tr> @endforelse </tbody> </table> </div> <!-- Pagination --> @if($pramukhPadadhikaris->hasPages()) <div class="px-6 py-4 border-t border-gray-200"> {{ $pramukhPadadhikaris->links() }} </div> @endif </div> </div> </div> @endsection 