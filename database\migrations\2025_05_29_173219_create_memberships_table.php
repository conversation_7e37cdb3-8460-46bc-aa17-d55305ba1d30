<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
  public function up()
{
    Schema::create('memberships', function (Blueprint $table) {
        $table->id();
        $table->string('membership_number')->unique(); // Auto-generated CGYS000001 style
        $table->string('photo');
        $table->string('signature');
        $table->string('name');
        $table->string('fathers_husband_name');
        $table->string('mobile_number', 10)->unique();
        $table->date('birth_date')->nullable();
        $table->string('marital_status')->nullable();
        $table->integer('family_members')->nullable();
        $table->string('education')->nullable();
        $table->string('caste_details')->nullable();
        $table->string('department_name')->nullable();
        $table->string('office')->nullable();
        $table->text('address')->nullable();
        $table->string('membership_type');
        $table->string('member_name_signature');
        $table->string('vibhag')->nullable();
        $table->string('mobile', 10);
        $table->string('proposer_address')->nullable();
        $table->boolean('declaration')->default(false);
        $table->json('children')->nullable();
        $table->json('ancestors')->nullable();
        $table->string('rejection_reason')->nullable();

        $table->boolean('is_active')->default(true);
        $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');

        $table->timestamps();
    });
}



    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('memberships');
    }
};