@extends('layouts.admin') @section('title', '<PERSON><PERSON><PERSON><PERSON> Management') @section('content') <div class="container mx-auto px-4 py-8"> <!-- Header --> <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8"> <div> <h1 class="text-3xl font-bold text-navy-900 mb-2"><PERSON><PERSON><PERSON><PERSON></h1> <p class="text-gray-600">Manage community leaders and officials</p> </div> <a href="{{ route('admin.pramukh-padadhikaris.create') }}" class="bg-navy-600 hover:bg-navy-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"> <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path> </svg> <span>Add New Pramukh Padadhikari</span> </a> </div> <!-- Success/Error Messages --> @if(session('success')) <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6"> {{ session('success') }} </div> @endif @if($errors->any()) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"> <ul class="list-disc list-inside"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> @endif <!-- Filters --> <div class="bg-white rounded-lg shadow-md p-6 mb-6"> <form method="GET" action="{{ route('admin.pramukh-padadhikaris.index') }}" class="flex flex-col md:flex-row gap-4"> <div class="flex-1"> <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label> <input type="text" name="search" id="search" value="{{ request('search') }}" placeholder="Search by name, post, office, or mobile..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-navy-500"> </div> <div class="md:w-48"> <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label> <select name="status" id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-navy-500"> <option value="">All Status</option> <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option> <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option> </select> </div> <div class="flex items-end space-x-2"> <button type="submit" class="bg-navy-600 hover:bg-navy-700 text-white px-6 py-2 rounded-md transition-colors duration-200"> Filter </button> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md transition-colors duration-200"> Clear </a> </div> </form> </div> <!-- Data Table --> <div class="bg-white rounded-lg shadow-md overflow-hidden"> @if($pramukhPadadhikaris->count() > 0) <!-- Desktop Table --> <div class="hidden md:block overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">फोटो</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">नाम एवं पदनाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">विभागीय पदनाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">पता</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">संपर्क</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @foreach($pramukhPadadhikaris as $pramukh) <tr class="hover:bg-gray-50:bg-navy-700"> <!-- Photo Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="flex-shrink-0 h-12 w-12"> @if($pramukh->photo) <img class="h-12 w-12 rounded-lg object-cover border border-gray-200" src="{{ asset('storage/' . $pramukh->photo) }}" alt="{{ $pramukh->naam }}"> @else <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center"> <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/> </svg> </div> @endif </div> </td> <!-- Name & Post Column --> <td class="px-6 py-4 whitespace-nowrap"> <div> <div class="text-sm font-medium text-gray-900">{{ $pramukh->naam }}</div> <div class="text-sm text-gray-500">{{ $pramukh->sangathan_me_padnaam }}</div> </div> </td> <!-- Vibhagiy Padnaam Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $pramukh->vibhagiy_padnaam ?? '-' }} </div> @if($pramukh->vibhag_ka_naam) <div class="text-sm text-gray-500">{{ $pramukh->vibhag_ka_naam }}</div> @endif </td> <!-- Pata Column --> <td class="px-6 py-4"> <div class="text-sm text-gray-900"> {{ Str::limit($pramukh->vartaman_pata, 40) }} </div> @if($pramukh->isthayi_pata && $pramukh->isthayi_pata !== $pramukh->vartaman_pata) <div class="text-sm text-gray-500">स्थायी: {{ Str::limit($pramukh->isthayi_pata, 30) }}</div> @endif </td> <!-- Sampark Column --> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900">{{ $pramukh->formatted_mobile }}</div> @if($pramukh->division) <div class="text-sm text-gray-500">{{ $pramukh->division->division_name_hin }}</div> @endif </td> <td class="px-6 py-4 whitespace-nowrap"> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $pramukh->active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}"> {{ $pramukh->status_text }} </span> </td> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex space-x-2"> <a href="{{ route('admin.pramukh-padadhikaris.show', $pramukh) }}" class="text-navy-600 hover:text-navy-900:text-navy-300">View</a> <a href="{{ route('admin.pramukh-padadhikaris.edit', $pramukh) }}" class="text-indigo-600 hover:text-indigo-900:text-indigo-300">Edit</a> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.toggle-status', $pramukh) }}" class="inline"> @csrf @method('PATCH') <button type="submit" class="text-yellow-600 hover:text-yellow-900:text-yellow-300" onclick="return confirm('Are you sure you want to {{ $pramukh->active ? 'deactivate' : 'activate' }} this pramukh padadhikari?')"> {{ $pramukh->active ? 'Deactivate' : 'Activate' }} </button> </form> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.destroy', $pramukh) }}" class="inline"> @csrf @method('DELETE') <button type="submit" class="text-red-600 hover:text-red-900:text-red-300" onclick="return confirm('Are you sure you want to delete this pramukh padadhikari? This action cannot be undone.')"> Delete </button> </form> </div> </td> </tr> @endforeach </tbody> </table> </div> <!-- Mobile Cards --> <div class="md:hidden"> @foreach($pramukhPadadhikaris as $pramukh) <div class="p-6 border-b border-gray-200"> <div class="flex items-start space-x-4 mb-4"> <!-- Photo --> <div class="flex-shrink-0"> @if($pramukh->photo) <img class="h-16 w-16 rounded-lg object-cover border border-gray-200" src="{{ asset('storage/' . $pramukh->photo) }}" alt="{{ $pramukh->name }}"> @else <div class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center"> <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/> </svg> </div> @endif </div> <!-- Content --> <div class="flex-1 min-w-0"> <div class="flex justify-between items-start"> <div> <h3 class="text-lg font-medium text-gray-900">{{ $pramukh->name }}</h3> <p class="text-sm text-gray-500">{{ $pramukh->post_in_community }}</p> </div> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $pramukh->active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}"> {{ $pramukh->status_text }} </span> </div> </div> </div> <div class="space-y-2 mb-4"> <div class="flex items-center text-sm text-gray-600"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> {{ $pramukh->formatted_mobile }} </div> <div class="flex items-start text-sm text-gray-600"> <svg class="w-4 h-4 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path> </svg> {{ $pramukh->address }} </div> @if($pramukh->office_name) <div class="flex items-center text-sm text-gray-600"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path> </svg> {{ $pramukh->office_name }} - {{ $pramukh->office_post }} </div> @endif </div> <div class="flex flex-wrap gap-2"> <a href="{{ route('admin.pramukh-padadhikaris.show', $pramukh) }}" class="bg-navy-100 text-navy-800 px-3 py-1 rounded text-sm hover:bg-navy-200 transition-colors duration-200">View</a> <a href="{{ route('admin.pramukh-padadhikaris.edit', $pramukh) }}" class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded text-sm hover:bg-indigo-200 transition-colors duration-200">Edit</a> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.toggle-status', $pramukh) }}" class="inline"> @csrf @method('PATCH') <button type="submit" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-sm hover:bg-yellow-200 transition-colors duration-200" onclick="return confirm('Are you sure you want to {{ $pramukh->active ? 'deactivate' : 'activate' }} this pramukh padadhikari?')"> {{ $pramukh->active ? 'Deactivate' : 'Activate' }} </button> </form> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.destroy', $pramukh) }}" class="inline"> @csrf @method('DELETE') <button type="submit" class="bg-red-100 text-red-800 px-3 py-1 rounded text-sm hover:bg-red-200 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this pramukh padadhikari? This action cannot be undone.')"> Delete </button> </form> </div> </div> @endforeach </div> <!-- Pagination --> <div class="px-6 py-4 bg-gray-50"> {{ $pramukhPadadhikaris->links() }} </div> @else <div class="p-12 text-center"> <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path> </svg> <h3 class="mt-4 text-lg font-medium text-gray-900">No Pramukh Padadhikaris found</h3> <p class="mt-2 text-gray-500">Get started by adding a new pramukh padadhikari.</p> <div class="mt-6"> <a href="{{ route('admin.pramukh-padadhikaris.create') }}" class="bg-navy-600 hover:bg-navy-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"> Add New Pramukh Padadhikari </a> </div> </div> @endif </div> </div> @endsection 