@extends('layouts.member')

@section('title', 'पारिवारिक वंशावली')

@section('head')
    <!-- jQuery is required for OrgChart JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- OrgChart JS CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/css/jquery.orgchart.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/orgchart/3.8.0/js/jquery.orgchart.min.js"></script>
@endsection

@section('content')
<div class="min-h-screen bg-white dark:bg-navy-900">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Simple Header -->
        <div class="mb-6 no-print">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        पारिवारिक वंशावली
                    </h1>
                    <p class="mt-1 text-gray-600 dark:text-gray-400">
                        {{ $member->name }} का पूर्वजों का विवरण
                    </p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="window.print()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        प्रिंट करें
                    </button>
                    <button id="toggle-view-btn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        दृश्य बदलें
                    </button>
                    <a href="{{ route('member.dashboard') }}" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                        वापस
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern OrgChart JS Family Tree -->
        <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
            <div class="p-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                    पारिवारिक वंशावली चार्ट
                </h2>

                <!-- Chart Container -->
                <div id="family-tree-chart" class="relative" style="height: 600px; border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden;">
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p class="text-gray-600 dark:text-gray-400">पारिवारिक वृक्ष लोड हो रहा है...</p>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">उपयोग निर्देश:</h3>
                    <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        <li>• चार्ट को ड्रैग करके घुमाएं</li>
                        <li>• ज़ूम इन/आउट करने के लिए + और - बटन का उपयोग करें</li>
                        <li>• पूरा चार्ट देखने के लिए ⌂ बटन दबाएं</li>
                        <li>• किसी भी नोड पर क्लिक करके विस्तृत जानकारी देखें</li>
                    </ul>
                </div>
            </div>
        </div>

        @if($member->ancestors && count($member->ancestors) > 0)
            <!-- Legacy Simple Family Tree (Hidden by default, can be toggled) -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg mt-6" style="display: none;" id="legacy-tree">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                        पारंपरिक सूची दृश्य
                    </h2>
                    @php
                        // Sort ancestors by generation level (highest to lowest)
                        $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                        $generationLabels = [
                            7 => '7वीं पीढ़ी',
                            6 => '6वीं पीढ़ी',
                            5 => '5वीं पीढ़ी',
                            4 => '4वीं पीढ़ी',
                            3 => '3वीं पीढ़ी',
                            2 => '2वीं पीढ़ी (दादा-दादी)',
                            1 => '1वीं पीढ़ी (माता-पिता)'
                        ];
                    @endphp

                    @foreach($generationLabels as $level => $label)
                        @php
                            $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                        @endphp
                        @if($ancestorsInLevel->count() > 0)
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-300 dark:border-navy-600">
                                    {{ $label }}
                                </h3>
                                        
                                <div class="grid grid-cols-1 gap-4">
                                    @foreach($ancestorsInLevel as $ancestor)
                                        <div class="border border-gray-300 dark:border-navy-600 rounded p-4 bg-gray-50 dark:bg-navy-700">
                                            <div class="flex flex-col lg:flex-row gap-4">
                                                <!-- Left side - Basic ancestor details -->
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-lg text-gray-900 dark:text-white mb-2">
                                                        {{ $ancestor['name'] ?? 'N/A' }}
                                                    </h4>

                                                    <div class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                                        @if(!empty($ancestor['birth_date']))
                                                            <div><strong>जन्म:</strong> {{ $ancestor['birth_date'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['death_date']))
                                                            <div><strong>मृत्यु:</strong> {{ $ancestor['death_date'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['birth_place']))
                                                            <div><strong>जन्म स्थान:</strong> {{ $ancestor['birth_place'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['occupation']))
                                                            <div><strong>व्यवसाय:</strong> {{ $ancestor['occupation'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['spouse']))
                                                            <div><strong>पत्नी:</strong> {{ $ancestor['spouse'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['children_count']))
                                                            <div><strong>संतान:</strong> {{ $ancestor['children_count'] }}</div>
                                                        @endif
                                                        @if(!empty($ancestor['gotra']))
                                                            <div><strong>गोत्र:</strong> {{ $ancestor['gotra'] }}</div>
                                                        @endif
                                                    </div>
                                                </div>

                                                <!-- Right side - Children details -->
                                                @if(!empty($ancestor['children_details']))
                                                    <div class="flex-1 lg:max-w-md">
                                                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded p-3">
                                                            <h5 class="font-semibold text-blue-900 dark:text-blue-100 mb-2 text-sm">
                                                                संतान विवरण
                                                            </h5>
                                                            <div class="text-sm text-blue-800 dark:text-blue-200 whitespace-pre-line">
                                                                {{ $ancestor['children_details'] }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                            
                    <!-- Current Member -->
                    <div class="mt-8 pt-6 border-t-2 border-blue-500">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                            वर्तमान सदस्य
                        </h3>
                        <div class="border border-blue-300 rounded p-4 bg-blue-50 dark:bg-blue-900/20">
                            <h4 class="font-bold text-lg text-blue-900 dark:text-blue-100 mb-2">
                                {{ $member->name }}
                            </h4>
                            <div class="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                                <div><strong>पिता:</strong> {{ $member->fathers_husband_name }}</div>
                                @if($member->birth_date)
                                    <div><strong>जन्म:</strong> {{ $member->birth_date->format('d/m/Y') }}</div>
                                @endif
                                @if($member->address)
                                    <div><strong>पता:</strong> {{ Str::limit($member->address, 50) }}</div>
                                @endif
                                @if($member->department_name)
                                    <div><strong>विभाग:</strong> {{ $member->department_name }}</div>
                                @endif
                                <div><strong>सदस्यता:</strong> {{ $member->membership_number }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- No Ancestors Message -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
                <div class="px-6 py-8 text-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">कोई पूर्वज जानकारी नहीं</h3>
                    <p class="mt-2 text-gray-500 dark:text-gray-400">
                        आपके सदस्यता आवेदन में पूर्वजों की जानकारी उपलब्ध नहीं है।
                    </p>
                </div>
            </div>
        @endif

        <!-- Children Section -->
        @if($member->children && count($member->children) > 0)
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">
                        संतान विवरण
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($member->children as $child)
                            <div class="border border-gray-300 dark:border-navy-600 rounded p-3 bg-gray-50 dark:bg-navy-700">
                                <h4 class="font-bold text-gray-900 dark:text-white">{{ $child['name'] ?? 'N/A' }}</h4>
                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                    <div>{{ $child['gender'] === 'male' ? 'पुत्र' : 'पुत्री' }}</div>
                                    @if(!empty($child['dob']))
                                        <div>जन्म: {{ \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') }}</div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<style>
    body {
        font-family: 'Noto Sans Devanagari', Arial, sans-serif;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .bg-white {
            background: white !important;
        }

        .border {
            border: 1px solid #000 !important;
        }

        .text-gray-900 {
            color: #000 !important;
        }

        .text-gray-600 {
            color: #333 !important;
        }

        .bg-gray-50 {
            background: #f9f9f9 !important;
        }

        .bg-blue-50 {
            background: #e6f3ff !important;
        }

        .text-blue-900 {
            color: #1e3a8a !important;
        }

        .text-blue-700 {
            color: #1d4ed8 !important;
        }

        .text-blue-800 {
            color: #1e40af !important;
        }

        .text-blue-100 {
            color: #dbeafe !important;
        }

        .text-blue-200 {
            color: #bfdbfe !important;
        }

        .border-blue-500 {
            border-color: #3b82f6 !important;
        }

        .border-blue-300 {
            border-color: #93c5fd !important;
        }

        .border-blue-200 {
            border-color: #bfdbfe !important;
        }

        .border-blue-700 {
            border-color: #1d4ed8 !important;
        }
    }
</style>

<script>
    // Pass family tree data to JavaScript
    window.familyTreeData = @json($familyTreeData);

    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between modern and legacy views
        const toggleBtn = document.getElementById('toggle-view-btn');
        const modernView = document.getElementById('family-tree-chart').closest('.bg-white');
        const legacyView = document.getElementById('legacy-tree');
        let isModernView = true;

        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                if (isModernView) {
                    modernView.style.display = 'none';
                    if (legacyView) {
                        legacyView.style.display = 'block';
                    }
                    toggleBtn.textContent = 'आधुनिक दृश्य';
                    isModernView = false;
                } else {
                    modernView.style.display = 'block';
                    if (legacyView) {
                        legacyView.style.display = 'none';
                    }
                    toggleBtn.textContent = 'दृश्य बदलें';
                    isModernView = true;
                }
            });
        }

        // Initialize family tree if data is available
        if (window.familyTreeData && window.familyTreeData.nodeDataArray && window.familyTreeData.nodeDataArray.length > 0) {
            // The family tree will be initialized by the family-tree.js module
            console.log('Family tree data loaded:', window.familyTreeData);
        } else {
            // Show message if no data
            const chartContainer = document.getElementById('family-tree-chart');
            if (chartContainer) {
                chartContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🌳</div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">कोई पारिवारिक डेटा उपलब्ध नहीं</h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                आपके सदस्यता आवेदन में पूर्वजों की जानकारी उपलब्ध नहीं है।
                            </p>
                        </div>
                    </div>
                `;
            }
        }
    });
</script>
@endsection
