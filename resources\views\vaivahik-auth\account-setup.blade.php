@extends('layouts.app')

@section('title', 'खाता सेटअप - वैवाहिक पंजीयन')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-lg p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-900">खाता सेटअप आवश्यक</h2>
                <p class="mt-2 text-gray-600">आपकी वैवाहिक प्रोफाइल के लिए लॉगिन खाता बनाना आवश्यक है</p>
            </div>

            <!-- Information Cards -->
            <div class="space-y-6 mb-8">
                <!-- Current Situation -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-600 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-yellow-800 mb-2">वर्तमान स्थिति</h3>
                            <p class="text-yellow-700">
                                यदि आपने पहले वैवाहिक पंजीयन कराया था लेकिन ईमेल और पासवर्ड नहीं दिया था, 
                                तो अब आपको अपना खाता बनाना होगा।
                            </p>
                        </div>
                    </div>
                </div>

                <!-- New Policy -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-blue-600 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-blue-800 mb-2">नई नीति</h3>
                            <p class="text-blue-700">
                                अब सभी वैवाहिक पंजीयन के लिए ईमेल और पासवर्ड आवश्यक है। 
                                इससे आप अपनी प्रोफाइल का बेहतर प्रबंधन कर सकेंगे।
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Benefits -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-green-600 mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-green-800 mb-2">खाता बनाने के फायदे</h3>
                            <ul class="text-green-700 space-y-1">
                                <li>• रुचि अनुरोधों का प्रबंधन करें</li>
                                <li>• अपनी प्रोफाइल को अपडेट करें</li>
                                <li>• संपर्क जानकारी सुरक्षित रखें</li>
                                <li>• डैशबोर्ड से सभी गतिविधियां देखें</li>
                                <li>• अन्य प्रोफाइल्स में रुचि व्यक्त करें</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Options -->
            <div class="space-y-4">
                <h3 class="text-xl font-semibold text-gray-900 text-center mb-6">आगे क्या करें?</h3>
                
                <!-- Option 1: New Registration -->
                <div class="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">नया वैवाहिक पंजीयन</h4>
                            <p class="text-gray-600 mb-4">
                                यदि आपने पहले पंजीयन नहीं कराया है या नया पंजीयन कराना चाहते हैं
                            </p>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    पूरी जानकारी भरें
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    तुरंत खाता बनेगा
                                </span>
                            </div>
                        </div>
                        <div class="ml-6">
                            <a href="{{ route('sadasya.vaivahik-panjiyan.new') }}" 
                               class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                नया पंजीयन
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Option 2: Contact Admin -->
                <div class="border border-gray-200 rounded-lg p-6 hover:border-orange-300 transition-colors">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">व्यवस्थापक से संपर्क करें</h4>
                            <p class="text-gray-600 mb-4">
                                यदि आपकी प्रोफाइल पहले से मौजूद है और आप खाता जोड़ना चाहते हैं
                            </p>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    मौजूदा प्रोफाइल
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    व्यवस्थापक सहायता
                                </span>
                            </div>
                        </div>
                        <div class="ml-6">
                            <a href="{{ route('sampark-sujhav') }}" 
                               class="inline-flex items-center px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                संपर्क करें
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">सहायता चाहिए?</h4>
                    <p class="text-gray-600 mb-4">
                        यदि आपको कोई समस्या आ रही है या आपके कोई प्रश्न हैं, तो कृपया हमसे संपर्क करें।
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4">
                        <a href="{{ route('sampark-sujhav') }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            संपर्क करें
                        </a>
                        <a href="{{ route('sadasya.vaivahik-panjiyan') }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                            </svg>
                            प्रोफाइल सूची देखें
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
