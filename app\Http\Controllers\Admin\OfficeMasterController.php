<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OfficeMaster;
use Illuminate\Http\Request;

class OfficeMasterController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = OfficeMaster::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_english', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $officeMasters = $query->ordered()->paginate(15);

        return view('admin.office-masters.index', compact('officeMasters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.office-masters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:office_masters,name',
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            OfficeMaster::create($validated);

            return redirect()->route('admin.office-masters.index')
                           ->with('success', 'कार्यालय प्रकार सफलतापूर्वक जोड़ा गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'कार्यालय प्रकार जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(OfficeMaster $officeMaster)
    {
        return view('admin.office-masters.show', compact('officeMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OfficeMaster $officeMaster)
    {
        return view('admin.office-masters.edit', compact('officeMaster'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OfficeMaster $officeMaster)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:office_masters,name,' . $officeMaster->id,
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'category' => 'nullable|string|max:255',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $officeMaster->update($validated);

            return redirect()->route('admin.office-masters.index')
                           ->with('success', 'कार्यालय प्रकार सफलतापूर्वक अपडेट किया गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'कार्यालय प्रकार अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OfficeMaster $officeMaster)
    {
        try {
            // Check if this office type is being used
            $usageCount = $officeMaster->memberships()->count() + 
                         $officeMaster->vaivahikPanjiyans()->count();
            
            if ($usageCount > 0) {
                return back()->with('error', 'यह कार्यालय प्रकार उपयोग में है, इसे हटाया नहीं जा सकता।');
            }

            $officeMaster->delete();

            return redirect()->route('admin.office-masters.index')
                           ->with('success', 'कार्यालय प्रकार सफलतापूर्वक हटाया गया।');

        } catch (\Exception $e) {
            return back()->with('error', 'कार्यालय प्रकार हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
