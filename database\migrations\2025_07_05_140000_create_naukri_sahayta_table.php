<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('naukri_sahayta', function (Blueprint $table) {
            $table->id();
            $table->string('naam');
            $table->string('pita_ka_naam');
            $table->text('pata');
            $table->integer('umra');
            $table->text('ahartayen');
            $table->text('ruchi');
            $table->string('mobile_number');
            $table->string('biodata')->nullable(); // File path for uploaded biodata
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('naukri_sahayta');
    }
};
