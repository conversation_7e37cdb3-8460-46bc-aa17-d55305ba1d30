@extends('layouts.admin') @section('title', 'यादव वर्ग संपादित करें') @section('content') <div class="min-h-screen bg-gray-50 py-6"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Header --> <div class="admin-card p-6 mb-6"> <div class="flex items-center justify-between"> <div> <h1 class="text-2xl font-bold admin-text-primary">यादव वर्ग संपादित करें</h1> <p class="mt-1 text-sm admin-text-secondary">{{ $yadavVarg->name }} को संपादित करें</p> </div> <a href="{{ route('admin.yadav-vargs.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path> </svg> वापस जाएं </a> </div> </div> <!-- Error Messages --> @if($errors->any()) <div class="admin-card p-4 mb-6 bg-red-50 border-l-4 border-red-400"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> @foreach($errors->all() as $error) <p class="text-sm text-red-700">{{ $error }}</p> @endforeach </div> </div> </div> @endif <!-- Form --> <div class="admin-card p-6"> <form method="POST" action="{{ route('admin.yadav-vargs.update', $yadavVarg->id) }}" class="space-y-6"> @csrf @method('PUT') <!-- Name --> <div> <label for="name" class="block text-sm font-medium admin-text-primary mb-2"> नाम <span class="text-red-500">*</span> </label> <input type="text" id="name" name="name" value="{{ old('name', $yadavVarg->name) }}" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('name') border-red-500 @enderror" placeholder="उदाहरण: अहीर, गोप, यादव, गोवाल"> @error('name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- English Name --> <div> <label for="name_english" class="block text-sm font-medium admin-text-primary mb-2"> अंग्रेजी नाम </label> <input type="text" id="name_english" name="name_english" value="{{ old('name_english', $yadavVarg->name_english) }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('name_english') border-red-500 @enderror" placeholder="उदाहरण: Ahir, Gop, Yadav, Gowal"> @error('name_english') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Category --> <div> <label for="category" class="block text-sm font-medium admin-text-primary mb-2"> श्रेणी </label> <input type="text" id="category" name="category" value="{{ old('category', $yadavVarg->category) }}" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('category') border-red-500 @enderror" placeholder="उदाहरण: पारंपरिक, क्षेत्रीय, ऐतिहासिक"> @error('category') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Description --> <div> <label for="description" class="block text-sm font-medium admin-text-primary mb-2"> विवरण </label> <textarea id="description" name="description" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('description') border-red-500 @enderror" placeholder="यादव वर्ग के बारे में विस्तृत जानकारी...">{{ old('description', $yadavVarg->description) }}</textarea> @error('description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div> <label for="display_order" class="block text-sm font-medium admin-text-primary mb-2"> प्रदर्शन क्रम </label> <input type="number" id="display_order" name="display_order" value="{{ old('display_order', $yadavVarg->display_order) }}" min="0" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent admin-input @error('display_order') border-red-500 @enderror" placeholder="0"> @error('display_order') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror <p class="mt-1 text-sm admin-text-secondary">ड्रॉपडाउन में दिखाने का क्रम (0 = सबसे पहले)</p> </div> <!-- Active Status --> <div class="flex items-center"> <input type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $yadavVarg->is_active) ? 'checked' : '' }} class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"> <label for="is_active" class="ml-2 block text-sm admin-text-primary"> सक्रिय स्थिति </label> </div> <!-- Submit Buttons --> <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200"> <a href="{{ route('admin.yadav-vargs.index') }}" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"> रद्द करें </a> <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"> अपडेट करें </button> </div> </form> </div> </div> </div> @endsection 