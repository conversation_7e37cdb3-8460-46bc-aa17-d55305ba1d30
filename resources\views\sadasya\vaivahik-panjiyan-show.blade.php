@extends('layouts.app')

@section('title', 'प्रोफाइल विवरण - ' . $vaivahikData->naam)

@section('content')
    <div class="bg-gray-50 min-h-screen py-6">
        <div class="container mx-auto px-4 max-w-7xl">
            <!-- Back Navigation -->
            <div class="mb-6">
                <a href="{{ route('sadasya.vaivahik-panjiyan') }}" 
                   class="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    वापस जाएं
                </a>
            </div>

            <!-- Profile Header -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
                <div class="bg-gradient-to-r {{ $vaivahikData->ling == 'पुरुष' ? 'from-indigo-500 to-purple-600' : 'from-pink-500 to-rose-600' }} px-6 lg:px-12 py-8 lg:py-12">
                    <div class="flex flex-col lg:flex-row items-center space-y-6 lg:space-y-0 lg:space-x-12">
                        <!-- Profile Picture -->
                        <div class="flex-shrink-0">
                            @if($vaivahikData->photo1)
                                <img src="{{ asset('storage/' . $vaivahikData->photo1) }}"
                                     alt="{{ $vaivahikData->naam }}"
                                     class="w-32 h-32 lg:w-48 lg:h-48 rounded-full border-4 border-white/40 object-cover shadow-lg">
                            @else
                                <div class="w-32 h-32 lg:w-48 lg:h-48 bg-white/20 rounded-full flex items-center justify-center border-4 border-white/40">
                                    <svg class="w-16 h-16 lg:w-24 lg:h-24 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Basic Info -->
                        <div class="text-center lg:text-left text-white flex-1">
                            <h1 class="text-3xl lg:text-5xl font-bold mb-4">{{ $vaivahikData->naam }}</h1>
                            <div class="flex flex-col lg:flex-row items-center lg:items-start space-y-3 lg:space-y-0 lg:space-x-6 text-lg lg:text-xl mb-6">
                                <span class="inline-flex items-center bg-white/20 px-4 py-2 rounded-full">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6a2 2 0 114 0v1H8V6z" clip-rule="evenodd"/>
                                    </svg>
                                    {{ $vaivahikData->ling }}
                                </span>
                                @if($vaivahikData->age)
                                    <span class="inline-flex items-center bg-white/20 px-4 py-2 rounded-full">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                        </svg>
                                        {{ $vaivahikData->age }} वर्ष
                                    </span>
                                @endif
                                @if($vaivahikData->uchai)
                                    <span class="inline-flex items-center bg-white/20 px-4 py-2 rounded-full">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                        {{ $vaivahikData->uchai }}
                                    </span>
                                @endif
                            </div>

                            <!-- Quick Info Tags -->
                            <div class="flex flex-wrap justify-center lg:justify-start gap-2 text-sm">
                                @if($vaivahikData->saikshanik_yogyata)
                                    <span class="bg-white/15 px-3 py-1 rounded-full">{{ Str::limit($vaivahikData->saikshanik_yogyata, 20) }}</span>
                                @endif
                                @if($vaivahikData->upjivika)
                                    <span class="bg-white/15 px-3 py-1 rounded-full">{{ Str::limit($vaivahikData->upjivika, 20) }}</span>
                                @elseif($vaivahikData->vartaman_karya)
                                    <span class="bg-white/15 px-3 py-1 rounded-full">{{ Str::limit($vaivahikData->vartaman_karya, 20) }}</span>
                                @endif
                                @if($vaivahikData->jati)
                                    <span class="bg-white/15 px-3 py-1 rounded-full">{{ $vaivahikData->jati }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex-shrink-0 flex flex-col space-y-3">
                            @if($isLoggedIn)
                            <button id="expressInterestBtn"
                                    class="bg-white text-{{ $vaivahikData->ling == 'पुरुष' ? 'indigo' : 'pink' }}-600 hover:bg-gray-100 px-6 py-3 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                                </svg>
                                रुचि व्यक्त करें
                            </button>

                            <button id="contactInfoBtn"
                                    class="bg-white/20 text-white hover:bg-white/30 px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                </svg>
                                संपर्क देखें
                            </button>

                            @if(!$vaivahikData->owner_email)
                                <a href="{{ route('vaivahik.register') }}"
                                   class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-200 flex items-center text-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                    </svg>
                                    खाता बनाएं
                                </a>
                            @endif
                            @else
                                <button onclick="showLoginPrompt()"
                                        class="bg-white text-gray-600 hover:bg-gray-100 px-6 py-3 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd"/>
                                    </svg>
                                    पूर्ण विवरण के लिए लॉगिन करें
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 lg:gap-8">
                <!-- Personal Information -->
                <div class="xl:col-span-3 space-y-6 lg:space-y-8">
                    @if(!$isLoggedIn)
                        <!-- Limited Information for Non-Logged Users -->
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"/>
                                </svg>
                                बुनियादी जानकारी
                            </h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Name (already shown in header) -->
                                <!-- Gender (already shown in header) -->

                                <!-- Birth Date -->
                                @if($vaivahikData->janmatithi)
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">जन्म तिथि</p>
                                            <p class="font-semibold">{{ $vaivahikData->birth_date_string }}</p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Education -->
                                @if($vaivahikData->saikshanik_yogyata)
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051l.94.43a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">शिक्षा</p>
                                            <p class="font-semibold">{{ $vaivahikData->saikshanik_yogyata }}</p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Occupation -->
                                @if($vaivahikData->upjivika || $vaivahikData->vartaman_karya)
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">आजीविका</p>
                                            <p class="font-semibold">{{ $vaivahikData->upjivika ?: $vaivahikData->vartaman_karya }}</p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Address -->
                                @if($vaivahikData->pata)
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">स्थायी पता</p>
                                            <p class="font-semibold">{{ $vaivahikData->pata }}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Login Prompt -->
                            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                    <p class="text-sm text-yellow-800">
                                        <strong>पूर्ण विवरण देखने के लिए लॉगिन करें।</strong> अधिक जानकारी, पारिवारिक विवरण, और संपर्क सुविधाओं के लिए कृपया अपने खाते में लॉगिन करें।
                                    </p>
                                </div>
                            </div>
                        </div>
                    @else
                    <!-- Basic Details Card -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"/>
                            </svg>
                            व्यक्तिगत जानकारी
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @if($vaivahikData->janmatithi)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">जन्म तिथि</p>
                                        <p class="font-semibold">{{ $vaivahikData->birth_date_string }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->janmasamay_din)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">जन्म समय/दिन</p>
                                        <p class="font-semibold">{{ $vaivahikData->janmasamay_din }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->uchai)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">ऊंचाई</p>
                                        <p class="font-semibold">{{ $vaivahikData->uchai }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Family Information Card -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            पारिवारिक जानकारी
                        </h2>
                        <div class="space-y-4">
                            @if($vaivahikData->pita_ka_naam)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">पिता का नाम</p>
                                        <p class="font-semibold">{{ $vaivahikData->pita_ka_naam }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->mata_ka_naam)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">माता का नाम</p>
                                        <p class="font-semibold">{{ $vaivahikData->mata_ka_naam }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->bhai_bahan_vivran)
                                <div class="flex items-start space-x-3">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">भाई-बहन का विवरण</p>
                                        <p class="font-semibold">{{ $vaivahikData->bhai_bahan_vivran }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->family_member_membership_number)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">पारिवारिक सदस्यता संख्या</p>
                                        <p class="font-semibold">{{ $vaivahikData->family_member_membership_number }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Education & Career Card -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051l.94.43a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                <path d="M3.64 8.26l-.94-.43a1 1 0 00-.787 0l-7 3a1 1 0 000 1.838l7 3a1 1 0 00.787 0l.94-.43-7-3 7-3z"/>
                            </svg>
                            शिक्षा एवं व्यवसाय
                        </h2>
                        <div class="space-y-4">
                            @if($vaivahikData->saikshanik_yogyata)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051l.94.43a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">शैक्षणिक योग्यता</p>
                                        <p class="font-semibold">{{ $vaivahikData->saikshanik_yogyata }}</p>
                                    </div>
                                </div>
                            @endif

                            @if($vaivahikData->upjivika)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">उपजीविका/व्यवसाय</p>
                                        <p class="font-semibold">{{ $vaivahikData->upjivika }}</p>
                                    </div>
                                </div>
                            @elseif($vaivahikData->vartaman_karya)
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M6 6V5a3 3 0 713-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">वर्तमान कार्य</p>
                                        <p class="font-semibold">{{ $vaivahikData->vartaman_karya }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Cultural Information Card -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                            </svg>
                            सांस्कृतिक जानकारी
                        </h2>
                        <div class="space-y-3">
                            @if($vaivahikData->jati)
                                <div>
                                    <p class="text-sm text-gray-500">जाति</p>
                                    <p class="font-semibold">{{ $vaivahikData->jati }}</p>
                                </div>
                            @endif

                            @if($vaivahikData->upjati)
                                <div>
                                    <p class="text-sm text-gray-500">उपजाति</p>
                                    <p class="font-semibold">{{ $vaivahikData->upjati }}</p>
                                </div>
                            @endif

                            @if($vaivahikData->gotra)
                                <div>
                                    <p class="text-sm text-gray-500">गोत्र</p>
                                    <p class="font-semibold">{{ $vaivahikData->gotra }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Location Card -->
                    @if($vaivahikData->pata)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                <svg class="w-6 h-6 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                                पता
                            </h2>
                            <p class="text-gray-700">{{ $vaivahikData->pata }}</p>
                        </div>
                    @endif

                    <!-- Contact Information (Dynamic) -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                            </svg>
                            संपर्क जानकारी
                        </h2>
                        <div id="contactInfoContainer">
                            <div id="contactRestricted" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                    <p class="text-sm text-yellow-800">संपर्क विवरण केवल रुचि व्यक्त करने के बाद उपलब्ध होगा।</p>
                                </div>
                            </div>

                            <div id="contactAvailable" class="hidden bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center mb-3">
                                    <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <p class="text-sm text-green-800 font-semibold">संपर्क विवरण उपलब्ध है:</p>
                                </div>
                                <div id="contactDetails" class="space-y-2">
                                    <!-- Contact details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Gallery -->
            @php
                $photos = collect([
                    $vaivahikData->photo1,
                    $vaivahikData->photo2,
                    $vaivahikData->photo3,
                    $vaivahikData->photo4
                ])->filter();
            @endphp

            @if($photos->count() > 1)
                <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                        फोटो गैलरी
                    </h2>

                    <!-- Mobile Photo Gallery (Horizontal Scroll) -->
                    <div class="block md:hidden">
                        <div class="flex space-x-4 overflow-x-auto pb-4">
                            @foreach($photos as $index => $photo)
                                <div class="flex-shrink-0">
                                    <img src="{{ asset('storage/' . $photo) }}"
                                         alt="Photo {{ $index + 1 }}"
                                         class="w-32 h-40 object-cover rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-shadow"
                                         onclick="openPhotoModal('{{ asset('storage/' . $photo) }}', '{{ $vaivahikData->naam }} - Photo {{ $index + 1 }}')">
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Desktop Photo Gallery (Grid) -->
                    <div class="hidden md:grid grid-cols-2 lg:grid-cols-4 gap-4">
                        @foreach($photos as $index => $photo)
                            <div class="aspect-w-3 aspect-h-4">
                                <img src="{{ asset('storage/' . $photo) }}"
                                     alt="Photo {{ $index + 1 }}"
                                     class="w-full h-48 object-cover rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-shadow"
                                     onclick="openPhotoModal('{{ asset('storage/' . $photo) }}', '{{ $vaivahikData->naam }} - Photo {{ $index + 1 }}')">
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif


        </div>
    </div>

    <!-- Photo Modal -->
    <div id="photoModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button onclick="closePhotoModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
            <p id="modalCaption" class="text-white text-center mt-4 text-lg"></p>
        </div>
    </div>

    <script>
        // Photo Modal Functions
        function openPhotoModal(imageSrc, caption) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('modalCaption').textContent = caption;
            document.getElementById('photoModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closePhotoModal() {
            document.getElementById('photoModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside the image
        document.getElementById('photoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePhotoModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePhotoModal();
            }
        });

        // Interest Request System
        const profileId = {{ $vaivahikData->id }};

        // Check contact access on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkContactAccess();
        });

        // Express Interest Button Handler
        document.getElementById('expressInterestBtn').addEventListener('click', function() {
            const button = this;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<svg class="animate-spin w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>भेजा जा रहा है...';
            button.disabled = true;

            fetch(`/interest/submit/${profileId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    message: '' // Optional message can be added later
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    button.innerHTML = '<svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/></svg>भेजा गया';
                    button.classList.remove('bg-white', 'text-indigo-600', 'text-pink-600');
                    button.classList.add('bg-green-500', 'text-white');
                } else {
                    if (data.redirect) {
                        showNotification('कृपया पहले लॉगिन करें।', 'warning');
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 2000);
                    } else {
                        showNotification(data.message, 'error');
                        // If no account, show registration option
                        if (data.message.includes('लॉगिन')) {
                            setTimeout(() => {
                                if (confirm('क्या आप अपना खाता बनाना चाहते हैं?')) {
                                    window.location.href = '{{ route("vaivahik.register") }}';
                                } else {
                                    window.location.href = '{{ route("vaivahik.login") }}';
                                }
                            }, 2000);
                        } else {
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('कुछ गलत हुआ। कृपया पुनः प्रयास करें।', 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });

        // Contact Info Button Handler
        document.getElementById('contactInfoBtn').addEventListener('click', function() {
            checkContactAccess();
        });

        // Check Contact Access Function
        function checkContactAccess() {
            fetch(`/interest/check-access/${profileId}`)
            .then(response => response.json())
            .then(data => {
                if (data.hasAccess) {
                    showContactInfo(data.contactInfo);
                } else {
                    showContactRestricted(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showContactRestricted('संपर्क जानकारी लोड करने में त्रुटि।');
            });
        }

        // Show Contact Info
        function showContactInfo(contactInfo) {
            document.getElementById('contactRestricted').classList.add('hidden');
            document.getElementById('contactAvailable').classList.remove('hidden');

            const contactDetails = document.getElementById('contactDetails');
            contactDetails.innerHTML = `
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                    <div>
                        <p class="text-sm text-gray-600">मोबाइल नंबर</p>
                        <p class="font-semibold text-lg">${contactInfo.mobile}</p>
                    </div>
                </div>
            `;
        }

        // Show Contact Restricted
        function showContactRestricted(message) {
            document.getElementById('contactAvailable').classList.add('hidden');
            document.getElementById('contactRestricted').classList.remove('hidden');
            document.getElementById('contactRestricted').querySelector('p').textContent = message;
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Slide in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Slide out and remove
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 4000);
        }

        // Show Login Prompt Function
        function showLoginPrompt() {
            if (confirm('पूर्ण विवरण देखने के लिए कृपया सदस्यता लॉगिन करें। क्या आप लॉगिन पेज पर जाना चाहते हैं?')) {
                // Create a form to submit with the intended URL
                const form = document.createElement('form');
                form.method = 'GET';
                form.action = '{{ route("member.login") }}';

                const intendedInput = document.createElement('input');
                intendedInput.type = 'hidden';
                intendedInput.name = 'intended';
                intendedInput.value = window.location.href;

                form.appendChild(intendedInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
