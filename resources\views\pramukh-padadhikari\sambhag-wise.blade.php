@extends('layouts.app')

@section('title', 'संभाग वार प्रमुख पदाधिकारी')

@section('content')
<div class="min-h-screen bg-gray-50 py-2">
    <div class="max-w-7xl mx-auto px-2">

        <!-- Header -->
        <div class="text-center mb-3">
            <h2 class="text-lg sm:text-xl font-bold text-gray-800">संभाग स्तरीय प्रमुख पदाधिकारी</h2>
            <div class="w-12 h-0.5 bg-purple-400 mx-auto"></div>
        </div>

        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-sm p-2 mb-3 border border-gray-200">
            <form method="GET" action="{{ route('pramukh-padadhikari.sambhag-wise') }}" class="space-y-2">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <div>
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="नाम, पदनाम..."
                               class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:ring-1 focus:ring-purple-400 focus:border-purple-400">
                    </div>
                    <div>
                        <select name="division_code" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:ring-1 focus:ring-purple-400 focus:border-purple-400">
                            <option value="">सभी संभाग</option>
                            @foreach($divisions as $division)
                                <option value="{{ $division->division_code }}" {{ request('division_code') === $division->division_code ? 'selected' : '' }}>
                                    {{ $division->division_name_eng }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="flex justify-center gap-2">
                    <button type="submit" class="px-3 py-1.5 bg-purple-500 hover:bg-purple-600 text-white rounded-md text-xs font-medium">
                        खोजें
                    </button>
                    <a href="{{ route('pramukh-padadhikari.sambhag-wise') }}" class="px-3 py-1.5 bg-gray-400 hover:bg-gray-500 text-white rounded-md text-xs font-medium">
                        रीसेट
                    </a>
                </div>
            </form>
        </div>

        <!-- Results Count -->
        @if($pramukhPadadhikaris->total() > 0)
            <div class="bg-blue-50 rounded-lg p-2 mb-3 text-center border border-blue-200">
                <span class="text-sm font-semibold text-gray-700">
                    कुल <span class="font-bold text-blue-600">{{ $pramukhPadadhikaris->total() }}</span> पदाधिकारी मिले
                </span>
            </div>
        @endif

        <!-- Modern Mobile Cards View (mobile-first responsive design) -->
        <div class="lg:hidden space-y-4 px-4">
            @forelse($pramukhPadadhikaris as $pramukh)
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <!-- Header with gradient and profile -->
                    <div class="bg-gradient-to-br from-purple-600 via-pink-600 to-purple-700 p-4 relative overflow-hidden">
                        <!-- Background pattern -->
                        <div class="absolute inset-0 opacity-10">
                            <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"></div>
                        </div>

                        <div class="relative flex items-center space-x-4">
                            <!-- Profile Image -->
                            <div class="flex-shrink-0">
                                @if($pramukh->photo)
                                    <div class="relative">
                                        <img class="h-16 w-16 rounded-full object-cover border-3 border-white shadow-lg ring-2 ring-white/30"
                                             src="{{ asset('storage/' . $pramukh->photo) }}"
                                             alt="{{ $pramukh->naam }}">
                                        <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white"></div>
                                    </div>
                                @else
                                    <div class="h-16 w-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center shadow-lg ring-2 ring-white/30">
                                        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                    </div>
                                @endif
                            </div>

                            <!-- Name and Position -->
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-bold text-white truncate mb-1">{{ $pramukh->naam }}</h3>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/20 text-white backdrop-blur-sm">
                                        {{ $pramukh->sangathan_me_padnaam }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Section -->
                    <div class="p-4 space-y-3">
                        <!-- Division Info -->
                        @if($pramukh->division)
                        <div class="bg-purple-50 rounded-lg p-3 border-l-4 border-purple-500">
                            <div class="flex items-start space-x-2">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                                <div class="min-w-0 flex-1">
                                    <span class="text-xs font-medium text-gray-500 block">संभाग</span>
                                    <span class="text-sm font-semibold text-gray-800">{{ $pramukh->division->name }}</span>
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Department Info -->
                        @if($pramukh->vibhagiy_padnaam || $pramukh->vibhag_ka_naam)
                        <div class="bg-gray-50 rounded-lg p-3 border-l-4 border-indigo-500">
                            @if($pramukh->vibhagiy_padnaam)
                            <div class="flex items-start space-x-2 mb-2">
                                <div class="w-2 h-2 bg-indigo-500 rounded-full mt-1.5 flex-shrink-0"></div>
                                <div class="min-w-0 flex-1">
                                    <span class="text-xs font-medium text-gray-500 block">विभागीय पदनाम</span>
                                    <span class="text-sm font-semibold text-gray-800">{{ $pramukh->vibhagiy_padnaam }}</span>
                                </div>
                            </div>
                            @endif
                            @if($pramukh->vibhag_ka_naam)
                            <div class="flex items-start space-x-2">
                                <div class="w-2 h-2 bg-purple-500 rounded-full mt-1.5 flex-shrink-0"></div>
                                <div class="min-w-0 flex-1">
                                    <span class="text-xs font-medium text-gray-500 block">विभाग का नाम</span>
                                    <span class="text-sm font-semibold text-gray-800">{{ $pramukh->vibhag_ka_naam }}</span>
                                </div>
                            </div>
                            @endif
                        </div>
                        @endif

                        <!-- Contact Info -->
                        @if($pramukh->mobile_number)
                        <div class="flex items-center space-x-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <span class="text-xs font-medium text-gray-500 block">संपर्क नंबर</span>
                                <a href="tel:{{ $pramukh->mobile_number }}" class="text-sm font-semibold text-green-700 hover:text-green-800">
                                    {{ $pramukh->formatted_mobile }}
                                </a>
                            </div>
                        </div>
                        @endif

                        <!-- Additional Details -->
                        <div class="space-y-2">
                            @if($pramukh->vartaman_pata)
                            <div class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <div class="flex-1 min-w-0">
                                    <span class="text-xs font-medium text-gray-500 block">वर्तमान पता</span>
                                    <span class="text-sm text-gray-700 leading-snug">{{ Str::limit($pramukh->vartaman_pata, 80) }}</span>
                                </div>
                            </div>
                            @endif

                            @if($pramukh->isthayi_pata)
                            <div class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                                </svg>
                                <div class="flex-1 min-w-0">
                                    <span class="text-xs font-medium text-gray-500 block">स्थायी पता</span>
                                    <span class="text-sm text-gray-700 leading-snug">{{ Str::limit($pramukh->isthayi_pata, 60) }}</span>
                                </div>
                            </div>
                            @endif

                            @if($pramukh->sanchipt_vishesh)
                            <div class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div class="flex-1 min-w-0">
                                    <span class="text-xs font-medium text-gray-500 block">संक्षिप्त विशेष</span>
                                    <span class="text-sm text-gray-700 leading-snug">{{ Str::limit($pramukh->sanchipt_vishesh, 70) }}</span>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            @empty
                <div class="bg-white rounded-2xl shadow-lg p-8 text-center mx-4">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <p class="text-gray-600 text-lg font-medium">कोई संभाग स्तरीय पदाधिकारी नहीं मिला</p>
                    <p class="text-gray-500 text-sm mt-2">वर्तमान में कोई सक्रिय पदाधिकारी उपलब्ध नहीं है</p>
                </div>
            @endforelse
        </div>

        <!-- Desktop Table View (hidden on small screens) -->
        <div class="hidden lg:block bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-3 py-2">
                <h3 class="text-sm font-bold text-white">संभाग स्तरीय पदाधिकारी सूची</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">फोटो</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">नाम</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">संगठन में पदनाम</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">विभागीय पदनाम</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">विभाग का नाम</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">वर्तमान पता</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">स्थायी पता</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">संक्षिप्त विशेष</th>
                            <th class="px-3 py-3 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">संपर्क नंबर</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($pramukhPadadhikaris as $pramukh)
                            <tr class="hover:bg-gray-50">
                                <!-- Photo Column -->
                                <td class="px-3 py-3 whitespace-nowrap">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        @if($pramukh->photo)
                                            <img class="h-12 w-12 rounded-lg object-cover border border-gray-200"
                                                 src="{{ asset('storage/' . $pramukh->photo) }}"
                                                 alt="{{ $pramukh->naam }}">
                                        @else
                                            <div class="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <!-- Name Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm font-semibold text-gray-900">{{ $pramukh->naam }}</div>
                                </td>
                                <!-- Sangathan Me Padnaam Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-purple-600 bg-purple-50 px-2 py-1 rounded inline-block">{{ $pramukh->sangathan_me_padnaam ?? '-' }}</div>
                                </td>
                                <!-- Vibhagiy Padnaam Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900">{{ $pramukh->vibhagiy_padnaam ?? '-' }}</div>
                                </td>
                                <!-- Vibhag Ka Naam Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900">{{ $pramukh->vibhag_ka_naam ?? '-' }}</div>
                                </td>
                                <!-- Vartaman Pata Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900 max-w-xs">{{ Str::limit($pramukh->vartaman_pata ?? '-', 50) }}</div>
                                </td>
                                <!-- Isthayi Pata Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900 max-w-xs">{{ Str::limit($pramukh->isthayi_pata ?? '-', 50) }}</div>
                                </td>
                                <!-- Sanchipt Vishesh Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900 max-w-xs">{{ Str::limit($pramukh->sanchipt_vishesh ?? '-', 50) }}</div>
                                </td>
                                <!-- Contact Column -->
                                <td class="px-3 py-3">
                                    <div class="text-sm text-gray-900">{{ $pramukh->formatted_mobile }}</div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="px-3 py-6 text-center text-gray-500">
                                    <p class="text-sm">कोई प्रमुख पदाधिकारी नहीं मिला</p>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if($pramukhPadadhikaris->hasPages())
            <div class="mt-4">
                {{ $pramukhPadadhikaris->links('pagination.mobile-friendly') }}
            </div>
        @endif
    </div>
</div>
@endsection
