<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MembershipVargMaster;
use Illuminate\Http\Request;

class MembershipVargMasterController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = MembershipVargMaster::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_english', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $membershipVargs = $query->ordered()->paginate(15);

        return view('admin.membership-varg-masters.index', compact('membershipVargs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.membership-varg-masters.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:membership_varg_masters,name',
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'fee' => 'nullable|numeric|min:0',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            MembershipVargMaster::create($validated);

            return redirect()->route('admin.membership-varg-masters.index')
                           ->with('success', 'सदस्यता वर्ग सफलतापूर्वक जोड़ा गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'सदस्यता वर्ग जोड़ने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MembershipVargMaster $membershipVargMaster)
    {
        return view('admin.membership-varg-masters.show', compact('membershipVargMaster'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MembershipVargMaster $membershipVargMaster)
    {
        return view('admin.membership-varg-masters.edit', compact('membershipVargMaster'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MembershipVargMaster $membershipVargMaster)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:membership_varg_masters,name,' . $membershipVargMaster->id,
                'name_english' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'fee' => 'nullable|numeric|min:0',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
            ]);

            $validated['is_active'] = $request->has('is_active');
            $validated['display_order'] = $validated['display_order'] ?? 0;

            $membershipVargMaster->update($validated);

            return redirect()->route('admin.membership-varg-masters.index')
                           ->with('success', 'सदस्यता वर्ग सफलतापूर्वक अपडेट किया गया।');

        } catch (\Exception $e) {
            return back()->withInput()
                        ->with('error', 'सदस्यता वर्ग अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MembershipVargMaster $membershipVargMaster)
    {
        try {
            // Check if this varg is being used by any memberships
            if ($membershipVargMaster->memberships()->count() > 0) {
                return back()->with('error', 'यह सदस्यता वर्ग उपयोग में है, इसे हटाया नहीं जा सकता।');
            }

            $membershipVargMaster->delete();

            return redirect()->route('admin.membership-varg-masters.index')
                           ->with('success', 'सदस्यता वर्ग सफलतापूर्वक हटाया गया।');

        } catch (\Exception $e) {
            return back()->with('error', 'सदस्यता वर्ग हटाने में त्रुटि: ' . $e->getMessage());
        }
    }
}
