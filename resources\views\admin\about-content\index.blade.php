@extends('layouts.admin') @section('title', 'Manage About Content') @section('breadcrumbs') <div class="text-sm breadcrumbs text-gray-500"> <ul> <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li> <li>About Content</li> </ul> </div> @endsection @section('header') <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6"> <h1 class="text-2xl font-bold">About Content Management</h1> <a href="{{ route('admin.about-content.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-navy-600 hover:bg-navy-700:bg-navy-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /> </svg> Add New Content </a> </div> <!-- Page Filter --> <div class="flex flex-wrap gap-4 mb-6"> <div class="text-sm text-gray-700">Filter by page:</div> @foreach($pageOptions as $key => $label) <a href="{{ route('admin.about-content.index', ['page_key' => $key]) }}" class="px-3 py-1 text-sm rounded-full {{ $pageKey == $key ? 'bg-navy-100 text-navy-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200:bg-navy-700' }}"> {{ $label }} </a> @endforeach </div> @endsection @section('content') @if($contents->isEmpty()) <div class="text-center py-12"> <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /> </svg> <h3 class="mt-2 text-sm font-medium text-gray-700">No content found</h3> <p class="mt-1 text-sm text-gray-500">Get started by creating a new content section.</p> <div class="mt-6"> <a href="{{ route('admin.about-content.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-navy-600 hover:bg-navy-700:bg-navy-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"> <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /> </svg> Add New Content </a> </div> </div> @else <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead> <tr> <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th> <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th> <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th> <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th> <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @foreach($contents as $content) <tr> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"> {{ $content->title }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"> {{ $content->section_key ?? '-' }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"> {{ $content->display_order }} </td> <td class="px-6 py-4 whitespace-nowrap"> @if($content->is_published) <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"> Published </span> @else <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800"> Draft </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"> <a href="{{ route('admin.about-content.edit', $content->id) }}" class="text-navy-600 hover:text-navy-900:text-navy-300"> Edit </a> <form action="{{ route('admin.about-content.destroy', $content->id) }}" method="POST" class="inline-block ml-2"> @csrf @method('DELETE') <button type="submit" class="text-red-600 hover:text-red-900:text-red-300" onclick="return confirm('Are you sure you want to delete this item?')"> Delete </button> </form> </td> </tr> @endforeach </tbody> </table> </div> @endif @endsection 