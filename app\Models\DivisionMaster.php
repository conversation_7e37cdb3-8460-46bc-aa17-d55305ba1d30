<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DivisionMaster extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'division_code',
        'division_name_eng',
        'division_name_hin',
        'is_active',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active divisions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive divisions.
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('division_name_eng');
    }

    /**
     * Get districts that belong to this division.
     */
    public function districts()
    {
        return $this->hasMany(DistrictMaster::class, 'division_code', 'division_code');
    }

    /**
     * Get memberships that use this division.
     */
    public function memberships()
    {
        return $this->hasMany(Membership::class, 'division_master_id');
    }

    /**
     * Get vaivahik panjiyans that use this division.
     */
    public function vaivahikPanjiyans()
    {
        return $this->hasMany(VaivahikPanjiyan::class, 'division_master_id');
    }
}
