{"version": 3, "file": "jquery.orgchart.min.js", "sources": ["jquery.orgchart.js"], "sourcesContent": ["/*\n * jQ<PERSON><PERSON> Plugin\n * https://github.com/dabeng/OrgChart\n *\n * Copyright 2016, dabeng\n * https://github.com/dabeng\n *\n * Licensed under the MIT license:\n * http://www.opensource.org/licenses/MIT\n */\n'use strict';\n\n(function (factory) {\n  if (typeof module === 'object' && typeof module.exports === 'object') {\n    factory(require('jquery'), window, document);\n  } else {\n    factory(jQuery, window, document);\n  }\n}(function ($, window, document, undefined) {\n  var OrgChart = function (elem, opts) {\n    this.$chartContainer = $(elem);\n    this.opts = opts;\n    this.defaultOptions = {\n      'icons': {\n        'theme': 'oci',\n        'parentNode': 'oci-menu',\n        'expandToUp': 'oci-chevron-up',\n        'collapseToDown': 'oci-chevron-down',\n        'collapseToLeft': 'oci-chevron-left',\n        'expandToRight': 'oci-chevron-right',\n        'backToCompact': 'oci-corner-top-left',\n        'backToLoose': 'oci-corner-bottom-right',\n        'collapsed': 'oci-plus-square',\n        'expanded': 'oci-minus-square',\n        'spinner': 'oci-spinner'\n      },\n      'nodeTitle': 'name',\n      'nodeId': 'id',\n      'toggleSiblingsResp': false,\n      'visibleLevel': 999,\n      'chartClass': '',\n      'exportButton': false,\n      'exportButtonName': 'Export',\n      'exportFilename': 'OrgChart',\n      'exportFileextension': 'png',\n      'draggable': false,\n      'direction': 't2b',\n      'pan': false,\n      'zoom': false,\n      'zoominLimit': 7,\n      'zoomoutLimit': 0.5\n    };\n  };\n  //\n  OrgChart.prototype = {\n    //\n    init: function (opts) {\n      var that = this;\n      this.options = $.extend({}, this.defaultOptions, this.opts, opts);\n      // build the org-chart\n      var $chartContainer = this.$chartContainer;\n      if (this.$chart) {\n        this.$chart.remove();\n      }\n      var data = this.options.data;\n      var $chart = this.$chart = $('<div>', {\n        'data': { 'options': this.options },\n        'class': 'orgchart' + (this.options.chartClass !== '' ? ' ' + this.options.chartClass : '') + (this.options.direction !== 't2b' ? ' ' + this.options.direction : ''),\n        'click': function(event) {\n          if (!$(event.target).closest('.node').length) {\n            $chart.find('.node.focused').removeClass('focused');\n          }\n        }\n      });\n      if (typeof MutationObserver !== 'undefined') {\n        this.triggerInitEvent();\n      }\n      var $root = Array.isArray(data) ? $chart.append($('<ul class=\"nodes\"></ul>')).find('.nodes')\n        : $chart.append($('<ul class=\"nodes\"><li class=\"hierarchy\"></li></ul>')).find('.hierarchy');\n\n        if (data instanceof $) { // ul datasource\n          this.buildHierarchy($root, this.buildJsonDS(data.children()), 0, this.options);\n        } else { // local json datasource\n          if (data.relationship) {\n            this.buildHierarchy($root, data);\n          } else {\n            this.buildHierarchy($root, Array.isArray(data) ? data : this.attachRel(data, '00'));\n          }\n        }\n\n      $chartContainer.append($chart);\n\n      // append the export button\n      if (this.options.exportButton && !$('.oc-export-btn').length) {\n        this.attachExportButton();\n      }\n\n      if (this.options.pan) {\n        this.bindPan();\n      }\n\n      if (this.options.zoom) {\n        this.bindZoom();\n      }\n\n      return this;\n    },\n    handleCompactNodes: function () {\n      // caculate the compact nodes' level which is used to add different styles\n      this.$chart.find('.node.compact')\n        .each((index, node) => {\n          $(node).addClass($(node).parents('.compact').length % 2 === 0 ? 'even' : 'odd');\n        }); // the following code snippets is used to add direction arrows for the most top compact node, however the styles is not adjusted correctly\n        // .filter((index, node) => !$(node).parent().is('.compact'))\n        // .each((index, node) => {\n        //   $(node).append(`<i class=\"edge verticalEdge topEdge ${this.options.icons.theme}\"></i>`);\n        //   if (this.getSiblings($(node)).length) {\n        //     $(node).append(`<i class=\"edge horizontalEdge rightEdge ${this.options.icons.theme}\"></i><i class=\"edge horizontalEdge leftEdge ${this.options.icons.theme}\"></i>`);\n        //   }\n        // });\n    },\n    //\n    triggerInitEvent: function () {\n      var that = this;\n      var mo = new MutationObserver(function (mutations) {\n        mo.disconnect();\n        initTime:\n        for (var i = 0; i < mutations.length; i++) {\n          for (var j = 0; j < mutations[i].addedNodes.length; j++) {\n            if (mutations[i].addedNodes[j].classList.contains('orgchart')) {\n              that.handleCompactNodes();\n              if (that.options.initCompleted && typeof that.options.initCompleted === 'function') {\n                that.options.initCompleted(that.$chart);\n              }\n              var initEvent = $.Event('init.orgchart');\n              that.$chart.trigger(initEvent);\n              break initTime;\n            }\n          }\n        }\n      });\n      mo.observe(this.$chartContainer[0], { childList: true });\n    },\n    triggerShowEvent: function ($target, rel) {\n      var initEvent = $.Event('show-' + rel + '.orgchart');\n      $target.trigger(initEvent);\n    },\n    triggerHideEvent: function ($target, rel) {\n      var initEvent = $.Event('hide-' + rel + '.orgchart');\n      $target.trigger(initEvent);\n    },\n    // add export button for orgchart\n    attachExportButton: function () {\n      var that = this;\n      var $exportBtn = $('<button>', {\n        'class': 'oc-export-btn',\n        'text': this.options.exportButtonName,\n        'click': function(e) {\n          e.preventDefault();\n          that.export();\n        }\n      });\n      this.$chartContainer.after($exportBtn);\n    },\n    setOptions: function (opts, val) {\n      if (typeof opts === 'string') {\n        if (opts === 'pan') {\n          if (val) {\n            this.bindPan();\n          } else {\n            this.unbindPan();\n          }\n        }\n        if (opts === 'zoom') {\n          if (val) {\n            this.bindZoom();\n          } else {\n            this.unbindZoom();\n          }\n        }\n      }\n      if (typeof opts === 'object') {\n        if (opts.data) {\n          this.init(opts);\n        } else {\n          if (typeof opts.pan !== 'undefined') {\n            if (opts.pan) {\n              this.bindPan();\n            } else {\n              this.unbindPan();\n            }\n          }\n          if (typeof opts.zoom !== 'undefined') {\n            if (opts.zoom) {\n              this.bindZoom();\n            } else {\n              this.unbindZoom();\n            }\n          }\n        }\n      }\n\n      return this;\n    },\n    //\n    panStartHandler: function (e) {\n      var $chart = $(e.delegateTarget);\n      if ($(e.target).closest('.node').length || (e.touches && e.touches.length > 1)) {\n        $chart.data('panning', false);\n        return;\n      } else {\n        $chart.css('cursor', 'move').data('panning', true);\n      }\n      var lastX = 0;\n      var lastY = 0;\n      var lastTf = $chart.css('transform');\n      if (lastTf !== 'none') {\n        var temp = lastTf.split(',');\n        if (lastTf.indexOf('3d') === -1) {\n          lastX = parseInt(temp[4]);\n          lastY = parseInt(temp[5]);\n        } else {\n          lastX = parseInt(temp[12]);\n          lastY = parseInt(temp[13]);\n        }\n      }\n      var startX = 0;\n      var startY = 0;\n      if (!e.targetTouches) { // pand on desktop\n        startX = e.pageX - lastX;\n        startY = e.pageY - lastY;\n      } else if (e.targetTouches.length === 1) { // pan on mobile device\n        startX = e.targetTouches[0].pageX - lastX;\n        startY = e.targetTouches[0].pageY - lastY;\n      } else if (e.targetTouches.length > 1) {\n        return;\n      }\n      $chart.on('mousemove touchmove',function(e) {\n        if (!$chart.data('panning')) {\n          return;\n        }\n        var newX = 0;\n        var newY = 0;\n        if (!e.targetTouches) { // pand on desktop\n          newX = e.pageX - startX;\n          newY = e.pageY - startY;\n        } else if (e.targetTouches.length === 1) { // pan on mobile device\n          newX = e.targetTouches[0].pageX - startX;\n          newY = e.targetTouches[0].pageY - startY;\n        } else if (e.targetTouches.length > 1) {\n          return;\n        }\n        var lastTf = $chart.css('transform');\n        if (lastTf === 'none') {\n          if (lastTf.indexOf('3d') === -1) {\n            $chart.css('transform', 'matrix(1, 0, 0, 1, ' + newX + ', ' + newY + ')');\n          } else {\n            $chart.css('transform', 'matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, ' + newX + ', ' + newY + ', 0, 1)');\n          }\n        } else {\n          var matrix = lastTf.split(',');\n          if (lastTf.indexOf('3d') === -1) {\n            matrix[4] = ' ' + newX;\n            matrix[5] = ' ' + newY + ')';\n          } else {\n            matrix[12] = ' ' + newX;\n            matrix[13] = ' ' + newY;\n          }\n          $chart.css('transform', matrix.join(','));\n        }\n      });\n    },\n    //\n    panEndHandler: function (e) {\n      if (e.data.chart.data('panning')) {\n        e.data.chart.data('panning', false).css('cursor', 'default').off('mousemove');\n      }\n    },\n    //\n    bindPan: function () {\n      this.$chartContainer.css('overflow', 'hidden');\n      this.$chart.on('mousedown touchstart', this.panStartHandler);\n      $(document).on('mouseup touchend', { 'chart': this.$chart }, this.panEndHandler);\n    },\n    //\n    unbindPan: function () {\n      this.$chartContainer.css('overflow', 'auto');\n      this.$chart.off('mousedown touchstart', this.panStartHandler);\n      $(document).off('mouseup touchend', this.panEndHandler);\n    },\n    //\n    zoomWheelHandler: function (e) {\n      var oc = e.data.oc;\n      e.preventDefault();\n      var newScale  = 1 + (e.originalEvent.deltaY > 0 ? -0.2 : 0.2);\n      oc.setChartScale(oc.$chart, newScale);\n    },\n    //\n    zoomStartHandler: function (e) {\n      if(e.touches && e.touches.length === 2) {\n        var oc = e.data.oc;\n        oc.$chart.data('pinching', true);\n        var dist = oc.getPinchDist(e);\n        oc.$chart.data('pinchDistStart', dist);\n      }\n    },\n    zoomingHandler: function (e) {\n      var oc = e.data.oc;\n      if(oc.$chart.data('pinching')) {\n        var dist = oc.getPinchDist(e);\n        oc.$chart.data('pinchDistEnd', dist);\n      }\n    },\n    zoomEndHandler: function (e) {\n      var oc = e.data.oc;\n      if(oc.$chart.data('pinching')) {\n        oc.$chart.data('pinching', false);\n        var diff = oc.$chart.data('pinchDistEnd') - oc.$chart.data('pinchDistStart');\n        if (diff > 0) {\n          oc.setChartScale(oc.$chart, 1.2);\n        } else if (diff < 0) {\n          oc.setChartScale(oc.$chart, 0.8);\n        }\n      }\n    },\n    //\n    bindZoom: function () {\n      this.$chartContainer.on('wheel', { 'oc': this }, this.zoomWheelHandler);\n      this.$chartContainer.on('touchstart', { 'oc': this }, this.zoomStartHandler);\n      $(document).on('touchmove', { 'oc': this }, this.zoomingHandler);\n      $(document).on('touchend', { 'oc': this }, this.zoomEndHandler);\n    },\n    unbindZoom: function () {\n      this.$chartContainer.off('wheel', this.zoomWheelHandler);\n      this.$chartContainer.off('touchstart', this.zoomStartHandler);\n      $(document).off('touchmove', this.zoomingHandler);\n      $(document).off('touchend', this.zoomEndHandler);\n    },\n    //\n    getPinchDist: function (e) {\n      return Math.sqrt((e.touches[0].clientX - e.touches[1].clientX) * (e.touches[0].clientX - e.touches[1].clientX) +\n      (e.touches[0].clientY - e.touches[1].clientY) * (e.touches[0].clientY - e.touches[1].clientY));\n    },\n    //\n    setChartScale: function ($chart, newScale) {\n      var opts = $chart.data('options');\n      var lastTf = $chart.css('transform');\n      var matrix = '';\n      var targetScale = 1;\n      if (lastTf === 'none') {\n        $chart.css('transform', 'scale(' + newScale + ',' + newScale + ')');\n      } else {\n        matrix = lastTf.split(',');\n        if (lastTf.indexOf('3d') === -1) {\n          targetScale = Math.abs(window.parseFloat(matrix[3]) * newScale);\n          if (targetScale > opts.zoomoutLimit && targetScale < opts.zoominLimit) {\n            $chart.css('transform', lastTf + ' scale(' + newScale + ',' + newScale + ')');\n          }\n        } else {\n          targetScale = Math.abs(window.parseFloat(matrix[1]) * newScale);\n          if (targetScale > opts.zoomoutLimit && targetScale < opts.zoominLimit) {\n            $chart.css('transform', lastTf + ' scale3d(' + newScale + ',' + newScale + ', 1)');\n          }\n        }\n      }\n    },\n    //\n    buildJsonDS: function ($li) {\n      var that = this;\n      var subObj = {\n        'name': $li.contents().eq(0).text().trim(),\n        'relationship': ($li.parent().parent().is('li') ? '1': '0') + ($li.siblings('li').length ? 1: 0) + ($li.children('ul').length ? 1 : 0)\n      };\n      $.each($li.data(), function(key, value) {\n         subObj[key] = value;\n      });\n      $li.children('ul').children().each(function() {\n        if (!subObj.children) { subObj.children = []; }\n        subObj.children.push(that.buildJsonDS($(this)));\n      });\n      return subObj;\n    },\n    // process datasource and add necessary information\n    attachRel: function (data, flags) {\n      var that = this;\n      data.relationship = flags + (data.children && data.children.length > 0 ? 1 : 0);\n      if (this.options?.compact?.constructor === Function && this.options.compact(data)) {\n        data.compact = true;\n      }\n      if (data.children) {\n        data.children.forEach(function(item) {\n          if (data.hybrid || data.vertical) { // identify all the descendant nodes except the root node of hybrid structure\n            item.vertical = true;\n          } else if (data.compact && item.children) { // identify all the compact ancestor nodes\n            item.compact = true;\n          } else if (data.compact && !item.children) { // identify all the compact descendant nodes\n            item.associatedCompact = true;\n          }\n          that.attachRel(item, '1' + (data.children.length > 1 ? 1 : 0));\n        });\n      }\n      return data;\n    },\n    //\n    loopChart: function ($chart, includeNodeData) {\n      includeNodeData = (includeNodeData !== null && includeNodeData !== undefined) ? includeNodeData : false;\n      var that = this;\n      var $node = $chart.find('.node:first');\n      var subObj = { 'id': $node[0].id };\n      if (includeNodeData) {\n        $.each($node.data('nodeData'), function (key, value) {\n          subObj[key] = value;\n        });\n      }\n      $node.siblings('.nodes').children().each(function() {\n        if (!subObj.children) { subObj.children = []; }\n        subObj.children.push(that.loopChart($(this), includeNodeData));\n      });\n      return subObj;\n    },\n    //\n    getHierarchy: function (includeNodeData) {\n      includeNodeData = (includeNodeData !== null && includeNodeData !== undefined) ? includeNodeData : false;\n      if (typeof this.$chart === 'undefined') {\n        return 'Error: orgchart does not exist'\n      } else {\n        if (!this.$chart.find('.node').length) {\n          return 'Error: nodes do not exist'\n        } else {\n          var valid = true;\n          this.$chart.find('.node').each(function () {\n            if (!this.id) {\n              valid = false;\n              return false;\n            }\n          });\n          if (!valid) {\n            return 'Error: All nodes of orghcart to be exported must have data-id attribute!';\n          }\n        }\n      }\n      return this.loopChart(this.$chart, includeNodeData);\n    },\n    // detect the exist/display state of related node\n    getNodeState: function ($node, relation) {\n      var $target = {};\n      var isVerticalNode = !!$node.closest('vertical').length;\n      var relation = relation || 'self';\n      if (relation === 'parent') {\n        if (isVerticalNode) {\n          $target = $node.closest('ul').parents('ul');\n          if (!$target.length) {\n            $target = $node.closest('.nodes');\n            if (!$target.length) {\n              $target = $node.closest('.vertical').siblings(':first');\n            }\n          }\n        } else {\n          $target = $node.closest('.nodes').siblings('.node');\n        }\n        if ($target.length) {\n          if ($target.is('.hidden') || (!$target.is('.hidden') && $target.closest('.nodes').is('.hidden')) || (!$target.is('.hidden') && $target.closest('.vertical').is('.hidden'))) {\n            return { 'exist': true, 'visible': false };\n          }\n          return { 'exist': true, 'visible': true };\n        }\n      } else if (relation === 'children') {\n        $target = isVerticalNode ? $node.parent().children('ul') : $node.siblings('.nodes');\n        if ($target.length) {\n          if (!$target.is('.hidden')) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      } else if (relation === 'siblings') {\n        $target = isVerticalNode ? $node.closest('ul') : $node.parent().siblings();\n        if ($target.length && (!isVerticalNode || $target.children('li').length > 1)) {\n          if (!$target.is('.hidden') && !$target.parent().is('.hidden') && (!isVerticalNode || !$target.closest('.vertical').is('.hidden'))) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      } else {\n        $target = $node;\n        if ($target.length) {\n          if (!(($target.closest('.nodes').length && $target.closest('.nodes').is('.hidden')) ||\n            ($target.closest('.hierarchy').length && $target.closest('.hierarchy').is('.hidden')) ||\n            ($target.closest('.vertical').length && ($target.closest('.nodes').is('.hidden') || $target.closest('.vertical').is('.hidden')))\n          )) {\n            return { 'exist': true, 'visible': true };\n          }\n          return { 'exist': true, 'visible': false };\n        }\n      }\n      return { 'exist': false, 'visible': false };\n    },\n    getParent: function ($node) {\n      return this.getRelatedNodes($node, 'parent');\n    },\n    getChildren: function ($node) {\n      return this.getRelatedNodes($node, 'children');\n    },\n    getSiblings: function ($node) {\n      return this.getRelatedNodes($node, 'siblings');\n    },\n    // find the related nodes\n    getRelatedNodes: function ($node, relation) {\n      if (!$node || !($node instanceof $) || !$node.is('.node')) {\n        return $();\n      }\n      if (relation === 'parent') {\n        return $node.closest('.nodes').siblings('.node');\n      } else if (relation === 'children') {\n        return $node.siblings('.nodes').children('.hierarchy').find('.node:first');\n      } else if (relation === 'siblings') {\n        return $node.closest('.hierarchy').siblings().find('.node:first');\n      } else {\n        return $();\n      }\n    },\n    hideParentEnd: function (event) {\n      $(event.target).removeClass('sliding');\n      event.data.parent.addClass('hidden');\n    },\n    // recursively hide the ancestor node and sibling nodes of the specified node\n    hideParent: function ($node) {\n      var $parent = $node.closest('.nodes').siblings('.node');\n      if ($parent.find('.spinner').length) {\n        $node.closest('.orgchart').data('inAjax', false);\n      }\n      // hide the sibling nodes\n      if (this.getNodeState($node, 'siblings').visible) {\n        this.hideSiblings($node);\n      }\n      // hide the lines\n      $node.parent().addClass('isAncestorsCollapsed');\n      // hide the superior nodes with transition\n      if (this.getNodeState($parent).visible) {\n        $parent.addClass('sliding slide-down').one('transitionend', { 'parent': $parent }, this.hideParentEnd);\n      }\n      // if the current node has the parent node, hide it recursively\n      if (this.getNodeState($parent, 'parent').visible) {\n        this.hideParent($parent);\n      }\n    },\n    showParentEnd: function (event) {\n      var $node = event.data.node;\n      $(event.target).removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.topEdge'));\n      }\n    },\n    // show the parent node of the specified node\n    showParent: function ($node) {\n      // just show only one superior level\n      var $parent = $node.closest('.nodes').siblings('.node').removeClass('hidden');\n      // just show only one line\n      $node.closest('.hierarchy').removeClass('isAncestorsCollapsed');\n      // show parent node with animation\n      this.repaint($parent[0]);\n      $parent.addClass('sliding').removeClass('slide-down').one('transitionend', { 'node': $node }, this.showParentEnd.bind(this));\n    },\n    stopAjax: function ($nodeLevel) {\n      if ($nodeLevel.find('.spinner').length) {\n        $nodeLevel.closest('.orgchart').data('inAjax', false);\n      }\n    },\n    isVisibleNode: function (index, elem) {\n      return this.getNodeState($(elem)).visible;\n    },\n    isCompactDescendant: function (index, elem) {\n      return $(elem).parent().is('.node.compact');\n    },\n    // do some necessary cleanup tasks when hide animation is finished\n    hideChildrenEnd: function (event) {\n      var $node = event.data.node;\n      event.data.animatedNodes.removeClass('sliding');\n      event.data.animatedNodes.closest('.nodes').addClass('hidden');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // recursively hide the descendant nodes of the specified node\n    hideChildren: function ($node) {\n      $node.closest('.hierarchy').addClass('isChildrenCollapsed');\n      var $lowerLevel = $node.siblings('.nodes');\n      this.stopAjax($lowerLevel);\n      var $animatedNodes = $lowerLevel.find('.node').filter(this.isVisibleNode.bind(this)).not(this.isCompactDescendant.bind(this));\n      var isVerticalDesc = $lowerLevel.is('.vertical');\n      if (!isVerticalDesc) {\n        $animatedNodes.closest('.hierarchy').addClass('isCollapsedDescendant');\n      }\n      if ($lowerLevel.is('.vertical') || $lowerLevel.find('.vertical').length) {\n        $animatedNodes.find(this.options.icons.expanded).removeClass(this.options.icons.expanded).addClass(this.options.icons.collapsed);\n      }\n      this.repaint($animatedNodes.get(0));\n      $animatedNodes.addClass('sliding slide-up').eq(0).one('transitionend', { 'animatedNodes': $animatedNodes, 'lowerLevel': $lowerLevel, 'node': $node }, this.hideChildrenEnd.bind(this));\n    },\n    //\n    showChildrenEnd: function (event) {\n      var $node = event.data.node;\n      event.data.animatedNodes.removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // show the children nodes of the specified node\n    showChildren: function ($node) {\n      var that = this;\n      $node.closest('.hierarchy').removeClass('isChildrenCollapsed');\n      var $levels = $node.siblings('.nodes');\n      var isVerticalDesc = $levels.is('.vertical');\n      var $animatedNodes = isVerticalDesc\n        ? $levels.removeClass('hidden').find('.node').filter(this.isVisibleNode.bind(this))\n        : $levels.removeClass('hidden').children('.hierarchy').find('.node:first').filter(this.isVisibleNode.bind(this));\n      if (!isVerticalDesc) {\n        $animatedNodes.filter(':not(:only-child)').closest('.hierarchy').addClass('isChildrenCollapsed');\n        $animatedNodes.closest('.hierarchy').removeClass('isCollapsedDescendant');\n      }\n      // the two following statements are used to enforce browser to repaint\n      this.repaint($animatedNodes.get(0));\n      $animatedNodes.addClass('sliding').removeClass('slide-up').eq(0).one('transitionend', { 'node': $node, 'animatedNodes': $animatedNodes }, this.showChildrenEnd.bind(this));\n    },\n    //\n    hideSiblingsEnd: function (event) {\n      var that = this;\n      var $node = event.data.node;\n      var $nodeContainer = event.data.nodeContainer;\n      var direction = event.data.direction;\n      var $siblings = direction ? (direction === 'left' ? $nodeContainer.prevAll(':not(.hidden)') : $nodeContainer.nextAll(':not(.hidden)')) : $nodeContainer.siblings();\n      event.data.animatedNodes.removeClass('sliding');\n      $siblings.find('.node:gt(0)').filter(this.isVisibleNode.bind(this))\n        .removeClass('slide-left slide-right')\n        .addClass(function() {\n          if (that.options.compact) {\n            return '';\n          } else {\n            return 'slide-up';\n          }\n        });\n      $siblings.find('.nodes, .vertical').addClass('hidden')\n        .end().addClass('hidden');\n\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n      }\n    },\n    // hide the sibling nodes of the specified node\n    hideSiblings: function ($node, direction) {\n      var that = this;\n      var $nodeContainer = $node.closest('.hierarchy').addClass('isSiblingsCollapsed');\n      if ($nodeContainer.siblings().find('.spinner').length) {\n        $node.closest('.orgchart').data('inAjax', false);\n      }\n      if (direction) {\n        if (direction === 'left') {\n          $nodeContainer.addClass('left-sibs')\n            .prevAll('.isSiblingsCollapsed').removeClass('isSiblingsCollapsed left-sibs').end()\n            .prevAll().addClass('isCollapsedSibling isChildrenCollapsed')\n            .find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-right');\n        } else {\n          $nodeContainer.addClass('right-sibs')\n            .nextAll('.isSiblingsCollapsed').removeClass('isSiblingsCollapsed right-sibs').end()\n            .nextAll().addClass('isCollapsedSibling isChildrenCollapsed')\n            .find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-left');\n        }\n      } else {\n        $nodeContainer.prevAll().find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-right');\n        $nodeContainer.nextAll().find('.node').filter(this.isVisibleNode.bind(this)).addClass('sliding slide-left');\n        $nodeContainer.siblings().addClass('isCollapsedSibling isChildrenCollapsed');\n      }\n      var $animatedNodes = $nodeContainer.siblings().find('.sliding');\n      $animatedNodes.eq(0).one('transitionend', { 'node': $node, 'nodeContainer': $nodeContainer, 'direction': direction, 'animatedNodes': $animatedNodes }, this.hideSiblingsEnd.bind(this));\n    },\n    //\n    showSiblingsEnd: function (event) {\n      var $node = event.data.node;\n      event.data.visibleNodes.removeClass('sliding');\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n        $node.children('.topEdge').removeClass(this.options.icons.expandToUp).addClass(this.options.icons.collapseToDown);\n      }\n    },\n    //\n    showRelatedParentEnd: function(event) {\n      $(event.target).removeClass('sliding');\n    },\n    // show the sibling nodes of the specified node\n    showSiblings: function ($node, direction) {\n      var that = this;\n      // firstly, show the sibling nodes\n      var $siblings = $();\n      var $nodeContainer = $node.closest('.hierarchy');\n      if (direction) {\n        if (direction === 'left') {\n          $siblings = $nodeContainer.prevAll().removeClass('hidden');\n        } else {\n          $siblings = $nodeContainer.nextAll().removeClass('hidden');\n        }\n      } else {\n        $siblings = $node.closest('.hierarchy').siblings().removeClass('hidden');\n      }\n      // secondly, show the lines\n      var $upperLevel = $node.closest('.nodes').siblings('.node');\n      if (direction) {\n        $nodeContainer.removeClass(direction + '-sibs');\n        if (!$nodeContainer.is('[class*=-sibs]')) {\n          $nodeContainer.removeClass('isSiblingsCollapsed');\n        }\n        $siblings.removeClass('isCollapsedSibling ' + direction + '-sibs');\n      } else {\n        $node.closest('.hierarchy').removeClass('isSiblingsCollapsed');\n        $siblings.removeClass('isCollapsedSibling');\n      }\n      // thirdly, show parent node if it is collapsed\n      if (!this.getNodeState($node, 'parent').visible) {\n        $node.closest('.hierarchy').removeClass('isAncestorsCollapsed');\n        $upperLevel.removeClass('hidden');\n        this.repaint($upperLevel[0]);\n        $upperLevel.addClass('sliding').removeClass('slide-down').one('transitionend', this.showRelatedParentEnd);\n      }\n      // lastly, show the sibling nodes with animation\n      var $visibleNodes = $siblings.find('.node').filter(this.isVisibleNode.bind(this));\n      this.repaint($visibleNodes.get(0));\n      $visibleNodes.addClass('sliding').removeClass('slide-left slide-right');\n      $visibleNodes.eq(0).one('transitionend', { 'node': $node, 'visibleNodes': $visibleNodes }, this.showSiblingsEnd.bind(this));\n    },\n    // start up loading status for requesting new nodes\n    startLoading: function ($edge) {\n      var $chart = this.$chart;\n      if (typeof $chart.data('inAjax') !== 'undefined' && $chart.data('inAjax') === true) {\n        return false;\n      }\n\n      $edge.addClass('hidden');\n      $edge.parent().append(`<i class=\"${this.options.icons.theme} ${this.options.icons.spinner} spinner\"></i>`)\n        .children().not('.spinner').css('opacity', 0.2);\n      $chart.data('inAjax', true);\n      $('.oc-export-btn').prop('disabled', true);\n      return true;\n    },\n    // terminate loading status for requesting new nodes\n    endLoading: function ($edge) {\n      var $node = $edge.parent();\n      $edge.removeClass('hidden');\n      $node.find('.spinner').remove();\n      $node.children().removeAttr('style');\n      this.$chart.data('inAjax', false);\n      $('.oc-export-btn').prop('disabled', false);\n    },\n    // whether the cursor is hovering over the node\n    isInAction: function ($node) {\n      // TODO: 展开/折叠的按钮不止4个箭头，还有toggleBtn\n      return [\n        this.options.icons.expandToUp,\n        this.options.icons.collapseToDown,\n        this.options.icons.collapseToLeft,\n        this.options.icons.expandToRight\n      ].some((icon) => $node.children('.edge').attr('class').indexOf(icon) > -1);\n    },\n    //\n    switchVerticalArrow: function ($arrow) {\n      $arrow.toggleClass(`${this.options.icons.expandToUp} ${this.options.icons.collapseToDown}`);\n    },\n    //\n    switchHorizontalArrow: function ($node) {\n      var opts = this.options;\n      if (opts.toggleSiblingsResp && (typeof opts.ajaxURL === 'undefined' || $node.closest('.nodes').data('siblingsLoaded'))) {\n        var $prevSib = $node.parent().prev();\n        if ($prevSib.length) {\n          if ($prevSib.is('.hidden')) {\n            $node.children('.leftEdge').addClass(opts.icons.collapseToLeft).removeClass(opts.icons.expandToRight);\n          } else {\n            $node.children('.leftEdge').addClass(opts.icons.expandToRight).removeClass(opts.icons.collapseToLeft);\n          }\n        }\n        var $nextSib = $node.parent().next();\n        if ($nextSib.length) {\n          if ($nextSib.is('.hidden')) {\n            $node.children('.rightEdge').addClass(opts.icons.expandToRight).removeClass(opts.icons.collapseToLeft);\n          } else {\n            $node.children('.rightEdge').addClass(opts.icons.collapseToLeft).removeClass(opts.icons.expandToRight);\n          }\n        }\n      } else {\n        var $sibs = $node.parent().siblings();\n        var sibsVisible = $sibs.length ? !$sibs.is('.hidden') : false;\n        $node.children('.leftEdge').toggleClass(opts.icons.expandToRight, sibsVisible).toggleClass(opts.icons.collapseToLeft, !sibsVisible);\n        $node.children('.rightEdge').toggleClass(opts.icons.collapseToLeft, sibsVisible).toggleClass(opts.icons.expandToRight, !sibsVisible);\n      }\n    },\n    //\n    repaint: function (node) {\n      if (node) {\n        node.style.offsetWidth = node.offsetWidth;\n      }\n    },\n    // determines how to show arrow buttons \n    nodeEnterLeaveHandler: function (event) {\n      var $node = $(event.delegateTarget);\n      var flag = false;\n      if ($node.closest('.nodes.vertical').length) {\n        var $toggleBtn = $node.children('.toggleBtn');\n        if (event.type === 'mouseenter') {\n          if ($node.children('.toggleBtn').length) {\n            flag = this.getNodeState($node, 'children').visible;\n            $toggleBtn.toggleClass(this.options.icons.collapsed, !flag).toggleClass(this.options.icons.expanded, flag);\n          }\n        } else {\n          $toggleBtn.removeClass(`${this.options.icons.collapsed} ${this.options.icons.expanded}`);\n        }\n      } else {\n        var $topEdge = $node.children('.topEdge');\n        var $rightEdge = $node.children('.rightEdge');\n        var $bottomEdge = $node.children('.bottomEdge');\n        var $leftEdge = $node.children('.leftEdge');\n        if (event.type === 'mouseenter') {\n          if ($topEdge.length) {\n            flag = this.getNodeState($node, 'parent').visible;\n            $topEdge.toggleClass(this.options.icons.expandToUp, !flag).toggleClass(this.options.icons.collapseToDown, flag);\n          }\n          if ($bottomEdge.length) {\n            flag = this.getNodeState($node, 'children').visible;\n            $bottomEdge.toggleClass(this.options.icons.collapseToDown, !flag).toggleClass(this.options.icons.expandToUp, flag);\n          }\n          if ($leftEdge.length) {\n            this.switchHorizontalArrow($node);\n          }\n        } else {\n          $node.children('.edge').removeClass(`${this.options.icons.expandToUp} ${this.options.icons.collapseToDown} ${this.options.icons.collapseToLeft} ${this.options.icons.expandToRight}`);\n        }\n      }\n    },\n    //\n    nodeClickHandler: function (event) {\n      this.$chart.find('.focused').removeClass('focused');\n      $(event.delegateTarget).addClass('focused');\n    },\n    addAncestors: function (data, parentId) {\n      var $root = this.$chart.children('.nodes').children('.hierarchy');\n      this.buildHierarchy($root, data);\n      $root.children().slice(0, 2)\n        .wrapAll('<li class=\"hierarchy\"></li>').parent()\n        .appendTo($('#' + parentId).siblings('.nodes'));\n    },\n    addDescendants:function (data, $parent) {\n      var that = this;\n      var $descendants = $('<ul class=\"nodes\"></ul>');\n      $parent.after($descendants);\n      $.each(data, function (i) {\n        $descendants.append($('<li class=\"hierarchy\"></li>'));\n        that.buildHierarchy($descendants.children().eq(i), this);\n      });\n    },\n    //\n    HideFirstParentEnd: function (event) {\n      var $topEdge = event.data.topEdge;\n      var $node = $topEdge.parent();\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($topEdge);\n        this.switchHorizontalArrow($node);\n      }\n    },\n    // actions on clinking top edge of a node\n    topEdgeClickHandler: function (event) {\n      var that = this;\n      var $topEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var parentState = this.getNodeState($node, 'parent');\n      if (parentState.exist) {\n        var $parent = $node.closest('.nodes').siblings('.node');\n        if ($parent.is('.sliding')) { return; }\n        // hide the ancestor nodes and sibling nodes of the specified node\n        if (parentState.visible) {\n          this.hideParent($node);\n          $parent.one('transitionend', { 'topEdge': $topEdge }, this.HideFirstParentEnd.bind(this));\n          this.triggerHideEvent($node, 'parent');\n        } else { // show the ancestors and siblings\n          this.showParent($node);\n          this.triggerShowEvent($node, 'parent');\n        }\n      }\n    },\n    // actions on clinking bottom edge of a node\n    bottomEdgeClickHandler: function (event) {\n      var $bottomEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var childrenState = this.getNodeState($node, 'children');\n      if (childrenState.exist) {\n        var $children = $node.siblings('.nodes').children().children('.node');\n        if ($children.is('.sliding')) { return; }\n        // hide the descendant nodes of the specified node\n        if (childrenState.visible) {\n          this.hideChildren($node);\n          this.triggerHideEvent($node, 'children');\n        } else { // show the descendants\n          this.showChildren($node);\n          this.triggerShowEvent($node, 'children');\n        }\n      }\n    },\n    // actions on clicking horizontal edges\n    hEdgeClickHandler: function (event) {\n      var $hEdge = $(event.target);\n      var $node = $(event.delegateTarget);\n      var opts = this.options;\n      var siblingsState = this.getNodeState($node, 'siblings');\n      if (siblingsState.exist) {\n        var $siblings = $node.closest('.hierarchy').siblings();\n        if ($siblings.find('.sliding').length) { return; }\n        if (opts.toggleSiblingsResp) {\n          var $prevSib = $node.closest('.hierarchy').prev();\n          var $nextSib = $node.closest('.hierarchy').next();\n          if ($hEdge.is('.leftEdge')) {\n            if ($prevSib.is('.hidden')) {\n              this.showSiblings($node, 'left');\n              this.triggerShowEvent($node,'siblings');\n            } else {\n              this.hideSiblings($node, 'left');\n              this.triggerHideEvent($node, 'siblings');\n            }\n          } else {\n            if ($nextSib.is('.hidden')) {\n              this.showSiblings($node, 'right');\n              this.triggerShowEvent($node,'siblings');\n            } else {\n              this.hideSiblings($node, 'right');\n              this.triggerHideEvent($node, 'siblings');\n            }\n          }\n        } else {\n          if (siblingsState.visible) {\n            this.hideSiblings($node);\n            this.triggerHideEvent($node, 'siblings');\n          } else {\n            this.showSiblings($node);\n            this.triggerShowEvent($node, 'siblings');\n          }\n        }\n      }\n    },\n    // show the compact node's children in the compact mode\n    backToCompactHandler: function (event) {\n      $(event.delegateTarget).removeClass('looseMode')\n        .find('.looseMode').removeClass('looseMode')\n        .children('.backToCompactSymbol').addClass('hidden').end()\n        .children('.backToLooseSymbol').removeClass('hidden');\n      $(event.delegateTarget).children('.backToCompactSymbol').addClass('hidden').end()\n        .children('.backToLooseSymbol').removeClass('hidden');\n    },\n    // show the compact node's children in the loose mode \n    backToLooseHandler: function (event) {\n      $(event.delegateTarget)\n        .addClass('looseMode')\n        .children('.backToLooseSymbol').addClass('hidden').end()\n        .children('.backToCompactSymbol').removeClass('hidden');\n    },\n    //\n    expandVNodesEnd: function (event) {\n      event.data.vNodes.removeClass('sliding');\n    },\n    //\n    collapseVNodesEnd: function (event) {\n      event.data.vNodes.removeClass('sliding').closest('ul').addClass('hidden');\n    },\n    // event handler for toggle buttons in Hybrid(horizontal + vertical) OrgChart\n    toggleVNodes: function (event) {\n      var $toggleBtn = $(event.target);\n      var $descWrapper = $toggleBtn.parent().next();\n      var $descendants = $descWrapper.find('.node');\n      var $children = $descWrapper.children().children('.node');\n      if ($children.is('.sliding')) { return; }\n      $toggleBtn.toggleClass(`${this.options.icons.collapsed} ${this.options.icons.expanded}`);\n      if ($descendants.eq(0).is('.slide-up')) {\n        $descWrapper.removeClass('hidden');\n        this.repaint($children.get(0));\n        $children.addClass('sliding').removeClass('slide-up').eq(0).one('transitionend', { 'vNodes': $children }, this.expandVNodesEnd);\n      } else {\n        $descendants.addClass('sliding slide-up').eq(0).one('transitionend', { 'vNodes': $descendants }, this.collapseVNodesEnd);\n        $descendants.find('.toggleBtn').removeClass(`${this.options.icons.collapsed} ${this.options.icons.expanded}`);\n      }\n    },\n    //\n    createGhostNode: function (event) {\n      var $nodeDiv = $(event.target);\n      var opts = this.options;\n      var origEvent = event.originalEvent;\n      var isFirefox = /firefox/.test(window.navigator.userAgent.toLowerCase());\n      var ghostNode, nodeCover;\n      if (!document.querySelector('.ghost-node')) {\n        ghostNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n        if (!ghostNode.classList) return;\n        ghostNode.classList.add('ghost-node');\n        nodeCover = document.createElementNS('http://www.w3.org/2000/svg','rect');\n        ghostNode.appendChild(nodeCover);\n        $nodeDiv.closest('.orgchart').append(ghostNode);\n      } else {\n        ghostNode = $nodeDiv.closest('.orgchart').children('.ghost-node').get(0);\n        nodeCover = $(ghostNode).children().get(0);\n      }\n      var transValues = $nodeDiv.closest('.orgchart').css('transform').split(',');\n      var isHorizontal = opts.direction === 't2b' || opts.direction === 'b2t';\n      var scale = Math.abs(window.parseFloat(isHorizontal ? transValues[0].slice(transValues[0].indexOf('(') + 1) : transValues[1]));\n      ghostNode.setAttribute('width', isHorizontal ? $nodeDiv.outerWidth(false) : $nodeDiv.outerHeight(false));\n      ghostNode.setAttribute('height', isHorizontal ? $nodeDiv.outerHeight(false) : $nodeDiv.outerWidth(false));\n      nodeCover.setAttribute('x',5 * scale);\n      nodeCover.setAttribute('y',5 * scale);\n      nodeCover.setAttribute('width', 120 * scale);\n      nodeCover.setAttribute('height', 40 * scale);\n      nodeCover.setAttribute('rx', 4 * scale);\n      nodeCover.setAttribute('ry', 4 * scale);\n      nodeCover.setAttribute('stroke-width', 1 * scale);\n      var xOffset = origEvent.offsetX * scale;\n      var yOffset = origEvent.offsetY * scale;\n      if (opts.direction === 'l2r') {\n        xOffset = origEvent.offsetY * scale;\n        yOffset = origEvent.offsetX * scale;\n      } else if (opts.direction === 'r2l') {\n        xOffset = $nodeDiv.outerWidth(false) - origEvent.offsetY * scale;\n        yOffset = origEvent.offsetX * scale;\n      } else if (opts.direction === 'b2t') {\n        xOffset = $nodeDiv.outerWidth(false) - origEvent.offsetX * scale;\n        yOffset = $nodeDiv.outerHeight(false) - origEvent.offsetY * scale;\n      }\n      if (isFirefox) { // hack for old version of Firefox(< 48.0)\n        nodeCover.setAttribute('fill', 'rgb(255, 255, 255)');\n        nodeCover.setAttribute('stroke', 'rgb(191, 0, 0)');\n        var ghostNodeWrapper = document.createElement('img');\n        ghostNodeWrapper.src = 'data:image/svg+xml;utf8,' + (new XMLSerializer()).serializeToString(ghostNode);\n        origEvent.dataTransfer.setDragImage(ghostNodeWrapper, xOffset, yOffset);\n      } else {\n        // IE/Edge do not support this, so only use it if we can\n        if (origEvent.dataTransfer.setDragImage)\n          origEvent.dataTransfer.setDragImage(ghostNode, xOffset, yOffset);\n      }\n    },\n    // get the level amount of a hierachy\n    getUpperLevel: function ($node) {\n      if (!$node.is('.node')) {\n        return 0;\n      }\n      return $node.parents('.hierarchy').length;\n    },\n    // get the level amount of a hierachy\n    getLowerLevel: function ($node) {\n      if (!$node.is('.node')) {\n        return 0;\n      }\n      return $node.closest('.hierarchy').find('.nodes').length + 1;\n    },\n    // get nodes in level order traversal\n    getLevelOrderNodes: function ($root) {\n      if(!$root) return [];\n      var queue = [];\n      var output = [];\n      queue.push($root);\n      while(queue.length) {\n        var row = [];\n        for(var i = 0; i < queue.length; i++) {\n            var cur = queue.shift();\n            var children = this.getChildren(cur);\n            if(children.length) {\n              queue.push(children.toArray().flat());\n            }\n            row.push($(cur));\n        }\n        output.push(row);\n      }\n      return output;\n    },\n    //\n    filterAllowedDropNodes: function ($dragged) {\n      var opts = this.options;\n      // what is being dragged?  a node, or something within a node?\n      var draggingNode = $dragged.closest('[draggable]').hasClass('node');\n      var $dragZone = $dragged.closest('.nodes').siblings('.node'); // parent node\n      var $dragHier = $dragged.closest('.hierarchy').find('.node'); // this node, and its children\n      this.$chart.data('dragged', $dragged)\n        .find('.node').each(function (index, node) {\n          if (!draggingNode || $dragHier.index(node) === -1) {\n            if (opts.dropCriteria) {\n              if (opts.dropCriteria($dragged, $dragZone, $(node))) {\n                $(node).addClass('allowedDrop');\n              }\n            } else {\n              $(node).addClass('allowedDrop');\n            }\n          }\n        });\n    },\n    //\n    dragstartHandler: function (event) {\n      event.originalEvent.dataTransfer.setData('text/html', 'hack for firefox');\n      // if users enable zoom or direction options\n      if (this.$chart.css('transform') !== 'none') {\n        this.createGhostNode(event);\n      }\n      this.filterAllowedDropNodes($(event.target));\n    },\n    //\n    dragoverHandler: function (event) {\n      if (!$(event.delegateTarget).is('.allowedDrop')) {\n        event.originalEvent.dataTransfer.dropEffect = 'none';\n      } else {\n        // default action for drag-and-drop of div is not to drop, so preventing default action for nodes which have allowedDrop class\n        //to fix drag and drop on IE and Edge\n        event.preventDefault();\n      }\n    },\n    //\n    dragendHandler: function (event) {\n      this.$chart.find('.allowedDrop').removeClass('allowedDrop');\n    },\n    // when user drops the node, it will be removed from original parent node and be added to new parent node\n    dropHandler: async function (event) {\n      var that = this;\n      var $dropZone = $(event.delegateTarget);\n      var $dragged = this.$chart.data('dragged');\n\n      // Pass on drops which are not nodes (since they are not our doing)\n      if (!$dragged.hasClass('node')) {\n        this.$chart.triggerHandler({ 'type': 'otherdropped.orgchart', 'draggedItem': $dragged, 'dropZone': $dropZone });\n        return;\n      }\n\n      if (!$dropZone.hasClass('allowedDrop')) {\n          // We are trying to drop a node into a node which isn't allowed\n          // IE/Edge have a habit of allowing this, so we need our own double-check\n          return;\n      }\n\n      var $dragZone = $dragged.closest('.nodes').siblings('.node');\n      var dropEvent = $.Event('nodedrop.orgchart');\n      this.$chart.trigger(dropEvent, { 'draggedNode': $dragged, 'dragZone': $dragZone, 'dropZone': $dropZone });\n      if (dropEvent.isDefaultPrevented()) {\n        return;\n      }\n      // special process for hybrid chart\n      var datasource = this.$chart.data('options').data;\n      var digger = new JSONDigger(datasource, this.$chart.data('options').nodeId, 'children');\n      const hybridNode = digger.findOneNode({ 'hybrid': true });\n      if (this.$chart.data('options').verticalLevel > 1 || hybridNode) {\n        var draggedNode = digger.findNodeById($dragged.data('nodeData').id);\n        var copy = Object.assign({}, draggedNode);\n        digger.removeNode(draggedNode.id);\n        var dropNode = digger.findNodeById($dropZone.data('nodeData').id);\n        if (dropNode.children) {\n          dropNode.children.push(copy);\n        } else {\n          dropNode.children = [copy];\n        }\n        that.init({ 'data': datasource });\n      } else {\n        // The folowing code snippets are used to process horizontal chart\n        // firstly, deal with the hierarchy of drop zone\n        if (!$dropZone.siblings('.nodes').length) { // if the drop zone is a leaf node\n          $dropZone.append(`<i class=\"edge verticalEdge bottomEdge ${this.options.icons.theme}\"></i>`)\n            .after('<ul class=\"nodes\"></ul>')\n            .siblings('.nodes').append($dragged.find('.horizontalEdge').remove().end().closest('.hierarchy'));\n          if ($dropZone.children('.title').length) {\n            $dropZone.children('.title').prepend(`<i class=\"${this.options.icons.theme} ${this.$chart.data('options').icons.parentNode} parentNodeSymbol\"></i>`);\n          }\n        } else {\n          var horizontalEdges = `<i class=\"edge horizontalEdge rightEdge ${this.options.icons.theme}\"></i><i class=\"edge horizontalEdge leftEdge ${this.options.icons.theme}\"></i>`;\n          if (!$dragged.find('.horizontalEdge').length) {\n            $dragged.append(horizontalEdges);\n          }\n          $dropZone.siblings('.nodes').append($dragged.closest('.hierarchy'));\n          var $dropSibs = $dragged.closest('.hierarchy').siblings().find('.node:first');\n          if ($dropSibs.length === 1) {\n            $dropSibs.append(horizontalEdges);\n          }\n        }\n        // secondly, deal with the hierarchy of dragged node\n        if ($dragZone.siblings('.nodes').children('.hierarchy').length === 1) { // if there is only one sibling node left\n          $dragZone.siblings('.nodes').children('.hierarchy').find('.node:first')\n            .find('.horizontalEdge').remove();\n        } else if ($dragZone.siblings('.nodes').children('.hierarchy').length === 0) {\n          $dragZone.find('.bottomEdge, .parentNodeSymbol').remove()\n            .end().siblings('.nodes').remove();\n        }\n      }\n    },\n    //\n    touchstartHandler: function (event) {\n      if (this.touchHandled)\n        return;\n\n      if (event.touches && event.touches.length > 1)\n        return;\n\n      this.touchHandled = true;\n      this.touchMoved = false; // this is so we can work out later if this was a 'press' or a 'drag' touch\n      event.preventDefault();\n    },\n    //\n    touchmoveHandler: function (event) {\n      if (!this.touchHandled)\n        return;\n\n      if (event.touches && event.touches.length > 1)\n        return;\n\n      event.preventDefault();\n\n      if (!this.touchMoved) {\n        // we do not bother with createGhostNode (dragstart does) since the touch event does not have a dataTransfer property\n        this.filterAllowedDropNodes($(event.currentTarget));  // will also set 'this.$chart.data('dragged')' for us\n        // create an image which can be used to illustrate the drag (our own createGhostNode)\n        this.touchDragImage = this.createDragImage(event, this.$chart.data('dragged')[0]);\n      }\n      this.touchMoved = true;\n\n      // move our dragimage so it follows our finger\n      this.moveDragImage(event, this.touchDragImage);\n\n      var $touching = $(document.elementFromPoint(event.touches[0].clientX, event.touches[0].clientY));\n      var $touchingNodes = $touching.closest('div.node');\n      if ($touchingNodes.length > 0) {\n        var touchingNodeElement = $touchingNodes[0];\n        if ($touchingNodes.is('.allowedDrop')) {\n          this.touchTargetNode = touchingNodeElement;\n        }\n        else {\n          this.touchTargetNode = null;\n        }\n      }\n      else {\n        this.touchTargetNode = null;\n      }\n    },\n    //\n    touchendHandler: function (event) {\n      if (!this.touchHandled) {\n          return;\n      }\n      this.destroyDragImage();\n      if (this.touchMoved) {\n          // we've had movement, so this was a 'drag' touch\n          if (this.touchTargetNode) {\n              var fakeEventForDropHandler = { delegateTarget: this.touchTargetNode };\n              this.dropHandler(fakeEventForDropHandler);\n              this.touchTargetNode = null;\n          }\n          this.dragendHandler(event);\n      }\n      else {\n          // we did not move, so this was a 'press' touch (fake a click)\n          var firstTouch = event.changedTouches[0];\n          var fakeMouseClickEvent = document.createEvent('MouseEvents');\n          fakeMouseClickEvent.initMouseEvent('click', true, true, window, 1, firstTouch.screenX, firstTouch.screenY, firstTouch.clientX, firstTouch.clientY, event.ctrlKey, event.altKey, event.shiftKey, event.metaKey, 0, null);\n          event.target.dispatchEvent(fakeMouseClickEvent);\n      }\n      this.touchHandled = false;\n    },\n    //\n    createDragImage: function (event, source) {\n      var dragImage = source.cloneNode(true);\n      this.copyStyle(source, dragImage);\n      dragImage.style.top = dragImage.style.left = '-9999px';\n      var sourceRectangle = source.getBoundingClientRect();\n      var sourcePoint = this.getTouchPoint(event);\n      this.touchDragImageOffset = { x: sourcePoint.x - sourceRectangle.left, y: sourcePoint.y - sourceRectangle.top };\n      dragImage.style.opacity = '0.5';\n      document.body.appendChild(dragImage);\n      return dragImage;\n    },\n    //\n    destroyDragImage: function () {\n      if (this.touchDragImage && this.touchDragImage.parentElement)\n        this.touchDragImage.parentElement.removeChild(this.touchDragImage);\n      this.touchDragImageOffset = null;\n      this.touchDragImage = null;\n    },\n    //\n    copyStyle: function (src, dst) {\n      // remove potentially troublesome attributes\n      var badAttributes = ['id', 'class', 'style', 'draggable'];\n      badAttributes.forEach(function (att) {\n          dst.removeAttribute(att);\n      });\n      // copy canvas content\n      if (src instanceof HTMLCanvasElement) {\n        var cSrc = src, cDst = dst;\n        cDst.width = cSrc.width;\n        cDst.height = cSrc.height;\n        cDst.getContext('2d').drawImage(cSrc, 0, 0);\n      }\n      // copy style (without transitions)\n      var cs = getComputedStyle(src);\n      for (var i = 0; i < cs.length; i++) {\n        var key = cs[i];\n        if (key.indexOf('transition') < 0) {\n          dst.style[key] = cs[key];\n        }\n      }\n      dst.style.pointerEvents = 'none';\n      // and repeat for all children\n      for (var i = 0; i < src.children.length; i++) {\n        this.copyStyle(src.children[i], dst.children[i]);\n      }\n    },\n    //\n    getTouchPoint: function (event) {\n      if (event && event.touches) {\n        event = event.touches[0];\n      }\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    },\n    //\n    moveDragImage: function (event, image) {\n      if (!event || !image)\n        return;\n      var orgChartMaster = this;\n      requestAnimationFrame(function () {\n        var pt = orgChartMaster.getTouchPoint(event);\n        var s = image.style;\n        s.position = 'absolute';\n        s.pointerEvents = 'none';\n        s.zIndex = '999999';\n        if (orgChartMaster.touchDragImageOffset) {\n            s.left = Math.round(pt.x - orgChartMaster.touchDragImageOffset.x) + 'px';\n            s.top = Math.round(pt.y - orgChartMaster.touchDragImageOffset.y) + 'px';\n        }\n      });\n    },\n    //\n    bindDragDrop: function ($node) {\n      $node.on('dragstart', this.dragstartHandler.bind(this))\n        .on('dragover', this.dragoverHandler.bind(this))\n        .on('dragend', this.dragendHandler.bind(this))\n        .on('drop', this.dropHandler.bind(this))\n        .on('touchstart', this.touchstartHandler.bind(this))\n        .on('touchmove', this.touchmoveHandler.bind(this))\n        .on('touchend', this.touchendHandler.bind(this));\n    },\n    // create node\n    createNode: function (data) {\n      var that = this;\n      var opts = this.options;\n      var level = data.level;\n      if (data.children && data[opts.nodeId]) {\n        $.each(data.children, function (index, child) {\n          child.parentId = data[opts.nodeId]\n        });\n      }\n      // construct the content of node\n      var $nodeDiv = $('<div' + (opts.draggable ? ' draggable=\"true\"' : '') + (data[opts.nodeId] ? ' id=\"' + data[opts.nodeId] + '\"' : '') + (data.parentId ? ' data-parent=\"' + data.parentId + '\"' : '') + '>')\n        .addClass('node ' \n        + (data.className || '')\n        + (data?.outsider ? 'outsider' : '')\n        +  (level > opts.visibleLevel ? ' slide-up' : ''));\n      if (opts.nodeTemplate) {\n        $nodeDiv.append(opts.nodeTemplate(data));\n      } else {\n        $nodeDiv.append('<div class=\"title\">' + data[opts.nodeTitle] + '</div>')\n          .append(typeof opts.nodeContent !== 'undefined' ? '<div class=\"content\">' + (data[opts.nodeContent] || '') + '</div>' : '');\n      }\n      //\n      var nodeData = $.extend({}, data);\n      delete nodeData.children;\n      $nodeDiv.data('nodeData', nodeData);\n      // append 4 direction arrows or expand/collapse buttons or reset buttons\n      var flags = data.relationship || '';\n      if ((opts.verticalLevel && level >= opts.verticalLevel) || data.vertical) {\n        if (Number(flags.substr(2,1))) {\n          $nodeDiv.append(`<i class=\"toggleBtn ${opts.icons.theme}\"></i>`)\n            .children('.title').prepend(`<i class=\"${opts.icons.theme} ${opts.icons.parentNode} parentNodeSymbol\"></i>`);\n        }\n      } else if (data.hybrid) {\n        if (Number(flags.substr(2,1))) {\n          $nodeDiv.append(`<i class=\"edge verticalEdge bottomEdge ${opts.icons.theme}\"></i>`)\n            .children('.title').prepend(`<i class=\"${opts.icons.theme} ${opts.icons.parentNode} parentNodeSymbol\"></i>`);\n        }\n      } else if (data.compact) {\n        $nodeDiv.css('grid-template-columns', `repeat(${Math.floor(Math.sqrt(data.children.length + 1))}, auto)`);\n        if (Number(flags.substr(2,1))) {\n          $nodeDiv.append(`\n            <i class=\"${opts.icons.theme} ${opts.icons.backToCompact} backToCompactSymbol hidden\"></i>\n            <i class=\"${opts.icons.theme} ${opts.icons.backToLoose} backToLooseSymbol\"></i>\n            `)\n            .children('.title').prepend(`<i class=\"${opts.icons.theme} ${opts.icons.parentNode} parentNodeSymbol\"></i>`);\n        }\n      } else if (data.associatedCompact) {\n   \n      } else {\n        if (Number(flags.substr(0,1))) {\n          $nodeDiv.append(`<i class=\"edge verticalEdge topEdge ${opts.icons.theme}\"></i>`);\n        }\n        if(Number(flags.substr(1,1))) {\n          $nodeDiv.append(`<i class=\"edge horizontalEdge rightEdge ${opts.icons.theme}\"></i><i class=\"edge horizontalEdge leftEdge ${opts.icons.theme}\"></i>`);\n        }\n        if(Number(flags.substr(2,1))) {\n          $nodeDiv.append(`<i class=\"edge verticalEdge bottomEdge ${opts.icons.theme}\"></i>`)\n            .children('.title').prepend(`<i class=\"${opts.icons.theme} ${opts.icons.parentNode} parentNodeSymbol\"></i>`);\n        }\n      }\n\n      $nodeDiv.on('mouseenter mouseleave', this.nodeEnterLeaveHandler.bind(this));\n      $nodeDiv.on('click', this.nodeClickHandler.bind(this));\n      $nodeDiv.on('click', '.topEdge', this.topEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.bottomEdge', this.bottomEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.leftEdge, .rightEdge', this.hEdgeClickHandler.bind(this));\n      $nodeDiv.on('click', '.toggleBtn', this.toggleVNodes.bind(this));\n      $nodeDiv.on('click', '> .backToCompactSymbol',this.backToCompactHandler.bind(this));\n      $nodeDiv.on('click', '> .backToLooseSymbol',this.backToLooseHandler.bind(this));\n\n      if (opts.draggable) {\n        this.bindDragDrop($nodeDiv);\n        this.touchHandled = false;\n        this.touchMoved = false;\n        this.touchTargetNode = null;\n      }\n      // allow user to append dom modification after finishing node create of orgchart\n      if (opts.createNode) {\n        opts.createNode($nodeDiv, data);\n      }\n\n      return $nodeDiv;\n    },\n    // Construct the inferior nodes within a hierarchy\n    buildInferiorNodes: function ($hierarchy, $nodeDiv, data, level) {\n      var that = this;\n      var opts = this.options;\n      var isHidden = level + 1 > opts.visibleLevel || (data.collapsed !== undefined && data.collapsed);\n      var $nodesLayer;\n      if ((opts.verticalLevel && (level + 1) >= opts.verticalLevel) || data.hybrid) {\n        $nodesLayer = $('<ul class=\"nodes\">');\n        if (isHidden && (opts.verticalLevel && (level + 1) >= opts.verticalLevel)) {\n          $nodesLayer.addClass('hidden');\n        }\n        if (((opts.verticalLevel && level + 1 === opts.verticalLevel) || data.hybrid)\n          && !$hierarchy.closest('.vertical').length) {\n            $nodesLayer.addClass('vertical');\n        }\n        $hierarchy.append($nodesLayer);\n      } else if (data.compact) {\n        $nodeDiv.addClass('compact');\n      } else {\n        $nodesLayer = $('<ul class=\"nodes' + (isHidden ? ' hidden' : '') + '\">');\n        if (isHidden) {\n          $hierarchy.addClass('isChildrenCollapsed');\n        }\n        $hierarchy.append($nodesLayer);\n      }\n      // recurse through children nodes\n      if (Array.isArray(data.children[0])) {\n        $.each(data.children, function() {\n          this.level = level + 1;\n        });\n        this.buildHierarchy($nodesLayer, data.children); // 构造子一层的夫妻组合（每个组合可能有多妻多夫情况）\n      } else {\n        $.each(data.children, function () {\n          this.level = level + 1;\n          if (data.compact) {\n            that.buildHierarchy($nodeDiv, this);\n          } else {\n            var $nodeCell = $('<li class=\"hierarchy\">');\n            $nodesLayer.append($nodeCell);\n            that.buildHierarchy($nodeCell, this);\n          }\n        });\n      }\n    },\n    // recursively build the tree\n    buildHierarchy: function ($hierarchy, data) {\n      var that = this;\n      var opts = this.options;\n      var level = 0;\n      var $nodeDiv;\n      if (data.level || data[0]?.level) {\n        level = data.level;\n      } else {\n        level = $hierarchy.parentsUntil('.orgchart', '.nodes').length;\n        if (Array.isArray(data) && Array.isArray(data[0])) {\n          $.each(data, function () {\n            $.each(this, function () {\n              this.level = level;\n            });\n          });\n        } else {\n          data.level = level;\n        }\n      }\n      // Construct the single node in OrgChart or the multiple nodes in family tree\n      if (Array.isArray(data) && Array.isArray(data[0])) { // 处理family tree的情况\n        $.each(data, function () { // 构造一个家庭的hierarchy\n          var _this = this;\n          $.each(this, function (i) { // 构造一个夫/妻节点\n            $nodeDiv = that.createNode(this);\n            // if there are only two persons in a marriage, two single nodes will appear in a hierarchy\n            if (_this.length === 2 && i === 1) {\n              $hierarchy.find(`#${_this[0].id}`).after($nodeDiv);\n              if (this.children && this.children.length && this.children[0].length) {\n                that.buildInferiorNodes($hierarchy.find(`#${_this[0].id}`).parent(), $nodeDiv, this, level);\n              }\n            } else {\n              // if there are more than two persons in a marriage, every node will be included in a single hierarchy\n              var $wrapper = $(`<li class=\"hierarchy${_this.length > 1 ? ' spouse' : ''}${_this.length === 2 ? ' couple' : ''}${!!this.outsider === false && _this.length > 2  ? ' insider' : ''}\"></li>`);\n\n              //在family tree中，一个多妻/多夫组合里，本姓人只有一个，外姓人可能有多个，我们通过水平的连线来表示他们是一家子\n              if (i === 0) {\n                $wrapper.css({'--ft-width': '50%', '--ft-left-offset': '50%'});\n              } else if (i > 0 && i < _this.length - 1) {\n                $wrapper.css({'--ft-width': '100%', '--ft-left-offset': '0px'});\n              } else {\n                $wrapper.css({'--ft-width': '50%', '--ft-left-offset': '0px'});\n              }\n\n              $wrapper.append($nodeDiv);\n              $hierarchy.append($wrapper);\n              if (this.children && this.children.length && this.children[0].length) {\n                that.buildInferiorNodes($wrapper, $nodeDiv, this, level);\n              }\n            }\n          });\n        });\n      } else {\n        if (Object.keys(data).length > 2) { // TODO: 应该用更好的方式来判断是否是供父一级节点创建的信息\n          $nodeDiv = this.createNode(data);\n          $hierarchy.append($nodeDiv);\n        }\n        if (data.children && data.children.length) {\n          this.buildInferiorNodes($hierarchy, $nodeDiv, data, level);\n        }\n      }\n    },\n    // build the child nodes of specific node\n    buildChildNode: function ($appendTo, data) {\n      this.buildHierarchy($appendTo, { 'children': data });\n    },\n    // exposed method\n    addChildren: function ($node, data) {\n      this.buildChildNode($node.closest('.hierarchy'), data);\n      if (!$node.find('.parentNodeSymbol').length) {\n        $node.children('.title').prepend(`<i class=\"${this.options.icons.theme} ${this.options.icons.parentNode} parentNodeSymbol\"></i>`);\n      }\n      if ($node.closest('.nodes.vertical').length) {\n        if (!$node.children('.toggleBtn').length) {\n          $node.append(`<i class=\"toggleBtn ${this.options.icons.theme}\"></i>`);\n        }\n      } else {\n        if (!$node.children('.bottomEdge').length) {\n          $node.append(`<i class=\"edge verticalEdge bottomEdge ${this.options.icons.theme}\"></i>`);\n        }\n      }\n      if (this.isInAction($node)) {\n        this.switchVerticalArrow($node.children('.bottomEdge'));\n      }\n    },\n    // build the parent node of specific node\n    buildParentNode: function ($currentRoot, data) {\n      data.relationship = data.relationship || '001';\n      var $newRootWrapper = $('<ul class=\"nodes\"><li class=\"hierarchy\"></li></ul>')\n        .find('.hierarchy').append(this.createNode(data)).end();\n      this.$chart.prepend($newRootWrapper)\n        .find('.hierarchy:first').append($currentRoot.closest('ul').addClass('nodes'));\n    },\n    // exposed method\n    addParent: function ($currentRoot, data) {\n      this.buildParentNode($currentRoot, data);\n      if (!$currentRoot.children('.topEdge').length) {\n        $currentRoot.children('.title').after(`<i class=\"edge verticalEdge topEdge ${this.options.icons.theme}\"></i>`);\n      }\n      if (this.isInAction($currentRoot)) {\n        this.switchVerticalArrow($currentRoot.children('.topEdge'));\n      }\n    },\n    // build the sibling nodes of specific node\n    buildSiblingNode: function ($nodeChart, data) {\n      var newSiblingCount = $.isArray(data) ? data.length : data.children.length;\n      var existingSibligCount = $nodeChart.parent().is('.nodes') ? $nodeChart.siblings().length + 1 : 1;\n      var siblingCount = existingSibligCount + newSiblingCount;\n      var insertPostion = (siblingCount > 1) ? Math.floor(siblingCount/2 - 1) : 0;\n      // just build the sibling nodes for the specific node\n      if ($nodeChart.closest('.nodes').parent().is('.hierarchy')) {\n        this.buildChildNode($nodeChart.parent().closest('.hierarchy'), data);\n        var $siblings = $nodeChart.parent().closest('.hierarchy').children('.nodes:last').children('.hierarchy');\n        if (existingSibligCount > 1) {\n          $siblings.eq(0).before($nodeChart.siblings().addBack().unwrap());\n        } else {\n          $siblings.eq(insertPostion).after($nodeChart.unwrap());\n        }\n      } else { // build the sibling nodes and parent node for the specific ndoe\n        this.buildHierarchy($nodeChart.parent().prepend($('<li class=\"hierarchy\">')).children('.hierarchy:first'), data);\n        $nodeChart.prevAll('.hierarchy').children('.nodes').children().eq(insertPostion).after($nodeChart);\n      }\n    },\n    //\n    addSiblings: function ($node, data) {\n      this.buildSiblingNode($node.closest('.hierarchy'), data);\n      $node.closest('.nodes').data('siblingsLoaded', true);\n      if (!$node.children('.leftEdge').length) {\n        $node.children('.topEdge').after(`<i class=\"edge horizontalEdge rightEdge ${this.options.icons.theme}\"></i><i class=\"edge horizontalEdge leftEdge ${this.options.icons.theme}\"></i>`);\n      }\n      if (this.isInAction($node)) {\n        this.switchHorizontalArrow($node);\n        $node.children('.topEdge').removeClass(this.options.icons.expandToUp).addClass(this.options.icons.collapseToDown);\n      }\n    },\n    // remove node and its descendent nodes\n    removeNodes: function ($node) {\n      var $wrapper = $node.closest('.hierarchy').parent();\n      if ($wrapper.parent().is('.hierarchy')) {\n        if (this.getNodeState($node, 'siblings').exist) {\n          $node.closest('.hierarchy').remove();\n          if ($wrapper.children().length === 1) {\n            $wrapper.find('.node:first .horizontalEdge').remove();\n          }\n        } else {\n          $wrapper.siblings('.node').find('.bottomEdge').remove()\n            .end().end().remove();\n        }\n      } else { // if $node is root node\n        $wrapper.closest('.orgchart').remove();\n      }\n    },\n    //\n    hideDropZones: function () {\n      // Remove all the 'this is a drop zone' indicators\n      var orgChartObj = this;\n      orgChartObj.$chart.find('.allowedDrop')\n        .removeClass('allowedDrop');\n    },\n    //\n    showDropZones: function (dragged) {\n      // Highlight all the 'drop zones', and set dragged, so that the drop/enter can work out what happens later\n      // TODO: This assumes all nodes are droppable: it doesn't run the custom isDroppable function - it should!\n      var orgChartObj = this;\n      orgChartObj.$chart.find('.node')\n        .each(function (index, node) {\n          $(node).addClass('allowedDrop');\n        });\n      orgChartObj.$chart.data('dragged', $(dragged));\n    },\n    //\n    processExternalDrop: function (dropZone, dragged) {\n      // Allow an external drop event to be handled by one of our nodes\n      if (dragged) {\n        this.$chart.data('dragged', $(dragged));\n      }\n      var droppedOnNode = dropZone.closest('.node');\n      // would like to just call 'dropZoneHandler', but I can't reach it from here\n      // instead raise a drop event on the node element\n      droppedOnNode.triggerHandler({ 'type': 'drop' });\n    },\n    //\n    exportPDF: function(canvas, exportFilename){\n      var doc = {};\n      var docWidth = Math.floor(canvas.width);\n      var docHeight = Math.floor(canvas.height);\n      if (!window.jsPDF) {\n        window.jsPDF = window.jspdf.jsPDF;\n      }\n\n      if (docWidth > docHeight) {\n        doc = new jsPDF({\n          orientation: 'landscape',\n          unit: 'px',\n          format: [docWidth, docHeight]\n        });\n      } else {\n        doc = new jsPDF({\n          orientation: 'portrait',\n          unit: 'px',\n          format: [docHeight, docWidth]\n        });\n      }\n      doc.addImage(canvas.toDataURL(), 'png', 0, 0);\n      doc.save(exportFilename + '.pdf');\n    },\n    //\n    exportPNG: function(canvas, exportFilename){\n      var that = this;\n      var isWebkit = 'WebkitAppearance' in document.documentElement.style;\n      var isFf = !!window.sidebar;\n      var isEdge = navigator.appName === 'Microsoft Internet Explorer' || (navigator.appName === \"Netscape\" && navigator.appVersion.indexOf('Edge') > -1);\n      var $chartContainer = this.$chartContainer;\n\n      if ((!isWebkit && !isFf) || isEdge) {\n        window.navigator.msSaveBlob(canvas.msToBlob(), exportFilename + '.png');\n      } else {\n        var selector = '.download-btn' + (that.options.chartClass !== '' ? '.' + that.options.chartClass : '');\n\n        if (!$chartContainer.find(selector).length) {\n          $chartContainer.append('<a class=\"download-btn' + (that.options.chartClass !== '' ? ' ' + that.options.chartClass : '') + '\"'\n                                 + ' download=\"' + exportFilename + '.png\"></a>');\n        }\n\n        $chartContainer.find(selector).attr('href', canvas.toDataURL())[0].click();\n      }\n    },\n    //\n    export: function (exportFilename, exportFileextension) {\n      var that = this;\n      exportFilename = (typeof exportFilename !== 'undefined') ?  exportFilename : this.options.exportFilename;\n      exportFileextension = (typeof exportFileextension !== 'undefined') ?  exportFileextension : this.options.exportFileextension;\n      if ($(this).children('.spinner').length) {\n        return false;\n      }\n      var $chartContainer = this.$chartContainer;\n      var $mask = $chartContainer.find('.mask');\n      if (!$mask.length) {\n        $chartContainer.append(`<div class=\"mask\"><i class=\"${this.options.icons.theme} ${this.options.icons.spinner} spinner\"></i></div>`);\n      } else {\n        $mask.removeClass('hidden');\n      }\n      var sourceChart = $chartContainer.addClass('canvasContainer').find('.orgchart:not(\".hidden\")').get(0);\n      var flag = that.options.direction === 'l2r' || that.options.direction === 'r2l';\n      html2canvas(sourceChart, {\n        'width': flag ? sourceChart.clientHeight : sourceChart.clientWidth,\n        'height': flag ? sourceChart.clientWidth : sourceChart.clientHeight,\n        'onclone': function (cloneDoc) {\n          $(cloneDoc).find('.canvasContainer').css('overflow', 'visible')\n            .find('.orgchart:not(\".hidden\"):first').css('transform', '');\n        }\n      })\n      .then(function (canvas) {\n        $chartContainer.find('.mask').addClass('hidden');\n\n        if (exportFileextension.toLowerCase() === 'pdf') {\n          that.exportPDF(canvas, exportFilename);\n        } else {\n          that.exportPNG(canvas, exportFilename);\n        }\n\n        $chartContainer.removeClass('canvasContainer');\n      }, function () {\n        $chartContainer.removeClass('canvasContainer');\n      });\n    }\n  };\n\n  $.fn.orgchart = function (opts) {\n    return new OrgChart(this, opts).init();\n  };\n\n}));\n"], "names": ["factory", "module", "exports", "require", "window", "document", "j<PERSON><PERSON><PERSON>", "$", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "elem", "opts", "this", "$chartContainer", "defaultOptions", "icons", "theme", "parentNode", "expandToUp", "collapseToDown", "collapseToLeft", "expandToRight", "backToCompact", "backToLoose", "collapsed", "expanded", "spinner", "nodeTitle", "nodeId", "toggleSiblingsResp", "visibleLevel", "chartClass", "exportButton", "exportButtonName", "exportFilename", "exportFileextension", "draggable", "direction", "pan", "zoom", "zoominLimit", "zoomoutLimit", "prototype", "init", "options", "extend", "data", "$chart", "remove", "class", "click", "event", "target", "closest", "length", "find", "removeClass", "$root", "MutationObserver", "triggerInitEvent", "Array", "isArray", "append", "buildHierarchy", "buildJsonDS", "children", "relationship", "attachRel", "attachExportButton", "bind<PERSON>an", "bindZoom", "handleCompactNodes", "each", "index", "node", "addClass", "parents", "that", "mo", "mutations", "disconnect", "initTime", "i", "j", "addedNodes", "classList", "contains", "initCompleted", "initEvent", "Event", "trigger", "observe", "childList", "triggerShowEvent", "$target", "rel", "triggerHideEvent", "$exportBtn", "text", "e", "preventDefault", "export", "after", "setOptions", "val", "unbind<PERSON>an", "unbind<PERSON>oom", "panStartHandler", "<PERSON><PERSON><PERSON><PERSON>", "touches", "css", "temp", "lastX", "lastY", "lastTf", "startX", "split", "indexOf", "parseInt", "startY", "targetTouches", "pageX", "pageY", "on", "newX", "newY", "matrix", "join", "panEndHandler", "chart", "off", "zoomWheelHandler", "oc", "newScale", "originalEvent", "deltaY", "setChartScale", "zoomStartHandler", "dist", "getPinchDist", "zoo<PERSON><PERSON><PERSON><PERSON>", "zoomEndHandler", "diff", "Math", "sqrt", "clientX", "clientY", "targetScale", "abs", "parseFloat", "$li", "subObj", "name", "contents", "eq", "trim", "parent", "is", "siblings", "key", "value", "push", "flags", "compact", "constructor", "Function", "for<PERSON>ach", "item", "hybrid", "vertical", "associatedCompact", "<PERSON><PERSON><PERSON>", "includeNodeData", "$node", "id", "getHierarchy", "valid", "getNodeState", "relation", "isVerticalNode", "exist", "visible", "getParent", "getRelatedNodes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "hideParentEnd", "hideParent", "$parent", "hideSiblings", "one", "showParentEnd", "isInAction", "switchVerticalArrow", "showParent", "repaint", "bind", "stopAjax", "$nodeLevel", "isVisibleNode", "isCompactDescendant", "hideChildrenEnd", "animatedNodes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$lowerLevel", "$animatedNodes", "filter", "not", "get", "lowerLevel", "showChildrenEnd", "showChildren", "$levels", "isVerticalDesc", "hideSiblingsEnd", "$nodeContainer", "nodeContainer", "$siblings", "prevAll", "nextAll", "end", "switchHorizontalArrow", "showSiblingsEnd", "visibleNodes", "showRelatedParentEnd", "showSiblings", "$upperLevel", "$visibleNodes", "startLoading", "$edge", "prop", "endLoading", "removeAttr", "some", "attr", "icon", "$arrow", "toggleClass", "sibsVisible", "ajaxURL", "$prevSib", "prev", "$nextSib", "next", "$sibs", "style", "offsetWidth", "nodeEnterLeaveHandler", "$topEdge", "$bottomEdge", "$leftEdge", "flag", "$toggleBtn", "type", "nodeClickHandler", "addAncestors", "parentId", "slice", "wrapAll", "appendTo", "addDescendants", "$descendants", "HideFirstParentEnd", "topEdge", "topEdgeClickHandler", "parentState", "bottomEdgeClickHandler", "childrenState", "$children", "hEdgeClickHandler", "$hEdge", "siblingsState", "backToCompactHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expandVNodesEnd", "vNodes", "collapseVNodesEnd", "toggleVNodes", "$descWrapper", "createGhostNode", "ghostNode", "nodeCover", "$nodeDiv", "origEvent", "isFirefox", "test", "navigator", "userAgent", "toLowerCase", "querySelector", "createElementNS", "add", "append<PERSON><PERSON><PERSON>", "transValues", "isHorizontal", "scale", "xOffset", "setAttribute", "outerWidth", "outerHeight", "offsetX", "yOffset", "offsetY", "ghostNodeWrapper", "createElement", "src", "XMLSerializer", "serializeToString", "dataTransfer", "setDragImage", "getUpperLevel", "getLowerLevel", "getLevelOrderNodes", "queue", "output", "row", "cur", "shift", "toArray", "flat", "filterAllowedDropNodes", "$dragged", "draggingNode", "hasClass", "$dragZone", "$dragHier", "dropCriteria", "dragstartHandler", "setData", "dragover<PERSON><PERSON><PERSON>", "dropEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "async", "datasource", "copy", "horizontalEdges", "$dropSibs", "$dropZone", "dropEvent", "draggedNode", "dragZone", "dropZone", "isDefaultPrevented", "hybridNode", "digger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "findOneNode", "verticalLevel", "findNodeById", "Object", "assign", "removeNode", "dropNode", "prepend", "<PERSON><PERSON><PERSON><PERSON>", "draggedItem", "touchstartHandler", "touchHandled", "touchMoved", "touchmoveHandler", "touchingNodeElement", "currentTarget", "touchDragImage", "createDragImage", "moveDragImage", "$touchingNodes", "elementFromPoint", "touchTargetNode", "touchendHandler", "firstTouch", "fakeMouseClickEvent", "destroyDragImage", "fakeEventForDropHandler", "changedTouches", "createEvent", "initMouseEvent", "screenX", "screenY", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "dispatchEvent", "source", "dragImage", "cloneNode", "sourceRectangle", "copyStyle", "top", "left", "getBoundingClientRect", "sourcePoint", "getTouchPoint", "touchDragImageOffset", "x", "y", "opacity", "body", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "dst", "att", "removeAttribute", "HTMLCanvasElement", "cDst", "width", "cSrc", "height", "getContext", "drawImage", "cs", "getComputedStyle", "pointerEvents", "image", "orgChartMaster", "requestAnimationFrame", "pt", "s", "position", "zIndex", "round", "bindDragDrop", "createNode", "level", "child", "className", "outsider", "nodeData", "nodeTemplate", "nodeContent", "Number", "substr", "floor", "buildInferiorNodes", "$hierarchy", "$nodesLayer", "isHidden", "$nodeCell", "parentsUntil", "_this", "$wrapper", "--ft-width", "--ft-left-offset", "keys", "buildChildNode", "$appendTo", "add<PERSON><PERSON><PERSON><PERSON>", "buildParentNode", "$currentRoot", "$newRootWrapper", "addParent", "buildSiblingNode", "$nodeChart", "newSiblingCount", "existingSibligCount", "siblingCount", "insertPostion", "before", "addBack", "unwrap", "addSiblings", "removeNodes", "hideDropZones", "showDropZones", "dragged", "processExternalDrop", "exportPDF", "canvas", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "jsPDF", "jspdf", "orientation", "unit", "format", "addImage", "toDataURL", "save", "exportPNG", "isWebkit", "documentElement", "isFf", "sidebar", "isEdge", "appName", "appVersion", "msSaveBlob", "msToBlob", "selector", "$mask", "sourceChart", "html2canvas", "clientHeight", "clientWidth", "onclone", "cloneDoc", "then", "fn", "orgchart"], "mappings": "AAUA,aAEA,CAAC,SAAUA,GACa,UAAlB,OAAOC,QAAiD,UAA1B,OAAOA,OAAOC,QAC9CF,EAAQG,QAAQ,QAAQ,EAAGC,OAAQC,QAAQ,EAE3CL,EAAQM,OAAQF,OAAQC,QAAQ,CAEpC,EAAE,SAAUE,EAAGH,EAAQC,EAAUG,GAChB,SAAXC,EAAqBC,EAAMC,GAC7BC,KAAKC,gBAAkBN,EAAEG,CAAI,EAC7BE,KAAKD,KAAOA,EACZC,KAAKE,eAAiB,CACpBC,MAAS,CACPC,MAAS,MACTC,WAAc,WACdC,WAAc,iBACdC,eAAkB,mBAClBC,eAAkB,mBAClBC,cAAiB,oBACjBC,cAAiB,sBACjBC,YAAe,0BACfC,UAAa,kBACbC,SAAY,mBACZC,QAAW,aACb,EACAC,UAAa,OACbC,OAAU,KACVC,mBAAsB,CAAA,EACtBC,aAAgB,IAChBC,WAAc,GACdC,aAAgB,CAAA,EAChBC,iBAAoB,SACpBC,eAAkB,WAClBC,oBAAuB,MACvBC,UAAa,CAAA,EACbC,UAAa,MACbC,IAAO,CAAA,EACPC,KAAQ,CAAA,EACRC,YAAe,EACfC,aAAgB,EAClB,CACF,CAEAhC,EAASiC,UAAY,CAEnBC,KAAM,SAAUhC,GAEdC,KAAKgC,QAAUrC,EAAEsC,OAAO,GAAIjC,KAAKE,eAAgBF,KAAKD,KAAMA,CAAI,EADhE,IAGIE,EAAkBD,KAAKC,gBAIvBiC,GAHAlC,KAAKmC,QACPnC,KAAKmC,OAAOC,OAAO,EAEVpC,KAAKgC,QAAQE,MACpBC,EAASnC,KAAKmC,OAASxC,EAAE,QAAS,CACpCuC,KAAQ,CAAEF,QAAWhC,KAAKgC,OAAQ,EAClCK,MAAS,YAA0C,KAA5BrC,KAAKgC,QAAQb,WAAoB,IAAMnB,KAAKgC,QAAQb,WAAa,KAAkC,QAA3BnB,KAAKgC,QAAQP,UAAsB,IAAMzB,KAAKgC,QAAQP,UAAY,IACjKa,MAAS,SAASC,GACX5C,EAAE4C,EAAMC,MAAM,EAAEC,QAAQ,OAAO,EAAEC,QACpCP,EAAOQ,KAAK,eAAe,EAAEC,YAAY,SAAS,CAEtD,CACF,CAAC,EAIGC,GAH4B,aAA5B,OAAOC,kBACT9C,KAAK+C,iBAAiB,EAEZC,MAAMC,QAAQf,CAAI,EAAIC,EAAOe,OAAOvD,EAAE,yBAAyB,CAAC,EAAEgD,KAAK,QAAQ,EACvFR,EAAOe,OAAOvD,EAAE,oDAAoD,CAAC,EAAEgD,KAAK,YAAY,GA2B5F,OAzBMT,aAAgBvC,EAClBK,KAAKmD,eAAeN,EAAO7C,KAAKoD,YAAYlB,EAAKmB,SAAS,CAAC,EAAG,EAAGrD,KAAKgC,OAAO,EAEzEE,EAAKoB,aACPtD,KAAKmD,eAAeN,EAAOX,CAAI,EAE/BlC,KAAKmD,eAAeN,EAAOG,MAAMC,QAAQf,CAAI,EAAIA,EAAOlC,KAAKuD,UAAUrB,EAAM,IAAI,CAAC,EAIxFjC,EAAgBiD,OAAOf,CAAM,EAGzBnC,KAAKgC,QAAQZ,cAAgB,CAACzB,EAAE,gBAAgB,EAAE+C,QACpD1C,KAAKwD,mBAAmB,EAGtBxD,KAAKgC,QAAQN,KACf1B,KAAKyD,QAAQ,EAGXzD,KAAKgC,QAAQL,MACf3B,KAAK0D,SAAS,EAGT1D,IACT,EACA2D,mBAAoB,WAElB3D,KAAKmC,OAAOQ,KAAK,eAAe,EAC7BiB,KAAK,CAACC,EAAOC,KACZnE,EAAEmE,CAAI,EAAEC,SAASpE,EAAEmE,CAAI,EAAEE,QAAQ,UAAU,EAAEtB,OAAS,GAAM,EAAI,OAAS,KAAK,CAChF,CAAC,CAQL,EAEAK,iBAAkB,WAChB,IAAIkB,EAAOjE,KACPkE,EAAK,IAAIpB,iBAAiB,SAAUqB,GACtCD,EAAGE,WAAW,EACdC,EACA,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUzB,OAAQ4B,CAAC,GACrC,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAAUG,GAAGE,WAAW9B,OAAQ6B,CAAC,GACnD,GAAIJ,EAAUG,GAAGE,WAAWD,GAAGE,UAAUC,SAAS,UAAU,EAAG,CAC7DT,EAAKN,mBAAmB,EACpBM,EAAKjC,QAAQ2C,eAAuD,YAAtC,OAAOV,EAAKjC,QAAQ2C,eACpDV,EAAKjC,QAAQ2C,cAAcV,EAAK9B,MAAM,EAExC,IAAIyC,EAAYjF,EAAEkF,MAAM,eAAe,EACvCZ,EAAK9B,OAAO2C,QAAQF,CAAS,EAC7B,MAAMP,CACR,CAGN,CAAC,EACDH,EAAGa,QAAQ/E,KAAKC,gBAAgB,GAAI,CAAE+E,UAAW,CAAA,CAAK,CAAC,CACzD,EACAC,iBAAkB,SAAUC,EAASC,GAC/BP,EAAYjF,EAAEkF,MAAM,QAAUM,EAAM,WAAW,EACnDD,EAAQJ,QAAQF,CAAS,CAC3B,EACAQ,iBAAkB,SAAUF,EAASC,GAC/BP,EAAYjF,EAAEkF,MAAM,QAAUM,EAAM,WAAW,EACnDD,EAAQJ,QAAQF,CAAS,CAC3B,EAEApB,mBAAoB,WAClB,IAAIS,EAAOjE,KACPqF,EAAa1F,EAAE,WAAY,CAC7B0C,MAAS,gBACTiD,KAAQtF,KAAKgC,QAAQX,iBACrBiB,MAAS,SAASiD,GAChBA,EAAEC,eAAe,EACjBvB,EAAKwB,OAAO,CACd,CACF,CAAC,EACDzF,KAAKC,gBAAgByF,MAAML,CAAU,CACvC,EACAM,WAAY,SAAU5F,EAAM6F,GAsC1B,MArCoB,UAAhB,OAAO7F,IACI,QAATA,IACE6F,EACF5F,KAAKyD,QAAQ,EAEbzD,KAAK6F,UAAU,GAGN,SAAT9F,KACE6F,EACF5F,KAAK0D,SAAS,EAEd1D,KAAK8F,WAAW,GAIF,UAAhB,OAAO/F,IACLA,EAAKmC,KACPlC,KAAK+B,KAAKhC,CAAI,GAEU,KAAA,IAAbA,EAAK2B,MACV3B,EAAK2B,IACP1B,KAAKyD,QAAQ,EAEbzD,KAAK6F,UAAU,GAGM,KAAA,IAAd9F,EAAK4B,OACV5B,EAAK4B,KACP3B,KAAK0D,SAAS,EAEd1D,KAAK8F,WAAW,KAMjB9F,IACT,EAEA+F,gBAAiB,SAAUR,GACzB,IAAIpD,EAASxC,EAAE4F,EAAES,cAAc,EAC/B,GAAIrG,EAAE4F,EAAE/C,MAAM,EAAEC,QAAQ,OAAO,EAAEC,QAAW6C,EAAEU,SAA8B,EAAnBV,EAAEU,QAAQvD,OACjEP,EAAOD,KAAK,UAAW,CAAA,CAAK,MAD9B,CAIEC,EAAO+D,IAAI,SAAU,MAAM,EAAEhE,KAAK,UAAW,CAAA,CAAI,EAEnD,IAIMiE,EAJFC,EAAQ,EACRC,EAAQ,EACRC,EAASnE,EAAO+D,IAAI,WAAW,EAW/BK,GAVW,SAAXD,IACEH,EAAOG,EAAOE,MAAM,GAAG,EAGzBH,EAF2B,CAAC,IAA1BC,EAAOG,QAAQ,IAAI,GACrBL,EAAQM,SAASP,EAAK,EAAE,EAChBO,SAASP,EAAK,EAAE,IAExBC,EAAQM,SAASP,EAAK,GAAG,EACjBO,SAASP,EAAK,GAAG,IAGhB,GACTQ,EAAS,EACb,GAAKpB,EAAEqB,eAGA,GAA+B,IAA3BrB,EAAEqB,cAAclE,OACzB6D,EAAShB,EAAEqB,cAAc,GAAGC,MAAQT,EACpCO,EAASpB,EAAEqB,cAAc,GAAGE,MAAQT,OAC/B,GAA6B,EAAzBd,EAAEqB,cAAclE,OACzB,MACF,MAPE6D,EAAShB,EAAEsB,MAAQT,EACnBO,EAASpB,EAAEuB,MAAQT,EAOrBlE,EAAO4E,GAAG,sBAAsB,SAASxB,GACvC,GAAKpD,EAAOD,KAAK,SAAS,EAA1B,CAGA,IAAI8E,EAAO,EACPC,EAAO,EACX,GAAK1B,EAAEqB,eAGA,GAA+B,IAA3BrB,EAAEqB,cAAclE,OACzBsE,EAAOzB,EAAEqB,cAAc,GAAGC,MAAQN,EAClCU,EAAO1B,EAAEqB,cAAc,GAAGE,MAAQH,OAC7B,GAA6B,EAAzBpB,EAAEqB,cAAclE,OACzB,MACF,MAPEsE,EAAOzB,EAAEsB,MAAQN,EACjBU,EAAO1B,EAAEuB,MAAQH,EAOnB,IAQMO,EARFZ,EAASnE,EAAO+D,IAAI,WAAW,EACpB,SAAXI,EAC2B,CAAC,IAA1BA,EAAOG,QAAQ,IAAI,EACrBtE,EAAO+D,IAAI,YAAa,sBAAwBc,EAAO,KAAOC,EAAO,GAAG,EAExE9E,EAAO+D,IAAI,YAAa,gDAAkDc,EAAO,KAAOC,EAAO,SAAS,GAGtGC,EAASZ,EAAOE,MAAM,GAAG,EACA,CAAC,IAA1BF,EAAOG,QAAQ,IAAI,GACrBS,EAAO,GAAK,IAAMF,EAClBE,EAAO,GAAK,IAAMD,EAAO,MAEzBC,EAAO,IAAM,IAAMF,EACnBE,EAAO,IAAM,IAAMD,GAErB9E,EAAO+D,IAAI,YAAagB,EAAOC,KAAK,GAAG,CAAC,EA5B1C,CA8BF,CAAC,CA1DD,CA2DF,EAEAC,cAAe,SAAU7B,GACnBA,EAAErD,KAAKmF,MAAMnF,KAAK,SAAS,GAC7BqD,EAAErD,KAAKmF,MAAMnF,KAAK,UAAW,CAAA,CAAK,EAAEgE,IAAI,SAAU,SAAS,EAAEoB,IAAI,WAAW,CAEhF,EAEA7D,QAAS,WACPzD,KAAKC,gBAAgBiG,IAAI,WAAY,QAAQ,EAC7ClG,KAAKmC,OAAO4E,GAAG,uBAAwB/G,KAAK+F,eAAe,EAC3DpG,EAAEF,CAAQ,EAAEsH,GAAG,mBAAoB,CAAEM,MAASrH,KAAKmC,MAAO,EAAGnC,KAAKoH,aAAa,CACjF,EAEAvB,UAAW,WACT7F,KAAKC,gBAAgBiG,IAAI,WAAY,MAAM,EAC3ClG,KAAKmC,OAAOmF,IAAI,uBAAwBtH,KAAK+F,eAAe,EAC5DpG,EAAEF,CAAQ,EAAE6H,IAAI,mBAAoBtH,KAAKoH,aAAa,CACxD,EAEAG,iBAAkB,SAAUhC,GAC1B,IAAIiC,EAAKjC,EAAErD,KAAKsF,GAEZC,GADJlC,EAAEC,eAAe,EACD,GAA8B,EAAzBD,EAAEmC,cAAcC,OAAa,CAAC,GAAM,KACzDH,EAAGI,cAAcJ,EAAGrF,OAAQsF,CAAQ,CACtC,EAEAI,iBAAkB,SAAUtC,GAC1B,IACMiC,EADHjC,EAAEU,SAAgC,IAArBV,EAAEU,QAAQvD,UACpB8E,EAAKjC,EAAErD,KAAKsF,IACbrF,OAAOD,KAAK,WAAY,CAAA,CAAI,EAC3B4F,EAAON,EAAGO,aAAaxC,CAAC,EAC5BiC,EAAGrF,OAAOD,KAAK,iBAAkB4F,CAAI,EAEzC,EACAE,eAAgB,SAAUzC,GACxB,IAAIiC,EAAKjC,EAAErD,KAAKsF,GACbA,EAAGrF,OAAOD,KAAK,UAAU,IACtB4F,EAAON,EAAGO,aAAaxC,CAAC,EAC5BiC,EAAGrF,OAAOD,KAAK,eAAgB4F,CAAI,EAEvC,EACAG,eAAgB,SAAU1C,GACxB,IAGM2C,EAHFV,EAAKjC,EAAErD,KAAKsF,GACbA,EAAGrF,OAAOD,KAAK,UAAU,IAC1BsF,EAAGrF,OAAOD,KAAK,WAAY,CAAA,CAAK,EAErB,GADPgG,EAAOV,EAAGrF,OAAOD,KAAK,cAAc,EAAIsF,EAAGrF,OAAOD,KAAK,gBAAgB,GAEzEsF,EAAGI,cAAcJ,EAAGrF,OAAQ,GAAG,EACtB+F,EAAO,GAChBV,EAAGI,cAAcJ,EAAGrF,OAAQ,EAAG,EAGrC,EAEAuB,SAAU,WACR1D,KAAKC,gBAAgB8G,GAAG,QAAS,CAAES,GAAMxH,IAAK,EAAGA,KAAKuH,gBAAgB,EACtEvH,KAAKC,gBAAgB8G,GAAG,aAAc,CAAES,GAAMxH,IAAK,EAAGA,KAAK6H,gBAAgB,EAC3ElI,EAAEF,CAAQ,EAAEsH,GAAG,YAAa,CAAES,GAAMxH,IAAK,EAAGA,KAAKgI,cAAc,EAC/DrI,EAAEF,CAAQ,EAAEsH,GAAG,WAAY,CAAES,GAAMxH,IAAK,EAAGA,KAAKiI,cAAc,CAChE,EACAnC,WAAY,WACV9F,KAAKC,gBAAgBqH,IAAI,QAAStH,KAAKuH,gBAAgB,EACvDvH,KAAKC,gBAAgBqH,IAAI,aAActH,KAAK6H,gBAAgB,EAC5DlI,EAAEF,CAAQ,EAAE6H,IAAI,YAAatH,KAAKgI,cAAc,EAChDrI,EAAEF,CAAQ,EAAE6H,IAAI,WAAYtH,KAAKiI,cAAc,CACjD,EAEAF,aAAc,SAAUxC,GACtB,OAAO4C,KAAKC,MAAM7C,EAAEU,QAAQ,GAAGoC,QAAU9C,EAAEU,QAAQ,GAAGoC,UAAY9C,EAAEU,QAAQ,GAAGoC,QAAU9C,EAAEU,QAAQ,GAAGoC,UACrG9C,EAAEU,QAAQ,GAAGqC,QAAU/C,EAAEU,QAAQ,GAAGqC,UAAY/C,EAAEU,QAAQ,GAAGqC,QAAU/C,EAAEU,QAAQ,GAAGqC,QAAQ,CAC/F,EAEAV,cAAe,SAAUzF,EAAQsF,GAC/B,IAEIP,EAFAnH,EAAOoC,EAAOD,KAAK,SAAS,EAC5BoE,EAASnE,EAAO+D,IAAI,WAAW,EAE/BqC,EAAc,EACH,SAAXjC,EACFnE,EAAO+D,IAAI,YAAa,SAAWuB,EAAW,IAAMA,EAAW,GAAG,GAElEP,EAASZ,EAAOE,MAAM,GAAG,EACI,CAAC,IAA1BF,EAAOG,QAAQ,IAAI,GACrB8B,EAAcJ,KAAKK,IAAIhJ,EAAOiJ,WAAWvB,EAAO,EAAE,EAAIO,CAAQ,GAC5C1H,EAAK8B,cAAgB0G,EAAcxI,EAAK6B,aACxDO,EAAO+D,IAAI,YAAaI,EAAS,UAAYmB,EAAW,IAAMA,EAAW,GAAG,GAG9Ec,EAAcJ,KAAKK,IAAIhJ,EAAOiJ,WAAWvB,EAAO,EAAE,EAAIO,CAAQ,GAC5C1H,EAAK8B,cAAgB0G,EAAcxI,EAAK6B,aACxDO,EAAO+D,IAAI,YAAaI,EAAS,YAAcmB,EAAW,IAAMA,EAAW,MAAM,EAIzF,EAEArE,YAAa,SAAUsF,GACrB,IAAIzE,EAAOjE,KACP2I,EAAS,CACXC,KAAQF,EAAIG,SAAS,EAAEC,GAAG,CAAC,EAAExD,KAAK,EAAEyD,KAAK,EACzCzF,cAAiBoF,EAAIM,OAAO,EAAEA,OAAO,EAAEC,GAAG,IAAI,EAAI,IAAK,MAAQP,EAAIQ,SAAS,IAAI,EAAExG,OAAS,EAAG,IAAMgG,EAAIrF,SAAS,IAAI,EAAEX,OAAS,EAAI,EACtI,EAQA,OAPA/C,EAAEiE,KAAK8E,EAAIxG,KAAK,EAAG,SAASiH,EAAKC,GAC9BT,EAAOQ,GAAOC,CACjB,CAAC,EACDV,EAAIrF,SAAS,IAAI,EAAEA,SAAS,EAAEO,KAAK,WAC5B+E,EAAOtF,WAAYsF,EAAOtF,SAAW,IAC1CsF,EAAOtF,SAASgG,KAAKpF,EAAKb,YAAYzD,EAAEK,IAAI,CAAC,CAAC,CAChD,CAAC,EACM2I,CACT,EAEApF,UAAW,SAAUrB,EAAMoH,GACzB,IAAIrF,EAAOjE,KAiBX,OAhBAkC,EAAKoB,aAAegG,GAASpH,EAAKmB,UAAmC,EAAvBnB,EAAKmB,SAASX,OAAa,EAAI,GACzE1C,KAAKgC,SAASuH,SAASC,cAAgBC,UAAYzJ,KAAKgC,QAAQuH,QAAQrH,CAAI,IAC9EA,EAAKqH,QAAU,CAAA,GAEbrH,EAAKmB,UACPnB,EAAKmB,SAASqG,QAAQ,SAASC,GACzBzH,EAAK0H,QAAU1H,EAAK2H,SACtBF,EAAKE,SAAW,CAAA,EACP3H,EAAKqH,SAAWI,EAAKtG,SAC9BsG,EAAKJ,QAAU,CAAA,EACNrH,EAAKqH,SAAW,CAACI,EAAKtG,WAC/BsG,EAAKG,kBAAoB,CAAA,GAE3B7F,EAAKV,UAAUoG,EAAM,KAA8B,EAAvBzH,EAAKmB,SAASX,OAAa,EAAI,EAAE,CAC/D,CAAC,EAEIR,CACT,EAEA6H,UAAW,SAAU5H,EAAQ6H,GAC3BA,EAAuC,OAApBA,GAA4BA,IAAoBpK,GAAaoK,EAChF,IAAI/F,EAAOjE,KACPiK,EAAQ9H,EAAOQ,KAAK,aAAa,EACjCgG,EAAS,CAAEuB,GAAMD,EAAM,GAAGC,EAAG,EAUjC,OATIF,GACFrK,EAAEiE,KAAKqG,EAAM/H,KAAK,UAAU,EAAG,SAAUiH,EAAKC,GAC5CT,EAAOQ,GAAOC,CAChB,CAAC,EAEHa,EAAMf,SAAS,QAAQ,EAAE7F,SAAS,EAAEO,KAAK,WAClC+E,EAAOtF,WAAYsF,EAAOtF,SAAW,IAC1CsF,EAAOtF,SAASgG,KAAKpF,EAAK8F,UAAUpK,EAAEK,IAAI,EAAGgK,CAAe,CAAC,CAC/D,CAAC,EACMrB,CACT,EAEAwB,aAAc,SAAUH,GAEtB,IAMQI,EANR,OADAJ,EAAuC,OAApBA,GAA4BA,IAAoBpK,GAAaoK,EACrD,KAAA,IAAhBhK,KAAKmC,OACP,iCAEFnC,KAAKmC,OAAOQ,KAAK,OAAO,EAAED,QAGzB0H,EAAQ,CAAA,EACZpK,KAAKmC,OAAOQ,KAAK,OAAO,EAAEiB,KAAK,WAC7B,GAAI,CAAC5D,KAAKkK,GAER,OADAE,EAAQ,CAAA,CAGZ,CAAC,EACIA,EAKFpK,KAAK+J,UAAU/J,KAAKmC,OAAQ6H,CAAe,EAJrC,4EAVF,2BAeb,EAEAK,aAAc,SAAUJ,EAAOK,GAC7B,IAAIpF,EAAU,GACVqF,EAAiB,CAAC,CAACN,EAAMxH,QAAQ,UAAU,EAAEC,OAEjD,GAAiB,YAAb4H,EADWA,GAAY,SAazB,GAXIC,GACFrF,EAAU+E,EAAMxH,QAAQ,IAAI,EAAEuB,QAAQ,IAAI,GAC7BtB,SACXwC,EAAU+E,EAAMxH,QAAQ,QAAQ,GACnBC,SACXwC,EAAU+E,EAAMxH,QAAQ,WAAW,EAAEyG,SAAS,QAAQ,GAI1DhE,EAAU+E,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EAEhDhE,EAAQxC,OACV,OAAIwC,EAAQ+D,GAAG,SAAS,GAAM,CAAC/D,EAAQ+D,GAAG,SAAS,GAAK/D,EAAQzC,QAAQ,QAAQ,EAAEwG,GAAG,SAAS,GAAO,CAAC/D,EAAQ+D,GAAG,SAAS,GAAK/D,EAAQzC,QAAQ,WAAW,EAAEwG,GAAG,SAAS,EAC/J,CAAEuB,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAM,EAEpC,CAAED,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAK,CAC1C,MACK,GAAiB,aAAbH,GAET,IADApF,EAAUqF,EAAiBN,EAAMjB,OAAO,EAAE3F,SAAS,IAAI,EAAI4G,EAAMf,SAAS,QAAQ,GACtExG,OACV,OAAKwC,EAAQ+D,GAAG,SAAS,EAGlB,CAAEuB,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAM,EAFhC,CAAED,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAK,CAG5C,MACK,GAAiB,aAAbH,GAET,IADApF,EAAUqF,EAAiBN,EAAMxH,QAAQ,IAAI,EAAIwH,EAAMjB,OAAO,EAAEE,SAAS,GAC7DxG,SAAW,CAAC6H,GAAkD,EAAhCrF,EAAQ7B,SAAS,IAAI,EAAEX,QAC/D,OAAKwC,EAAQ+D,GAAG,SAAS,GAAM/D,EAAQ8D,OAAO,EAAEC,GAAG,SAAS,GAAOsB,GAAmBrF,EAAQzC,QAAQ,WAAW,EAAEwG,GAAG,SAAS,EAGxH,CAAEuB,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAM,EAFhC,CAAED,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAK,CAG5C,MAGA,IADAvF,EAAU+E,GACEvH,OACV,OAAOwC,EAAQzC,QAAQ,QAAQ,EAAEC,QAAUwC,EAAQzC,QAAQ,QAAQ,EAAEwG,GAAG,SAAS,GAC9E/D,EAAQzC,QAAQ,YAAY,EAAEC,QAAUwC,EAAQzC,QAAQ,YAAY,EAAEwG,GAAG,SAAS,GAClF/D,EAAQzC,QAAQ,WAAW,EAAEC,SAAWwC,EAAQzC,QAAQ,QAAQ,EAAEwG,GAAG,SAAS,GAAK/D,EAAQzC,QAAQ,WAAW,EAAEwG,GAAG,SAAS,GAIxH,CAAEuB,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAM,EAFhC,CAAED,MAAS,CAAA,EAAMC,QAAW,CAAA,CAAK,EAK9C,MAAO,CAAED,MAAS,CAAA,EAAOC,QAAW,CAAA,CAAM,CAC5C,EACAC,UAAW,SAAUT,GACnB,OAAOjK,KAAK2K,gBAAgBV,EAAO,QAAQ,CAC7C,EACAW,YAAa,SAAUX,GACrB,OAAOjK,KAAK2K,gBAAgBV,EAAO,UAAU,CAC/C,EACAY,YAAa,SAAUZ,GACrB,OAAOjK,KAAK2K,gBAAgBV,EAAO,UAAU,CAC/C,EAEAU,gBAAiB,SAAUV,EAAOK,GAChC,OAAKL,GAAWA,aAAiBtK,GAAOsK,EAAMhB,GAAG,OAAO,EAGvC,WAAbqB,EACKL,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EACzB,aAAboB,EACFL,EAAMf,SAAS,QAAQ,EAAE7F,SAAS,YAAY,EAAEV,KAAK,aAAa,EACnD,aAAb2H,EACFL,EAAMxH,QAAQ,YAAY,EAAEyG,SAAS,EAAEvG,KAAK,aAAa,EAEzDhD,EAAE,EATFA,EAAE,CAWb,EACAmL,cAAe,SAAUvI,GACvB5C,EAAE4C,EAAMC,MAAM,EAAEI,YAAY,SAAS,EACrCL,EAAML,KAAK8G,OAAOjF,SAAS,QAAQ,CACrC,EAEAgH,WAAY,SAAUd,GACpB,IAAIe,EAAUf,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EAClD8B,EAAQrI,KAAK,UAAU,EAAED,QAC3BuH,EAAMxH,QAAQ,WAAW,EAAEP,KAAK,SAAU,CAAA,CAAK,EAG7ClC,KAAKqK,aAAaJ,EAAO,UAAU,EAAEQ,SACvCzK,KAAKiL,aAAahB,CAAK,EAGzBA,EAAMjB,OAAO,EAAEjF,SAAS,sBAAsB,EAE1C/D,KAAKqK,aAAaW,CAAO,EAAEP,SAC7BO,EAAQjH,SAAS,oBAAoB,EAAEmH,IAAI,gBAAiB,CAAElC,OAAUgC,CAAQ,EAAGhL,KAAK8K,aAAa,EAGnG9K,KAAKqK,aAAaW,EAAS,QAAQ,EAAEP,SACvCzK,KAAK+K,WAAWC,CAAO,CAE3B,EACAG,cAAe,SAAU5I,GACvB,IAAI0H,EAAQ1H,EAAML,KAAK4B,KACvBnE,EAAE4C,EAAMC,MAAM,EAAEI,YAAY,SAAS,EACjC5C,KAAKoL,WAAWnB,CAAK,GACvBjK,KAAKqL,oBAAoBpB,EAAM5G,SAAS,UAAU,CAAC,CAEvD,EAEAiI,WAAY,SAAUrB,GAEpB,IAAIe,EAAUf,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EAAEtG,YAAY,QAAQ,EAE5EqH,EAAMxH,QAAQ,YAAY,EAAEG,YAAY,sBAAsB,EAE9D5C,KAAKuL,QAAQP,EAAQ,EAAE,EACvBA,EAAQjH,SAAS,SAAS,EAAEnB,YAAY,YAAY,EAAEsI,IAAI,gBAAiB,CAAEpH,KAAQmG,CAAM,EAAGjK,KAAKmL,cAAcK,KAAKxL,IAAI,CAAC,CAC7H,EACAyL,SAAU,SAAUC,GACdA,EAAW/I,KAAK,UAAU,EAAED,QAC9BgJ,EAAWjJ,QAAQ,WAAW,EAAEP,KAAK,SAAU,CAAA,CAAK,CAExD,EACAyJ,cAAe,SAAU9H,EAAO/D,GAC9B,OAAOE,KAAKqK,aAAa1K,EAAEG,CAAI,CAAC,EAAE2K,OACpC,EACAmB,oBAAqB,SAAU/H,EAAO/D,GACpC,OAAOH,EAAEG,CAAI,EAAEkJ,OAAO,EAAEC,GAAG,eAAe,CAC5C,EAEA4C,gBAAiB,SAAUtJ,GACzB,IAAI0H,EAAQ1H,EAAML,KAAK4B,KACvBvB,EAAML,KAAK4J,cAAclJ,YAAY,SAAS,EAC9CL,EAAML,KAAK4J,cAAcrJ,QAAQ,QAAQ,EAAEsB,SAAS,QAAQ,EACxD/D,KAAKoL,WAAWnB,CAAK,GACvBjK,KAAKqL,oBAAoBpB,EAAM5G,SAAS,aAAa,CAAC,CAE1D,EAEA0I,aAAc,SAAU9B,GACtBA,EAAMxH,QAAQ,YAAY,EAAEsB,SAAS,qBAAqB,EAC1D,IAAIiI,EAAc/B,EAAMf,SAAS,QAAQ,EAErC+C,GADJjM,KAAKyL,SAASO,CAAW,EACJA,EAAYrJ,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAAEmM,IAAInM,KAAK4L,oBAAoBJ,KAAKxL,IAAI,CAAC,GACvGgM,EAAY/C,GAAG,WAAW,GAE7CgD,EAAexJ,QAAQ,YAAY,EAAEsB,SAAS,uBAAuB,GAEnEiI,EAAY/C,GAAG,WAAW,GAAK+C,EAAYrJ,KAAK,WAAW,EAAED,SAC/DuJ,EAAetJ,KAAK3C,KAAKgC,QAAQ7B,MAAMU,QAAQ,EAAE+B,YAAY5C,KAAKgC,QAAQ7B,MAAMU,QAAQ,EAAEkD,SAAS/D,KAAKgC,QAAQ7B,MAAMS,SAAS,EAEjIZ,KAAKuL,QAAQU,EAAeG,IAAI,CAAC,CAAC,EAClCH,EAAelI,SAAS,kBAAkB,EAAE+E,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEY,cAAiBG,EAAgBI,WAAcL,EAAalI,KAAQmG,CAAM,EAAGjK,KAAK6L,gBAAgBL,KAAKxL,IAAI,CAAC,CACvL,EAEAsM,gBAAiB,SAAU/J,GACzB,IAAI0H,EAAQ1H,EAAML,KAAK4B,KACvBvB,EAAML,KAAK4J,cAAclJ,YAAY,SAAS,EAC1C5C,KAAKoL,WAAWnB,CAAK,GACvBjK,KAAKqL,oBAAoBpB,EAAM5G,SAAS,aAAa,CAAC,CAE1D,EAEAkJ,aAAc,SAAUtC,GAEtBA,EAAMxH,QAAQ,YAAY,EAAEG,YAAY,qBAAqB,EAD7D,IAEI4J,EAAUvC,EAAMf,SAAS,QAAQ,EACjCuD,EAAiBD,EAAQvD,GAAG,WAAW,EACvCgD,GAAiBQ,EACjBD,EAAQ5J,YAAY,QAAQ,EAAED,KAAK,OAAO,EAC1C6J,EAAQ5J,YAAY,QAAQ,EAAES,SAAS,YAAY,EAAEV,KAAK,aAAa,GAD3BuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAE/EyM,IACHR,EAAeC,OAAO,mBAAmB,EAAEzJ,QAAQ,YAAY,EAAEsB,SAAS,qBAAqB,EAC/FkI,EAAexJ,QAAQ,YAAY,EAAEG,YAAY,uBAAuB,GAG1E5C,KAAKuL,QAAQU,EAAeG,IAAI,CAAC,CAAC,EAClCH,EAAelI,SAAS,SAAS,EAAEnB,YAAY,UAAU,EAAEkG,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEpH,KAAQmG,EAAO6B,cAAiBG,CAAe,EAAGjM,KAAKsM,gBAAgBd,KAAKxL,IAAI,CAAC,CAC3K,EAEA0M,gBAAiB,SAAUnK,GACzB,IAAI0B,EAAOjE,KACPiK,EAAQ1H,EAAML,KAAK4B,KACnB6I,EAAiBpK,EAAML,KAAK0K,cAC5BnL,EAAYc,EAAML,KAAKT,UACvBoL,EAAYpL,EAA2B,SAAdA,EAAuBkL,EAAeG,QAAQ,eAAe,EAAIH,EAAeI,QAAQ,eAAe,EAAKJ,EAAezD,SAAS,EACjK3G,EAAML,KAAK4J,cAAclJ,YAAY,SAAS,EAC9CiK,EAAUlK,KAAK,aAAa,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAC/D4C,YAAY,wBAAwB,EACpCmB,SAAS,WACR,OAAIE,EAAKjC,QAAQuH,QACR,GAEA,UAEX,CAAC,EACHsD,EAAUlK,KAAK,mBAAmB,EAAEoB,SAAS,QAAQ,EAClDiJ,IAAI,EAAEjJ,SAAS,QAAQ,EAEtB/D,KAAKoL,WAAWnB,CAAK,GACvBjK,KAAKiN,sBAAsBhD,CAAK,CAEpC,EAEAgB,aAAc,SAAUhB,EAAOxI,GAC7B,IACIkL,EAAiB1C,EAAMxH,QAAQ,YAAY,EAAEsB,SAAS,qBAAqB,EAqB3EkI,GApBAU,EAAezD,SAAS,EAAEvG,KAAK,UAAU,EAAED,QAC7CuH,EAAMxH,QAAQ,WAAW,EAAEP,KAAK,SAAU,CAAA,CAAK,EAE7CT,EACgB,SAAdA,EACFkL,EAAe5I,SAAS,WAAW,EAChC+I,QAAQ,sBAAsB,EAAElK,YAAY,+BAA+B,EAAEoK,IAAI,EACjFF,QAAQ,EAAE/I,SAAS,wCAAwC,EAC3DpB,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAAE+D,SAAS,qBAAqB,EAErF4I,EAAe5I,SAAS,YAAY,EACjCgJ,QAAQ,sBAAsB,EAAEnK,YAAY,gCAAgC,EAAEoK,IAAI,EAClFD,QAAQ,EAAEhJ,SAAS,wCAAwC,EAC3DpB,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAAE+D,SAAS,oBAAoB,GAGtF4I,EAAeG,QAAQ,EAAEnK,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAAE+D,SAAS,qBAAqB,EAC3G4I,EAAeI,QAAQ,EAAEpK,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,EAAE+D,SAAS,oBAAoB,EAC1G4I,EAAezD,SAAS,EAAEnF,SAAS,wCAAwC,GAExD4I,EAAezD,SAAS,EAAEvG,KAAK,UAAU,GAC9DsJ,EAAenD,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEpH,KAAQmG,EAAO2C,cAAiBD,EAAgBlL,UAAaA,EAAWqK,cAAiBG,CAAe,EAAGjM,KAAK0M,gBAAgBlB,KAAKxL,IAAI,CAAC,CACxL,EAEAkN,gBAAiB,SAAU3K,GACzB,IAAI0H,EAAQ1H,EAAML,KAAK4B,KACvBvB,EAAML,KAAKiL,aAAavK,YAAY,SAAS,EACzC5C,KAAKoL,WAAWnB,CAAK,IACvBjK,KAAKiN,sBAAsBhD,CAAK,EAChCA,EAAM5G,SAAS,UAAU,EAAET,YAAY5C,KAAKgC,QAAQ7B,MAAMG,UAAU,EAAEyD,SAAS/D,KAAKgC,QAAQ7B,MAAMI,cAAc,EAEpH,EAEA6M,qBAAsB,SAAS7K,GAC7B5C,EAAE4C,EAAMC,MAAM,EAAEI,YAAY,SAAS,CACvC,EAEAyK,aAAc,SAAUpD,EAAOxI,GAC7B,IAEIoL,EAAYlN,EAAE,EACdgN,EAAiB1C,EAAMxH,QAAQ,YAAY,EAG3CoK,GAFApL,EACgB,SAAdA,EACUkL,EAAeG,QAAQ,EAEvBH,EAAeI,QAAQ,EAGzB9C,EAAMxH,QAAQ,YAAY,EAAEyG,SAAS,GALVtG,YAAY,QAAQ,EAQzD0K,EAAcrD,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EAmBtDqE,GAlBA9L,GACFkL,EAAe/J,YAAYnB,EAAY,OAAO,EACzCkL,EAAe1D,GAAG,gBAAgB,GACrC0D,EAAe/J,YAAY,qBAAqB,EAElDiK,EAAUjK,YAAY,sBAAwBnB,EAAY,OAAO,IAEjEwI,EAAMxH,QAAQ,YAAY,EAAEG,YAAY,qBAAqB,EAC7DiK,EAAUjK,YAAY,oBAAoB,GAGvC5C,KAAKqK,aAAaJ,EAAO,QAAQ,EAAEQ,UACtCR,EAAMxH,QAAQ,YAAY,EAAEG,YAAY,sBAAsB,EAC9D0K,EAAY1K,YAAY,QAAQ,EAChC5C,KAAKuL,QAAQ+B,EAAY,EAAE,EAC3BA,EAAYvJ,SAAS,SAAS,EAAEnB,YAAY,YAAY,EAAEsI,IAAI,gBAAiBlL,KAAKoN,oBAAoB,GAGtFP,EAAUlK,KAAK,OAAO,EAAEuJ,OAAOlM,KAAK2L,cAAcH,KAAKxL,IAAI,CAAC,GAChFA,KAAKuL,QAAQgC,EAAcnB,IAAI,CAAC,CAAC,EACjCmB,EAAcxJ,SAAS,SAAS,EAAEnB,YAAY,wBAAwB,EACtE2K,EAAczE,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEpH,KAAQmG,EAAOkD,aAAgBI,CAAc,EAAGvN,KAAKkN,gBAAgB1B,KAAKxL,IAAI,CAAC,CAC5H,EAEAwN,aAAc,SAAUC,GACtB,IAAItL,EAASnC,KAAKmC,OAClB,OAAqC,KAAA,IAA1BA,EAAOD,KAAK,QAAQ,GAA+C,CAAA,IAA1BC,EAAOD,KAAK,QAAQ,KAIxEuL,EAAM1J,SAAS,QAAQ,EACvB0J,EAAMzE,OAAO,EAAE9F,oBAAoBlD,KAAKgC,QAAQ7B,MAAMC,SAASJ,KAAKgC,QAAQ7B,MAAMW,uBAAuB,EACtGuC,SAAS,EAAE8I,IAAI,UAAU,EAAEjG,IAAI,UAAW,EAAG,EAChD/D,EAAOD,KAAK,SAAU,CAAA,CAAI,EAC1BvC,EAAE,gBAAgB,EAAE+N,KAAK,WAAY,CAAA,CAAI,EAClC,CAAA,EACT,EAEAC,WAAY,SAAUF,GACpB,IAAIxD,EAAQwD,EAAMzE,OAAO,EACzByE,EAAM7K,YAAY,QAAQ,EAC1BqH,EAAMtH,KAAK,UAAU,EAAEP,OAAO,EAC9B6H,EAAM5G,SAAS,EAAEuK,WAAW,OAAO,EACnC5N,KAAKmC,OAAOD,KAAK,SAAU,CAAA,CAAK,EAChCvC,EAAE,gBAAgB,EAAE+N,KAAK,WAAY,CAAA,CAAK,CAC5C,EAEAtC,WAAY,SAAUnB,GAEpB,MAAO,CACLjK,KAAKgC,QAAQ7B,MAAMG,WACnBN,KAAKgC,QAAQ7B,MAAMI,eACnBP,KAAKgC,QAAQ7B,MAAMK,eACnBR,KAAKgC,QAAQ7B,MAAMM,eACnBoN,KAAK,GAAgE,CAAC,EAAvD5D,EAAM5G,SAAS,OAAO,EAAEyK,KAAK,OAAO,EAAErH,QAAQsH,CAAI,CAAM,CAC3E,EAEA1C,oBAAqB,SAAU2C,GAC7BA,EAAOC,YAAejO,KAAKgC,QAAQ7B,MAAMG,WAAtB,IAAoCN,KAAKgC,QAAQ7B,MAAMI,cAAgB,CAC5F,EAEA0M,sBAAuB,SAAUhD,GAC/B,IAoBMiE,EApBFnO,EAAOC,KAAKgC,QACZjC,EAAKkB,qBAA+C,KAAA,IAAjBlB,EAAKoO,SAA2BlE,EAAMxH,QAAQ,QAAQ,EAAEP,KAAK,gBAAgB,KAC9GkM,EAAWnE,EAAMjB,OAAO,EAAEqF,KAAK,GACtB3L,SACP0L,EAASnF,GAAG,SAAS,EACvBgB,EAAM5G,SAAS,WAAW,EAAEU,SAAShE,EAAKI,MAAMK,cAAc,EAAEoC,YAAY7C,EAAKI,MAAMM,aAAa,EAEpGwJ,EAAM5G,SAAS,WAAW,EAAEU,SAAShE,EAAKI,MAAMM,aAAa,EAAEmC,YAAY7C,EAAKI,MAAMK,cAAc,IAGpG8N,EAAWrE,EAAMjB,OAAO,EAAEuF,KAAK,GACtB7L,SACP4L,EAASrF,GAAG,SAAS,EACvBgB,EAAM5G,SAAS,YAAY,EAAEU,SAAShE,EAAKI,MAAMM,aAAa,EAAEmC,YAAY7C,EAAKI,MAAMK,cAAc,EAErGyJ,EAAM5G,SAAS,YAAY,EAAEU,SAAShE,EAAKI,MAAMK,cAAc,EAAEoC,YAAY7C,EAAKI,MAAMM,aAAa,KAKrGyN,EAAcM,CAAAA,EADdA,EAAQvE,EAAMjB,OAAO,EAAEE,SAAS,GACZxG,QAAS,CAAC8L,EAAMvF,GAAG,SAAS,EACpDgB,EAAM5G,SAAS,WAAW,EAAE4K,YAAYlO,EAAKI,MAAMM,cAAeyN,CAAW,EAAED,YAAYlO,EAAKI,MAAMK,eAAgB,CAAC0N,CAAW,EAClIjE,EAAM5G,SAAS,YAAY,EAAE4K,YAAYlO,EAAKI,MAAMK,eAAgB0N,CAAW,EAAED,YAAYlO,EAAKI,MAAMM,cAAe,CAACyN,CAAW,EAEvI,EAEA3C,QAAS,SAAUzH,GACbA,IACFA,EAAK2K,MAAMC,YAAc5K,EAAK4K,YAElC,EAEAC,sBAAuB,SAAUpM,GAC/B,IAaMqM,EAEAC,EACAC,EAhBF7E,EAAQtK,EAAE4C,EAAMyD,cAAc,EAC9B+I,EAAO,CAAA,EACP9E,EAAMxH,QAAQ,iBAAiB,EAAEC,QAC/BsM,EAAa/E,EAAM5G,SAAS,YAAY,EACzB,eAAfd,EAAM0M,KACJhF,EAAM5G,SAAS,YAAY,EAAEX,SAC/BqM,EAAO/O,KAAKqK,aAAaJ,EAAO,UAAU,EAAEQ,QAC5CuE,EAAWf,YAAYjO,KAAKgC,QAAQ7B,MAAMS,UAAW,CAACmO,CAAI,EAAEd,YAAYjO,KAAKgC,QAAQ7B,MAAMU,SAAUkO,CAAI,GAG3GC,EAAWpM,YAAe5C,KAAKgC,QAAQ7B,MAAMS,UAAtB,IAAmCZ,KAAKgC,QAAQ7B,MAAMU,QAAU,IAGrF+N,EAAW3E,EAAM5G,SAAS,UAAU,EACvB4G,EAAM5G,SAAS,YAAY,EACxCwL,EAAc5E,EAAM5G,SAAS,aAAa,EAC1CyL,EAAY7E,EAAM5G,SAAS,WAAW,EACvB,eAAfd,EAAM0M,MACJL,EAASlM,SACXqM,EAAO/O,KAAKqK,aAAaJ,EAAO,QAAQ,EAAEQ,QAC1CmE,EAASX,YAAYjO,KAAKgC,QAAQ7B,MAAMG,WAAY,CAACyO,CAAI,EAAEd,YAAYjO,KAAKgC,QAAQ7B,MAAMI,eAAgBwO,CAAI,GAE5GF,EAAYnM,SACdqM,EAAO/O,KAAKqK,aAAaJ,EAAO,UAAU,EAAEQ,QAC5CoE,EAAYZ,YAAYjO,KAAKgC,QAAQ7B,MAAMI,eAAgB,CAACwO,CAAI,EAAEd,YAAYjO,KAAKgC,QAAQ7B,MAAMG,WAAYyO,CAAI,GAE/GD,EAAUpM,QACZ1C,KAAKiN,sBAAsBhD,CAAK,GAGlCA,EAAM5G,SAAS,OAAO,EAAET,eAAe5C,KAAKgC,QAAQ7B,MAAMG,cAAcN,KAAKgC,QAAQ7B,MAAMI,kBAAkBP,KAAKgC,QAAQ7B,MAAMK,kBAAkBR,KAAKgC,QAAQ7B,MAAMM,aAAe,EAG1L,EAEAyO,iBAAkB,SAAU3M,GAC1BvC,KAAKmC,OAAOQ,KAAK,UAAU,EAAEC,YAAY,SAAS,EAClDjD,EAAE4C,EAAMyD,cAAc,EAAEjC,SAAS,SAAS,CAC5C,EACAoL,aAAc,SAAUjN,EAAMkN,GAC5B,IAAIvM,EAAQ7C,KAAKmC,OAAOkB,SAAS,QAAQ,EAAEA,SAAS,YAAY,EAChErD,KAAKmD,eAAeN,EAAOX,CAAI,EAC/BW,EAAMQ,SAAS,EAAEgM,MAAM,EAAG,CAAC,EACxBC,QAAQ,6BAA6B,EAAEtG,OAAO,EAC9CuG,SAAS5P,EAAE,IAAMyP,CAAQ,EAAElG,SAAS,QAAQ,CAAC,CAClD,EACAsG,eAAe,SAAUtN,EAAM8I,GAC7B,IAAI/G,EAAOjE,KACPyP,EAAe9P,EAAE,yBAAyB,EAC9CqL,EAAQtF,MAAM+J,CAAY,EAC1B9P,EAAEiE,KAAK1B,EAAM,SAAUoC,GACrBmL,EAAavM,OAAOvD,EAAE,6BAA6B,CAAC,EACpDsE,EAAKd,eAAesM,EAAapM,SAAS,EAAEyF,GAAGxE,CAAC,EAAGtE,IAAI,CACzD,CAAC,CACH,EAEA0P,mBAAoB,SAAUnN,GAC5B,IAAIqM,EAAWrM,EAAML,KAAKyN,QACtB1F,EAAQ2E,EAAS5F,OAAO,EACxBhJ,KAAKoL,WAAWnB,CAAK,IACvBjK,KAAKqL,oBAAoBuD,CAAQ,EACjC5O,KAAKiN,sBAAsBhD,CAAK,EAEpC,EAEA2F,oBAAqB,SAAUrN,GAC7B,IAKMyI,EAJF4D,EAAWjP,EAAE4C,EAAMC,MAAM,EACzByH,EAAQtK,EAAE4C,EAAMyD,cAAc,EAC9B6J,EAAc7P,KAAKqK,aAAaJ,EAAO,QAAQ,EAC/C4F,EAAYrF,OAEVQ,EADAA,EAAUf,EAAMxH,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,GAC1CD,GAAG,UAAU,IAErB4G,EAAYpF,SACdzK,KAAK+K,WAAWd,CAAK,EACrBe,EAAQE,IAAI,gBAAiB,CAAEyE,QAAWf,CAAS,EAAG5O,KAAK0P,mBAAmBlE,KAAKxL,IAAI,CAAC,EACxFA,KAAKoF,iBAAiB6E,EAAO,QAAQ,IAErCjK,KAAKsL,WAAWrB,CAAK,EACrBjK,KAAKiF,iBAAiBgF,EAAO,QAAQ,GAG3C,EAEA6F,uBAAwB,SAAUvN,GACd5C,EAAE4C,EAAMC,MAAM,EAAhC,IACIyH,EAAQtK,EAAE4C,EAAMyD,cAAc,EAC9B+J,EAAgB/P,KAAKqK,aAAaJ,EAAO,UAAU,EACnD8F,EAAcvF,OAEZwF,CADY/F,EAAMf,SAAS,QAAQ,EAAE7F,SAAS,EAAEA,SAAS,OAAO,EACtD4F,GAAG,UAAU,IAEvB8G,EAActF,SAChBzK,KAAK+L,aAAa9B,CAAK,EACvBjK,KAAKoF,iBAAiB6E,EAAO,UAAU,IAEvCjK,KAAKuM,aAAatC,CAAK,EACvBjK,KAAKiF,iBAAiBgF,EAAO,UAAU,GAG7C,EAEAgG,kBAAmB,SAAU1N,GAC3B,IASQ+L,EATJ4B,EAASvQ,EAAE4C,EAAMC,MAAM,EACvByH,EAAQtK,EAAE4C,EAAMyD,cAAc,EAC9BjG,EAAOC,KAAKgC,QACZmO,EAAgBnQ,KAAKqK,aAAaJ,EAAO,UAAU,EACnDkG,EAAc3F,OAEZqC,CADY5C,EAAMxH,QAAQ,YAAY,EAAEyG,SAAS,EACvCvG,KAAK,UAAU,EAAED,SAC3B3C,EAAKkB,oBACHmN,EAAWnE,EAAMxH,QAAQ,YAAY,EAAE4L,KAAK,EAC5CC,EAAWrE,EAAMxH,QAAQ,YAAY,EAAE8L,KAAK,EAC5C2B,EAAOjH,GAAG,WAAW,EACnBmF,EAASnF,GAAG,SAAS,GACvBjJ,KAAKqN,aAAapD,EAAO,MAAM,EAC/BjK,KAAKiF,iBAAiBgF,EAAM,UAAU,IAEtCjK,KAAKiL,aAAahB,EAAO,MAAM,EAC/BjK,KAAKoF,iBAAiB6E,EAAO,UAAU,GAGrCqE,EAASrF,GAAG,SAAS,GACvBjJ,KAAKqN,aAAapD,EAAO,OAAO,EAChCjK,KAAKiF,iBAAiBgF,EAAM,UAAU,IAEtCjK,KAAKiL,aAAahB,EAAO,OAAO,EAChCjK,KAAKoF,iBAAiB6E,EAAO,UAAU,IAIvCkG,EAAc1F,SAChBzK,KAAKiL,aAAahB,CAAK,EACvBjK,KAAKoF,iBAAiB6E,EAAO,UAAU,IAEvCjK,KAAKqN,aAAapD,CAAK,EACvBjK,KAAKiF,iBAAiBgF,EAAO,UAAU,GAI/C,EAEAmG,qBAAsB,SAAU7N,GAC9B5C,EAAE4C,EAAMyD,cAAc,EAAEpD,YAAY,WAAW,EAC5CD,KAAK,YAAY,EAAEC,YAAY,WAAW,EAC1CS,SAAS,sBAAsB,EAAEU,SAAS,QAAQ,EAAEiJ,IAAI,EACxD3J,SAAS,oBAAoB,EAAET,YAAY,QAAQ,EACtDjD,EAAE4C,EAAMyD,cAAc,EAAE3C,SAAS,sBAAsB,EAAEU,SAAS,QAAQ,EAAEiJ,IAAI,EAC7E3J,SAAS,oBAAoB,EAAET,YAAY,QAAQ,CACxD,EAEAyN,mBAAoB,SAAU9N,GAC5B5C,EAAE4C,EAAMyD,cAAc,EACnBjC,SAAS,WAAW,EACpBV,SAAS,oBAAoB,EAAEU,SAAS,QAAQ,EAAEiJ,IAAI,EACtD3J,SAAS,sBAAsB,EAAET,YAAY,QAAQ,CAC1D,EAEA0N,gBAAiB,SAAU/N,GACzBA,EAAML,KAAKqO,OAAO3N,YAAY,SAAS,CACzC,EAEA4N,kBAAmB,SAAUjO,GAC3BA,EAAML,KAAKqO,OAAO3N,YAAY,SAAS,EAAEH,QAAQ,IAAI,EAAEsB,SAAS,QAAQ,CAC1E,EAEA0M,aAAc,SAAUlO,GACtB,IAAIyM,EAAarP,EAAE4C,EAAMC,MAAM,EAC3BkO,EAAe1B,EAAWhG,OAAO,EAAEuF,KAAK,EACxCkB,EAAeiB,EAAa/N,KAAK,OAAO,EACxCqN,EAAYU,EAAarN,SAAS,EAAEA,SAAS,OAAO,EACpD2M,EAAU/G,GAAG,UAAU,IAC3B+F,EAAWf,YAAejO,KAAKgC,QAAQ7B,MAAMS,UAAtB,IAAmCZ,KAAKgC,QAAQ7B,MAAMU,QAAU,EACnF4O,EAAa3G,GAAG,CAAC,EAAEG,GAAG,WAAW,GACnCyH,EAAa9N,YAAY,QAAQ,EACjC5C,KAAKuL,QAAQyE,EAAU5D,IAAI,CAAC,CAAC,EAC7B4D,EAAUjM,SAAS,SAAS,EAAEnB,YAAY,UAAU,EAAEkG,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEqF,OAAUP,CAAU,EAAGhQ,KAAKsQ,eAAe,IAE9Hb,EAAa1L,SAAS,kBAAkB,EAAE+E,GAAG,CAAC,EAAEoC,IAAI,gBAAiB,CAAEqF,OAAUd,CAAa,EAAGzP,KAAKwQ,iBAAiB,EACvHf,EAAa9M,KAAK,YAAY,EAAEC,YAAe5C,KAAKgC,QAAQ7B,MAAMS,UAAtB,IAAmCZ,KAAKgC,QAAQ7B,MAAMU,QAAU,GAEhH,EAEA8P,gBAAiB,SAAUpO,GACzB,IAIIqO,EAAWC,EAJXC,EAAWnR,EAAE4C,EAAMC,MAAM,EACzBzC,EAAOC,KAAKgC,QACZ+O,EAAYxO,EAAMmF,cAClBsJ,EAAY,UAAUC,KAAKzR,EAAO0R,UAAUC,UAAUC,YAAY,CAAC,EAEvE,GAAK3R,EAAS4R,cAAc,aAAa,EAQvCT,EAAYE,EAASrO,QAAQ,WAAW,EAAEY,SAAS,aAAa,EAAE+I,IAAI,CAAC,EACvEyE,EAAYlR,EAAEiR,CAAS,EAAEvN,SAAS,EAAE+I,IAAI,CAAC,MATC,CAE1C,GAAI,EADJwE,EAAYnR,EAAS6R,gBAAgB,6BAA8B,KAAK,GACzD7M,UAAW,OAC1BmM,EAAUnM,UAAU8M,IAAI,YAAY,EACpCV,EAAYpR,EAAS6R,gBAAgB,6BAA6B,MAAM,EACxEV,EAAUY,YAAYX,CAAS,EAC/BC,EAASrO,QAAQ,WAAW,EAAES,OAAO0N,CAAS,CAChD,CAIA,IAAIa,EAAcX,EAASrO,QAAQ,WAAW,EAAEyD,IAAI,WAAW,EAAEM,MAAM,GAAG,EACtEkL,EAAkC,QAAnB3R,EAAK0B,WAA0C,QAAnB1B,EAAK0B,UAChDkQ,EAAQxJ,KAAKK,IAAIhJ,EAAOiJ,WAAWiJ,EAAeD,EAAY,GAAGpC,MAAMoC,EAAY,GAAGhL,QAAQ,GAAG,EAAI,CAAC,EAAIgL,EAAY,EAAE,CAAC,EAUzHG,GATJhB,EAAUiB,aAAa,QAASH,EAAeZ,EAASgB,WAAW,CAAA,CAAK,EAAIhB,EAASiB,YAAY,CAAA,CAAK,CAAC,EACvGnB,EAAUiB,aAAa,SAAUH,EAAeZ,EAASiB,YAAY,CAAA,CAAK,EAAIjB,EAASgB,WAAW,CAAA,CAAK,CAAC,EACxGjB,EAAUgB,aAAa,IAAI,EAAIF,CAAK,EACpCd,EAAUgB,aAAa,IAAI,EAAIF,CAAK,EACpCd,EAAUgB,aAAa,QAAS,IAAMF,CAAK,EAC3Cd,EAAUgB,aAAa,SAAU,GAAKF,CAAK,EAC3Cd,EAAUgB,aAAa,KAAM,EAAIF,CAAK,EACtCd,EAAUgB,aAAa,KAAM,EAAIF,CAAK,EACtCd,EAAUgB,aAAa,eAAgB,CAAIF,CAAK,EAClCZ,EAAUiB,QAAUL,GAC9BM,EAAUlB,EAAUmB,QAAUP,EACX,QAAnB5R,EAAK0B,WACPmQ,EAAUb,EAAUmB,QAAUP,EAC9BM,EAAUlB,EAAUiB,QAAUL,GACF,QAAnB5R,EAAK0B,WACdmQ,EAAUd,EAASgB,WAAW,CAAA,CAAK,EAAIf,EAAUmB,QAAUP,EAC3DM,EAAUlB,EAAUiB,QAAUL,GACF,QAAnB5R,EAAK0B,YACdmQ,EAAUd,EAASgB,WAAW,CAAA,CAAK,EAAIf,EAAUiB,QAAUL,EAC3DM,EAAUnB,EAASiB,YAAY,CAAA,CAAK,EAAIhB,EAAUmB,QAAUP,GAE1DX,GACFH,EAAUgB,aAAa,OAAQ,oBAAoB,EACnDhB,EAAUgB,aAAa,SAAU,gBAAgB,GAC7CM,EAAmB1S,EAAS2S,cAAc,KAAK,GAClCC,IAAM,4BAA6B,IAAKC,eAAiBC,kBAAkB3B,CAAS,EACrGG,EAAUyB,aAAaC,aAAaN,EAAkBP,EAASK,CAAO,GAGlElB,EAAUyB,aAAaC,cACzB1B,EAAUyB,aAAaC,aAAa7B,EAAWgB,EAASK,CAAO,CAErE,EAEAS,cAAe,SAAUzI,GACvB,OAAKA,EAAMhB,GAAG,OAAO,EAGdgB,EAAMjG,QAAQ,YAAY,EAAEtB,OAF1B,CAGX,EAEAiQ,cAAe,SAAU1I,GACvB,OAAKA,EAAMhB,GAAG,OAAO,EAGdgB,EAAMxH,QAAQ,YAAY,EAAEE,KAAK,QAAQ,EAAED,OAAS,EAFlD,CAGX,EAEAkQ,mBAAoB,SAAU/P,GAC5B,GAAG,CAACA,EAAO,MAAO,GAClB,IAAIgQ,EAAQ,GACRC,EAAS,GAEb,IADAD,EAAMxJ,KAAKxG,CAAK,EACVgQ,EAAMnQ,QAAQ,CAElB,IADA,IAAIqQ,EAAM,GACFzO,EAAI,EAAGA,EAAIuO,EAAMnQ,OAAQ4B,CAAC,GAAI,CAClC,IAAI0O,EAAMH,EAAMI,MAAM,EAClB5P,EAAWrD,KAAK4K,YAAYoI,CAAG,EAChC3P,EAASX,QACVmQ,EAAMxJ,KAAKhG,EAAS6P,QAAQ,EAAEC,KAAK,CAAC,EAEtCJ,EAAI1J,KAAK1J,EAAEqT,CAAG,CAAC,CACnB,CACAF,EAAOzJ,KAAK0J,CAAG,CACjB,CACA,OAAOD,CACT,EAEAM,uBAAwB,SAAUC,GAChC,IAAItT,EAAOC,KAAKgC,QAEZsR,EAAeD,EAAS5Q,QAAQ,aAAa,EAAE8Q,SAAS,MAAM,EAC9DC,EAAYH,EAAS5Q,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EACvDuK,EAAYJ,EAAS5Q,QAAQ,YAAY,EAAEE,KAAK,OAAO,EAC3D3C,KAAKmC,OAAOD,KAAK,UAAWmR,CAAQ,EACjC1Q,KAAK,OAAO,EAAEiB,KAAK,SAAUC,EAAOC,GAC9BwP,GAA0C,CAAC,IAA3BG,EAAU5P,MAAMC,CAAI,GACnC/D,EAAK2T,cACH3T,CAAAA,EAAK2T,aAAaL,EAAUG,EAAW7T,EAAEmE,CAAI,CAAC,GAIlDnE,EAAEmE,CAAI,EAAEC,SAAS,aAAa,CAGpC,CAAC,CACL,EAEA4P,iBAAkB,SAAUpR,GAC1BA,EAAMmF,cAAc8K,aAAaoB,QAAQ,YAAa,kBAAkB,EAEnC,SAAjC5T,KAAKmC,OAAO+D,IAAI,WAAW,GAC7BlG,KAAK2Q,gBAAgBpO,CAAK,EAE5BvC,KAAKoT,uBAAuBzT,EAAE4C,EAAMC,MAAM,CAAC,CAC7C,EAEAqR,gBAAiB,SAAUtR,GACpB5C,EAAE4C,EAAMyD,cAAc,EAAEiD,GAAG,cAAc,EAK5C1G,EAAMiD,eAAe,EAJrBjD,EAAMmF,cAAc8K,aAAasB,WAAa,MAMlD,EAEAC,eAAgB,SAAUxR,GACxBvC,KAAKmC,OAAOQ,KAAK,cAAc,EAAEC,YAAY,aAAa,CAC5D,EAEAoR,YAAaC,eAAgB1R,GAC3B,IAgBIiR,EAOAU,EAKEC,EAoBEC,EAKAC,EApDJC,EAAY3U,EAAE4C,EAAMyD,cAAc,EAClCqN,EAAWrT,KAAKmC,OAAOD,KAAK,SAAS,EAGpCmR,EAASE,SAAS,MAAM,EAKxBe,EAAUf,SAAS,aAAa,IAMjCC,EAAYH,EAAS5Q,QAAQ,QAAQ,EAAEyG,SAAS,OAAO,EACvDqL,EAAY5U,EAAEkF,MAAM,mBAAmB,EAC3C7E,KAAKmC,OAAO2C,QAAQyP,EAAW,CAAEC,YAAenB,EAAUoB,SAAYjB,EAAWkB,SAAYJ,CAAU,CAAC,EACpGC,EAAUI,mBAAmB,IAI7BT,EAAalU,KAAKmC,OAAOD,KAAK,SAAS,EAAEA,KAEvC0S,GADFC,EAAS,IAAIC,WAAWZ,EAAYlU,KAAKmC,OAAOD,KAAK,SAAS,EAAElB,OAAQ,UAAU,GAC5D+T,YAAY,CAAEnL,OAAU,CAAA,CAAK,CAAC,EACR,EAA5C5J,KAAKmC,OAAOD,KAAK,SAAS,EAAE8S,eAAqBJ,GAC/CJ,EAAcK,EAAOI,aAAa5B,EAASnR,KAAK,UAAU,EAAEgI,EAAE,EAC9DiK,EAAOe,OAAOC,OAAO,GAAIX,CAAW,EACxCK,EAAOO,WAAWZ,EAAYtK,EAAE,GAC5BmL,EAAWR,EAAOI,aAAaX,EAAUpS,KAAK,UAAU,EAAEgI,EAAE,GACnD7G,SACXgS,EAAShS,SAASgG,KAAK8K,CAAI,EAE3BkB,EAAShS,SAAW,CAAC8Q,GAlCdnU,KAoCJ+B,KAAK,CAAEG,KAAQgS,CAAW,CAAC,IAI3BI,EAAUpL,SAAS,QAAQ,EAAExG,QAQ5B0R,6CAA6DpU,KAAKgC,QAAQ7B,MAAMC,qDAAqDJ,KAAKgC,QAAQ7B,MAAMC,cACvJiT,EAAS1Q,KAAK,iBAAiB,EAAED,QACpC2Q,EAASnQ,OAAOkR,CAAe,EAEjCE,EAAUpL,SAAS,QAAQ,EAAEhG,OAAOmQ,EAAS5Q,QAAQ,YAAY,CAAC,EAEzC,KADrB4R,EAAYhB,EAAS5Q,QAAQ,YAAY,EAAEyG,SAAS,EAAEvG,KAAK,aAAa,GAC9DD,QACZ2R,EAAUnR,OAAOkR,CAAe,IAdlCE,EAAUpR,iDAAiDlD,KAAKgC,QAAQ7B,MAAMC,aAAa,EACxFsF,MAAM,yBAAyB,EAC/BwD,SAAS,QAAQ,EAAEhG,OAAOmQ,EAAS1Q,KAAK,iBAAiB,EAAEP,OAAO,EAAE4K,IAAI,EAAEvK,QAAQ,YAAY,CAAC,EAC9F6R,EAAUjR,SAAS,QAAQ,EAAEX,QAC/B4R,EAAUjR,SAAS,QAAQ,EAAEiS,qBAAqBtV,KAAKgC,QAAQ7B,MAAMC,SAASJ,KAAKmC,OAAOD,KAAK,SAAS,EAAE/B,MAAME,mCAAmC,GAcpF,IAA/DmT,EAAUtK,SAAS,QAAQ,EAAE7F,SAAS,YAAY,EAAEX,OACtD8Q,EAAUtK,SAAS,QAAQ,EAAE7F,SAAS,YAAY,EAAEV,KAAK,aAAa,EACnEA,KAAK,iBAAiB,EAAEP,OAAO,EACsC,IAA/DoR,EAAUtK,SAAS,QAAQ,EAAE7F,SAAS,YAAY,EAAEX,QAC7D8Q,EAAU7Q,KAAK,gCAAgC,EAAEP,OAAO,EACrD4K,IAAI,EAAE9D,SAAS,QAAQ,EAAE9G,OAAO,KA1DrCpC,KAAKmC,OAAOoT,eAAe,CAAEtG,KAAQ,wBAAyBuG,YAAenC,EAAUqB,SAAYJ,CAAU,CAAC,CA6DlH,EAEAmB,kBAAmB,SAAUlT,GACvBvC,KAAK0V,cAGLnT,EAAM0D,SAAkC,EAAvB1D,EAAM0D,QAAQvD,SAGnC1C,KAAK0V,aAAe,CAAA,EACpB1V,KAAK2V,WAAa,CAAA,EAClBpT,EAAMiD,eAAe,EACvB,EAEAoQ,iBAAkB,SAAUrT,GAC1B,IAsBMsT,EAtBD7V,CAAAA,KAAK0V,cAGNnT,EAAM0D,SAAkC,EAAvB1D,EAAM0D,QAAQvD,SAGnCH,EAAMiD,eAAe,EAEhBxF,KAAK2V,aAER3V,KAAKoT,uBAAuBzT,EAAE4C,EAAMuT,aAAa,CAAC,EAElD9V,KAAK+V,eAAiB/V,KAAKgW,gBAAgBzT,EAAOvC,KAAKmC,OAAOD,KAAK,SAAS,EAAE,EAAE,GAElFlC,KAAK2V,WAAa,CAAA,EAGlB3V,KAAKiW,cAAc1T,EAAOvC,KAAK+V,cAAc,EAIjB,GADxBG,EADYvW,EAAEF,EAAS0W,iBAAiB5T,EAAM0D,QAAQ,GAAGoC,QAAS9F,EAAM0D,QAAQ,GAAGqC,OAAO,CAAC,EAChE7F,QAAQ,UAAU,GAC9BC,SACbmT,EAAsBK,EAAe,GACrCA,EAAejN,GAAG,cAAc,GAClCjJ,KAAKoW,gBAAkBP,EAOzB7V,KAAKoW,gBAAkB,KAE3B,EAEAC,gBAAiB,SAAU9T,GACzB,IAeQ+T,EACAC,EAhBHvW,KAAK0V,eAGV1V,KAAKwW,iBAAiB,EAClBxW,KAAK2V,YAED3V,KAAKoW,kBACDK,EAA0B,CAAEzQ,eAAgBhG,KAAKoW,eAAgB,EACrEpW,KAAKgU,YAAYyC,CAAuB,EACxCzW,KAAKoW,gBAAkB,MAE3BpW,KAAK+T,eAAexR,CAAK,IAIrB+T,EAAa/T,EAAMmU,eAAe,IAClCH,EAAsB9W,EAASkX,YAAY,aAAa,GACxCC,eAAe,QAAS,CAAA,EAAM,CAAA,EAAMpX,EAAQ,EAAG8W,EAAWO,QAASP,EAAWQ,QAASR,EAAWjO,QAASiO,EAAWhO,QAAS/F,EAAMwU,QAASxU,EAAMyU,OAAQzU,EAAM0U,SAAU1U,EAAM2U,QAAS,EAAG,IAAI,EACtN3U,EAAMC,OAAO2U,cAAcZ,CAAmB,GAElDvW,KAAK0V,aAAe,CAAA,EACtB,EAEAM,gBAAiB,SAAUzT,EAAO6U,GAChC,IAAIC,EAAYD,EAAOE,UAAU,CAAA,CAAI,EAGjCC,GAFJvX,KAAKwX,UAAUJ,EAAQC,CAAS,EAChCA,EAAU5I,MAAMgJ,IAAMJ,EAAU5I,MAAMiJ,KAAO,UACvBN,EAAOO,sBAAsB,GAC/CC,EAAc5X,KAAK6X,cAActV,CAAK,EAI1C,OAHAvC,KAAK8X,qBAAuB,CAAEC,EAAGH,EAAYG,EAAIR,EAAgBG,KAAMM,EAAGJ,EAAYI,EAAIT,EAAgBE,GAAI,EAC9GJ,EAAU5I,MAAMwJ,QAAU,MAC1BxY,EAASyY,KAAK1G,YAAY6F,CAAS,EAC5BA,CACT,EAEAb,iBAAkB,WACZxW,KAAK+V,gBAAkB/V,KAAK+V,eAAeoC,eAC7CnY,KAAK+V,eAAeoC,cAAcC,YAAYpY,KAAK+V,cAAc,EACnE/V,KAAK8X,qBAAuB,KAC5B9X,KAAK+V,eAAiB,IACxB,EAEAyB,UAAW,SAAUnF,EAAKgG,GAEJ,CAAC,KAAM,QAAS,QAAS,aAC/B3O,QAAQ,SAAU4O,GAC5BD,EAAIE,gBAAgBD,CAAG,CAC3B,CAAC,EAEGjG,aAAemG,qBACDC,EAAOJ,GAClBK,OADDC,EAAOtG,GACOqG,MAClBD,EAAKG,OAASD,EAAKC,OACnBH,EAAKI,WAAW,IAAI,EAAEC,UAAUH,EAAM,EAAG,CAAC,GAI5C,IAbA,IAMMA,EAAYF,EAMdM,EAAKC,iBAAiB3G,CAAG,EACpB/N,EAAI,EAAGA,EAAIyU,EAAGrW,OAAQ4B,CAAC,GAAI,CAClC,IAAI6E,EAAM4P,EAAGzU,GACT6E,EAAI1C,QAAQ,YAAY,EAAI,IAC9B4R,EAAI5J,MAAMtF,GAAO4P,EAAG5P,GAExB,CACAkP,EAAI5J,MAAMwK,cAAgB,OAE1B,IAAS3U,EAAI,EAAGA,EAAI+N,EAAIhP,SAASX,OAAQ4B,CAAC,GACxCtE,KAAKwX,UAAUnF,EAAIhP,SAASiB,GAAI+T,EAAIhV,SAASiB,EAAE,CAEnD,EAEAuT,cAAe,SAAUtV,GAIvB,MAAO,CACLwV,GAHAxV,EADEA,GAASA,EAAM0D,QACT1D,EAAM0D,QAAQ,GAGnB1D,GAAM8F,QACT2P,EAAGzV,EAAM+F,OACX,CACF,EAEA2N,cAAe,SAAU1T,EAAO2W,GAC9B,IAEIC,EAFC5W,GAAU2W,IAEXC,EAAiBnZ,KACrBoZ,sBAAsB,WACpB,IAAIC,EAAKF,EAAetB,cAActV,CAAK,EACvC+W,EAAIJ,EAAMzK,MACd6K,EAAEC,SAAW,WACbD,EAAEL,cAAgB,OAClBK,EAAEE,OAAS,SACPL,EAAerB,uBACfwB,EAAE5B,KAAOvP,KAAKsR,MAAMJ,EAAGtB,EAAIoB,EAAerB,qBAAqBC,CAAC,EAAI,KACpEuB,EAAE7B,IAAMtP,KAAKsR,MAAMJ,EAAGrB,EAAImB,EAAerB,qBAAqBE,CAAC,EAAI,KAEzE,CAAC,EACH,EAEA0B,aAAc,SAAUzP,GACtBA,EAAMlD,GAAG,YAAa/G,KAAK2T,iBAAiBnI,KAAKxL,IAAI,CAAC,EACnD+G,GAAG,WAAY/G,KAAK6T,gBAAgBrI,KAAKxL,IAAI,CAAC,EAC9C+G,GAAG,UAAW/G,KAAK+T,eAAevI,KAAKxL,IAAI,CAAC,EAC5C+G,GAAG,OAAQ/G,KAAKgU,YAAYxI,KAAKxL,IAAI,CAAC,EACtC+G,GAAG,aAAc/G,KAAKyV,kBAAkBjK,KAAKxL,IAAI,CAAC,EAClD+G,GAAG,YAAa/G,KAAK4V,iBAAiBpK,KAAKxL,IAAI,CAAC,EAChD+G,GAAG,WAAY/G,KAAKqW,gBAAgB7K,KAAKxL,IAAI,CAAC,CACnD,EAEA2Z,WAAY,SAAUzX,GACpB,IACInC,EAAOC,KAAKgC,QACZ4X,EAAQ1X,EAAK0X,MAOb9I,GANA5O,EAAKmB,UAAYnB,EAAKnC,EAAKiB,SAC7BrB,EAAEiE,KAAK1B,EAAKmB,SAAU,SAAUQ,EAAOgW,GACrCA,EAAMzK,SAAWlN,EAAKnC,EAAKiB,OAC7B,CAAC,EAGYrB,EAAE,QAAUI,EAAKyB,UAAY,oBAAsB,KAAOU,EAAKnC,EAAKiB,QAAU,QAAUkB,EAAKnC,EAAKiB,QAAU,IAAM,KAAOkB,EAAKkN,SAAW,iBAAmBlN,EAAKkN,SAAW,IAAM,IAAM,GAAG,EACvMrL,SAAS,SACP7B,EAAK4X,WAAa,KAClB5X,GAAM6X,SAAW,WAAa,KAC7BH,EAAQ7Z,EAAKmB,aAAe,YAAc,GAAG,GAQ/C8Y,GAPAja,EAAKka,aACPnJ,EAAS5N,OAAOnD,EAAKka,aAAa/X,CAAI,CAAC,EAEvC4O,EAAS5N,OAAO,sBAAwBhB,EAAKnC,EAAKgB,WAAa,QAAQ,EACpEmC,OAAmC,KAAA,IAArBnD,EAAKma,YAA8B,yBAA2BhY,EAAKnC,EAAKma,cAAgB,IAAM,SAAW,EAAE,EAG/Gva,EAAEsC,OAAO,GAAIC,CAAI,GAI5BoH,GAHJ,OAAO0Q,EAAS3W,SAChByN,EAAS5O,KAAK,WAAY8X,CAAQ,EAEtB9X,EAAKoB,cAAgB,IAuDjC,OAtDKvD,EAAKiV,eAAiB4E,GAAS7Z,EAAKiV,eAAkB9S,EAAK2H,SAC1DsQ,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GAC1BtJ,EAAS5N,8BAA8BnD,EAAKI,MAAMC,aAAa,EAC5DiD,SAAS,QAAQ,EAAEiS,qBAAqBvV,EAAKI,MAAMC,SAASL,EAAKI,MAAME,mCAAmC,EAEtG6B,EAAK0H,OACVuQ,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GAC1BtJ,EAAS5N,iDAAiDnD,EAAKI,MAAMC,aAAa,EAC/EiD,SAAS,QAAQ,EAAEiS,qBAAqBvV,EAAKI,MAAMC,SAASL,EAAKI,MAAME,mCAAmC,EAEtG6B,EAAKqH,SACduH,EAAS5K,IAAI,kCAAmCiC,KAAKkS,MAAMlS,KAAKC,KAAKlG,EAAKmB,SAASX,OAAS,CAAC,CAAC,UAAU,EACpGyX,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GAC1BtJ,EAAS5N;wBACKnD,EAAKI,MAAMC,SAASL,EAAKI,MAAMO;wBAC/BX,EAAKI,MAAMC,SAASL,EAAKI,MAAMQ;aAC1C,EACA0C,SAAS,QAAQ,EAAEiS,qBAAqBvV,EAAKI,MAAMC,SAASL,EAAKI,MAAME,mCAAmC,GAEtG6B,EAAK4H,oBAGVqQ,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GAC1BtJ,EAAS5N,8CAA8CnD,EAAKI,MAAMC,aAAa,EAE9E+Z,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GACzBtJ,EAAS5N,kDAAkDnD,EAAKI,MAAMC,qDAAqDL,EAAKI,MAAMC,aAAa,EAElJ+Z,OAAO7Q,EAAM8Q,OAAO,EAAE,CAAC,CAAC,GACzBtJ,EAAS5N,iDAAiDnD,EAAKI,MAAMC,aAAa,EAC/EiD,SAAS,QAAQ,EAAEiS,qBAAqBvV,EAAKI,MAAMC,SAASL,EAAKI,MAAME,mCAAmC,GAIjHyQ,EAAS/J,GAAG,wBAAyB/G,KAAK2O,sBAAsBnD,KAAKxL,IAAI,CAAC,EAC1E8Q,EAAS/J,GAAG,QAAS/G,KAAKkP,iBAAiB1D,KAAKxL,IAAI,CAAC,EACrD8Q,EAAS/J,GAAG,QAAS,WAAY/G,KAAK4P,oBAAoBpE,KAAKxL,IAAI,CAAC,EACpE8Q,EAAS/J,GAAG,QAAS,cAAe/G,KAAK8P,uBAAuBtE,KAAKxL,IAAI,CAAC,EAC1E8Q,EAAS/J,GAAG,QAAS,wBAAyB/G,KAAKiQ,kBAAkBzE,KAAKxL,IAAI,CAAC,EAC/E8Q,EAAS/J,GAAG,QAAS,aAAc/G,KAAKyQ,aAAajF,KAAKxL,IAAI,CAAC,EAC/D8Q,EAAS/J,GAAG,QAAS,yBAAyB/G,KAAKoQ,qBAAqB5E,KAAKxL,IAAI,CAAC,EAClF8Q,EAAS/J,GAAG,QAAS,uBAAuB/G,KAAKqQ,mBAAmB7E,KAAKxL,IAAI,CAAC,EAE1ED,EAAKyB,YACPxB,KAAK0Z,aAAa5I,CAAQ,EAC1B9Q,KAAK0V,aAAe,CAAA,EACpB1V,KAAK2V,WAAa,CAAA,EAClB3V,KAAKoW,gBAAkB,MAGrBrW,EAAK4Z,YACP5Z,EAAK4Z,WAAW7I,EAAU5O,CAAI,EAGzB4O,CACT,EAEAwJ,mBAAoB,SAAUC,EAAYzJ,EAAU5O,EAAM0X,GACxD,IAGIY,EAHAvW,EAAOjE,KACPD,EAAOC,KAAKgC,QACZyY,EAAWb,EAAQ,EAAI7Z,EAAKmB,cAAiBgB,EAAKtB,YAAchB,GAAasC,EAAKtB,UAEjFb,EAAKiV,eAAkB4E,EAAQ,GAAM7Z,EAAKiV,eAAkB9S,EAAK0H,QACpE4Q,EAAc7a,EAAE,oBAAoB,EAChC8a,GAAa1a,EAAKiV,eAAkB4E,EAAQ,GAAM7Z,EAAKiV,eACzDwF,EAAYzW,SAAS,QAAQ,GAEzBhE,EAAKiV,eAAiB4E,EAAQ,IAAM7Z,EAAKiV,eAAkB9S,EAAK0H,SACjE,CAAC2Q,EAAW9X,QAAQ,WAAW,EAAEC,QAClC8X,EAAYzW,SAAS,UAAU,EAEnCwW,EAAWrX,OAAOsX,CAAW,GACpBtY,EAAKqH,QACduH,EAAS/M,SAAS,SAAS,GAE3ByW,EAAc7a,EAAE,oBAAsB8a,EAAW,UAAY,IAAM,IAAI,EACnEA,GACFF,EAAWxW,SAAS,qBAAqB,EAE3CwW,EAAWrX,OAAOsX,CAAW,GAG3BxX,MAAMC,QAAQf,EAAKmB,SAAS,EAAE,GAChC1D,EAAEiE,KAAK1B,EAAKmB,SAAU,WACpBrD,KAAK4Z,MAAQA,EAAQ,CACvB,CAAC,EACD5Z,KAAKmD,eAAeqX,EAAatY,EAAKmB,QAAQ,GAE9C1D,EAAEiE,KAAK1B,EAAKmB,SAAU,WAEpB,IAGMqX,EAJN1a,KAAK4Z,MAAQA,EAAQ,EACjB1X,EAAKqH,QACPtF,EAAKd,eAAe2N,EAAU9Q,IAAI,GAE9B0a,EAAY/a,EAAE,wBAAwB,EAC1C6a,EAAYtX,OAAOwX,CAAS,EAC5BzW,EAAKd,eAAeuX,EAAW1a,IAAI,EAEvC,CAAC,CAEL,EAEAmD,eAAgB,SAAUoX,EAAYrY,GACpC,IAGI4O,EAHA7M,EAAOjE,KAEP4Z,GADO5Z,KAAKgC,QACJ,GAERE,EAAK0X,OAAS1X,EAAK,IAAI0X,MACzBA,EAAQ1X,EAAK0X,OAEbA,EAAQW,EAAWI,aAAa,YAAa,QAAQ,EAAEjY,OACnDM,MAAMC,QAAQf,CAAI,GAAKc,MAAMC,QAAQf,EAAK,EAAE,EAC9CvC,EAAEiE,KAAK1B,EAAM,WACXvC,EAAEiE,KAAK5D,KAAM,WACXA,KAAK4Z,MAAQA,CACf,CAAC,CACH,CAAC,EAED1X,EAAK0X,MAAQA,GAIb5W,MAAMC,QAAQf,CAAI,GAAKc,MAAMC,QAAQf,EAAK,EAAE,EAC9CvC,EAAEiE,KAAK1B,EAAM,WACX,IAAI0Y,EAAQ5a,KACZL,EAAEiE,KAAK5D,KAAM,SAAUsE,GAGrB,IAOMuW,EATN/J,EAAW7M,EAAK0V,WAAW3Z,IAAI,EAEV,IAAjB4a,EAAMlY,QAAsB,IAAN4B,GACxBiW,EAAW5X,KAAK,IAAIiY,EAAM,GAAG1Q,EAAI,EAAExE,MAAMoL,CAAQ,EAC7C9Q,KAAKqD,UAAYrD,KAAKqD,SAASX,QAAU1C,KAAKqD,SAAS,GAAGX,QAC5DuB,EAAKqW,mBAAmBC,EAAW5X,KAAK,IAAIiY,EAAM,GAAG1Q,EAAI,EAAElB,OAAO,EAAG8H,EAAU9Q,KAAM4Z,CAAK,IAIxFiB,EAAWlb,yBAAwC,EAAfib,EAAMlY,OAAa,UAAY,KAAsB,IAAjBkY,EAAMlY,OAAe,UAAY,KAAyB,CAAA,GAApB,CAAC,CAAC1C,KAAK+Z,UAAqC,EAAfa,EAAMlY,OAAc,WAAa,WAAW,EAGjL,IAAN4B,EACFuW,EAAS3U,IAAI,CAAC4U,aAAc,MAAOC,mBAAoB,KAAK,CAAC,EAChD,EAAJzW,GAASA,EAAIsW,EAAMlY,OAAS,EACrCmY,EAAS3U,IAAI,CAAC4U,aAAc,OAAQC,mBAAoB,KAAK,CAAC,EAE9DF,EAAS3U,IAAI,CAAC4U,aAAc,MAAOC,mBAAoB,KAAK,CAAC,EAG/DF,EAAS3X,OAAO4N,CAAQ,EACxByJ,EAAWrX,OAAO2X,CAAQ,EACtB7a,KAAKqD,UAAYrD,KAAKqD,SAASX,QAAU1C,KAAKqD,SAAS,GAAGX,QAC5DuB,EAAKqW,mBAAmBO,EAAU/J,EAAU9Q,KAAM4Z,CAAK,EAG7D,CAAC,CACH,CAAC,GAE8B,EAA3B1E,OAAO8F,KAAK9Y,CAAI,EAAEQ,SACpBoO,EAAW9Q,KAAK2Z,WAAWzX,CAAI,EAC/BqY,EAAWrX,OAAO4N,CAAQ,GAExB5O,EAAKmB,UAAYnB,EAAKmB,SAASX,QACjC1C,KAAKsa,mBAAmBC,EAAYzJ,EAAU5O,EAAM0X,CAAK,EAG/D,EAEAqB,eAAgB,SAAUC,EAAWhZ,GACnClC,KAAKmD,eAAe+X,EAAW,CAAE7X,SAAYnB,CAAK,CAAC,CACrD,EAEAiZ,YAAa,SAAUlR,EAAO/H,GAC5BlC,KAAKib,eAAehR,EAAMxH,QAAQ,YAAY,EAAGP,CAAI,EAChD+H,EAAMtH,KAAK,mBAAmB,EAAED,QACnCuH,EAAM5G,SAAS,QAAQ,EAAEiS,qBAAqBtV,KAAKgC,QAAQ7B,MAAMC,SAASJ,KAAKgC,QAAQ7B,MAAME,mCAAmC,EAE9H4J,EAAMxH,QAAQ,iBAAiB,EAAEC,OAC9BuH,EAAM5G,SAAS,YAAY,EAAEX,QAChCuH,EAAM/G,8BAA8BlD,KAAKgC,QAAQ7B,MAAMC,aAAa,EAGjE6J,EAAM5G,SAAS,aAAa,EAAEX,QACjCuH,EAAM/G,iDAAiDlD,KAAKgC,QAAQ7B,MAAMC,aAAa,EAGvFJ,KAAKoL,WAAWnB,CAAK,GACvBjK,KAAKqL,oBAAoBpB,EAAM5G,SAAS,aAAa,CAAC,CAE1D,EAEA+X,gBAAiB,SAAUC,EAAcnZ,GACvCA,EAAKoB,aAAepB,EAAKoB,cAAgB,MACrCgY,EAAkB3b,EAAE,oDAAoD,EACzEgD,KAAK,YAAY,EAAEO,OAAOlD,KAAK2Z,WAAWzX,CAAI,CAAC,EAAE8K,IAAI,EACxDhN,KAAKmC,OAAOmT,QAAQgG,CAAe,EAChC3Y,KAAK,kBAAkB,EAAEO,OAAOmY,EAAa5Y,QAAQ,IAAI,EAAEsB,SAAS,OAAO,CAAC,CACjF,EAEAwX,UAAW,SAAUF,EAAcnZ,GACjClC,KAAKob,gBAAgBC,EAAcnZ,CAAI,EAClCmZ,EAAahY,SAAS,UAAU,EAAEX,QACrC2Y,EAAahY,SAAS,QAAQ,EAAEqC,6CAA6C1F,KAAKgC,QAAQ7B,MAAMC,aAAa,EAE3GJ,KAAKoL,WAAWiQ,CAAY,GAC9Brb,KAAKqL,oBAAoBgQ,EAAahY,SAAS,UAAU,CAAC,CAE9D,EAEAmY,iBAAkB,SAAUC,EAAYvZ,GACtC,IAOM2K,EAPF6O,GAAkB/b,EAAEsD,QAAQf,CAAI,EAAIA,EAAcA,EAAKmB,UAAdX,OACzCiZ,EAAsBF,EAAWzS,OAAO,EAAEC,GAAG,QAAQ,EAAIwS,EAAWvS,SAAS,EAAExG,OAAS,EAAI,EAC5FkZ,EAAeD,EAAsBD,EACrCG,EAAgC,EAAfD,EAAoBzT,KAAKkS,MAAMuB,EAAa,EAAI,CAAC,EAAI,EAEtEH,EAAWhZ,QAAQ,QAAQ,EAAEuG,OAAO,EAAEC,GAAG,YAAY,GACvDjJ,KAAKib,eAAeQ,EAAWzS,OAAO,EAAEvG,QAAQ,YAAY,EAAGP,CAAI,EAC/D2K,EAAY4O,EAAWzS,OAAO,EAAEvG,QAAQ,YAAY,EAAEY,SAAS,aAAa,EAAEA,SAAS,YAAY,EAC7E,EAAtBsY,EACF9O,EAAU/D,GAAG,CAAC,EAAEgT,OAAOL,EAAWvS,SAAS,EAAE6S,QAAQ,EAAEC,OAAO,CAAC,EAE/DnP,EAAU/D,GAAG+S,CAAa,EAAEnW,MAAM+V,EAAWO,OAAO,CAAC,IAGvDhc,KAAKmD,eAAesY,EAAWzS,OAAO,EAAEsM,QAAQ3V,EAAE,wBAAwB,CAAC,EAAE0D,SAAS,kBAAkB,EAAGnB,CAAI,EAC/GuZ,EAAW3O,QAAQ,YAAY,EAAEzJ,SAAS,QAAQ,EAAEA,SAAS,EAAEyF,GAAG+S,CAAa,EAAEnW,MAAM+V,CAAU,EAErG,EAEAQ,YAAa,SAAUhS,EAAO/H,GAC5BlC,KAAKwb,iBAAiBvR,EAAMxH,QAAQ,YAAY,EAAGP,CAAI,EACvD+H,EAAMxH,QAAQ,QAAQ,EAAEP,KAAK,iBAAkB,CAAA,CAAI,EAC9C+H,EAAM5G,SAAS,WAAW,EAAEX,QAC/BuH,EAAM5G,SAAS,UAAU,EAAEqC,iDAAiD1F,KAAKgC,QAAQ7B,MAAMC,qDAAqDJ,KAAKgC,QAAQ7B,MAAMC,aAAa,EAElLJ,KAAKoL,WAAWnB,CAAK,IACvBjK,KAAKiN,sBAAsBhD,CAAK,EAChCA,EAAM5G,SAAS,UAAU,EAAET,YAAY5C,KAAKgC,QAAQ7B,MAAMG,UAAU,EAAEyD,SAAS/D,KAAKgC,QAAQ7B,MAAMI,cAAc,EAEpH,EAEA2b,YAAa,SAAUjS,GACrB,IAAI4Q,EAAW5Q,EAAMxH,QAAQ,YAAY,EAAEuG,OAAO,EAC9C6R,EAAS7R,OAAO,EAAEC,GAAG,YAAY,EAC/BjJ,KAAKqK,aAAaJ,EAAO,UAAU,EAAEO,OACvCP,EAAMxH,QAAQ,YAAY,EAAEL,OAAO,EACA,IAA/ByY,EAASxX,SAAS,EAAEX,QACtBmY,EAASlY,KAAK,6BAA6B,EAAEP,OAAO,GAGtDyY,EAAS3R,SAAS,OAAO,EAAEvG,KAAK,aAAa,EAAEP,OAAO,EACnD4K,IAAI,EAAEA,IAAI,EAAE5K,OAAO,EAGxByY,EAASpY,QAAQ,WAAW,EAAEL,OAAO,CAEzC,EAEA+Z,cAAe,WAEKnc,KACNmC,OAAOQ,KAAK,cAAc,EACnCC,YAAY,aAAa,CAC9B,EAEAwZ,cAAe,SAAUC,GAGLrc,KACNmC,OAAOQ,KAAK,OAAO,EAC5BiB,KAAK,SAAUC,EAAOC,GACrBnE,EAAEmE,CAAI,EAAEC,SAAS,aAAa,CAChC,CAAC,EAJe/D,KAKNmC,OAAOD,KAAK,UAAWvC,EAAE0c,CAAO,CAAC,CAC/C,EAEAC,oBAAqB,SAAU5H,EAAU2H,GAEnCA,GACFrc,KAAKmC,OAAOD,KAAK,UAAWvC,EAAE0c,CAAO,CAAC,EAEpB3H,EAASjS,QAAQ,OAAO,EAG9B8S,eAAe,CAAEtG,KAAQ,MAAO,CAAC,CACjD,EAEAsN,UAAW,SAASC,EAAQlb,GAC1B,IAAImb,EAAM,GACNC,EAAWvU,KAAKkS,MAAMmC,EAAO9D,KAAK,EAClCiE,EAAYxU,KAAKkS,MAAMmC,EAAO5D,MAAM,EACnCpZ,EAAOod,QACVpd,EAAOod,MAAQpd,EAAOqd,MAAMD,QAI5BH,EADaE,EAAXD,EACI,IAAIE,MAAM,CACdE,YAAa,YACbC,KAAM,KACNC,OAAQ,CAACN,EAAUC,EACrB,CAAC,EAEK,IAAIC,MAAM,CACdE,YAAa,WACbC,KAAM,KACNC,OAAQ,CAACL,EAAWD,EACtB,CAAC,GAECO,SAAST,EAAOU,UAAU,EAAG,MAAO,EAAG,CAAC,EAC5CT,EAAIU,KAAK7b,EAAiB,MAAM,CAClC,EAEA8b,UAAW,SAASZ,EAAQlb,GAC1B,IACI+b,EAAW,qBAAsB5d,EAAS6d,gBAAgB7O,MAC1D8O,EAAO,CAAC,CAAC/d,EAAOge,QAChBC,EAA+B,gCAAtBvM,UAAUwM,SAAoE,aAAtBxM,UAAUwM,SAAiE,CAAC,EAAxCxM,UAAUyM,WAAWlX,QAAQ,MAAM,EACxIxG,EAAkBD,KAAKC,gBAEtB,CAACod,GAAY,CAACE,GAASE,EAC1Bje,EAAO0R,UAAU0M,WAAWpB,EAAOqB,SAAS,EAAGvc,EAAiB,MAAM,GAElEwc,EAAW,iBAA+C,KATrD9d,KAS8BgC,QAAQb,WAAoB,IAT1DnB,KASqEgC,QAAQb,WAAa,IAE9FlB,EAAgB0C,KAAKmb,CAAQ,EAAEpb,QAClCzC,EAAgBiD,OAAO,0BAAwD,KAZxElD,KAYiDgC,QAAQb,WAAoB,IAZ7EnB,KAYwFgC,QAAQb,WAAa,IAC3F,eAAgBG,EAAiB,YAAY,EAGxErB,EAAgB0C,KAAKmb,CAAQ,EAAEhQ,KAAK,OAAQ0O,EAAOU,UAAU,CAAC,EAAE,GAAG5a,MAAM,EAE7E,EAEAmD,OAAQ,SAAUnE,EAAgBC,GAChC,IAAI0C,EAAOjE,KAGX,GAFAsB,EAA4C,KAAA,IAAnBA,EAAmCA,EAAiBtB,KAAKgC,QAAQV,eAC1FC,EAAsD,KAAA,IAAxBA,EAAwCA,EAAsBvB,KAAKgC,QAAQT,oBACrG5B,EAAEK,IAAI,EAAEqD,SAAS,UAAU,EAAEX,OAC/B,MAAO,CAAA,EAET,IAAIzC,EAAkBD,KAAKC,gBACvB8d,EAAQ9d,EAAgB0C,KAAK,OAAO,EAMpCqb,GALCD,EAAMrb,OAGTqb,EAAMnb,YAAY,QAAQ,EAF1B3C,EAAgBiD,sCAAsClD,KAAKgC,QAAQ7B,MAAMC,SAASJ,KAAKgC,QAAQ7B,MAAMW,6BAA6B,EAIlHb,EAAgB8D,SAAS,iBAAiB,EAAEpB,KAAK,0BAA0B,EAAEyJ,IAAI,CAAC,GAChG2C,EAAkC,QAA3B9K,EAAKjC,QAAQP,WAAkD,QAA3BwC,EAAKjC,QAAQP,UAC5Dwc,YAAYD,EAAa,CACvBtF,MAAS3J,EAAOiP,EAAYE,aAAeF,EAAYG,YACvDvF,OAAU7J,EAAOiP,EAAYG,YAAcH,EAAYE,aACvDE,QAAW,SAAUC,GACnB1e,EAAE0e,CAAQ,EAAE1b,KAAK,kBAAkB,EAAEuD,IAAI,WAAY,SAAS,EAC3DvD,KAAK,gCAAgC,EAAEuD,IAAI,YAAa,EAAE,CAC/D,CACF,CAAC,EACAoY,KAAK,SAAU9B,GACdvc,EAAgB0C,KAAK,OAAO,EAAEoB,SAAS,QAAQ,EAEL,QAAtCxC,EAAoB6P,YAAY,EAClCnN,EAAKsY,UAAUC,EAAQlb,CAAc,EAErC2C,EAAKmZ,UAAUZ,EAAQlb,CAAc,EAGvCrB,EAAgB2C,YAAY,iBAAiB,CAC/C,EAAG,WACD3C,EAAgB2C,YAAY,iBAAiB,CAC/C,CAAC,CACH,CACF,EAEAjD,EAAE4e,GAAGC,SAAW,SAAUze,GACxB,OAAO,IAAIF,EAASG,KAAMD,CAAI,EAAEgC,KAAK,CACvC,CAEF,CAAE"}