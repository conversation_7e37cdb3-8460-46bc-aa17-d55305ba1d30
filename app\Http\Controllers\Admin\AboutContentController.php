<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AboutContent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AboutContentController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $pageKey = $request->query('page_key', AboutContent::PAGE_UDDESHYA);
        $contents = AboutContent::where('page_key', $pageKey)
                        ->orderBy('display_order')
                        ->get();
        
        return view('admin.about-content.index', [
            'contents' => $contents,
            'pageKey' => $pageKey,
            'pageOptions' => AboutContent::getPageOptions()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.about-content.create', [
            'pageOptions' => AboutContent::getPageOptions()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'page_key' => 'required|string',
                'section_key' => 'nullable|string|max:100',
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'image' => 'nullable|image|max:2048', // max 2MB
                'display_order' => 'nullable|integer',
            ]);

            // Additional validation for content to ensure it's not just empty HTML
            $textContent = strip_tags($validated['content']);
            if (empty(trim($textContent))) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['content' => 'Content cannot be empty.']);
            }

            $data = $request->except('image');
            
            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;
            
            // Handle image upload if provided
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                $path = $request->file('image')->store('public/about');
                $data['image_path'] = Storage::url($path);
            }
            
            AboutContent::create($data);
            
            return redirect()
                ->route('admin.about-content.index', ['page_key' => $request->page_key])
                ->with('success', 'Content created successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create content: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $content = AboutContent::findOrFail($id);
        return view('admin.about-content.show', compact('content'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $content = AboutContent::findOrFail($id);
        
        return view('admin.about-content.edit', [
            'content' => $content,
            'pageOptions' => AboutContent::getPageOptions()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $content = AboutContent::findOrFail($id);
            
            $validated = $request->validate([
                'page_key' => 'required|string',
                'section_key' => 'nullable|string|max:100',
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'image' => 'nullable|image|max:2048', // max 2MB
                'display_order' => 'nullable|integer',
            ]);

            // Additional validation for content to ensure it's not just empty HTML
            $textContent = strip_tags($validated['content']);
            if (empty(trim($textContent))) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['content' => 'Content cannot be empty.']);
            }

            $data = $request->except('image');
            
            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;
            
            // Handle image upload if provided
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                // Delete old image if exists
                if ($content->image_path && Storage::exists(str_replace('/storage', 'public', $content->image_path))) {
                    Storage::delete(str_replace('/storage', 'public', $content->image_path));
                }
                
                $path = $request->file('image')->store('public/about');
                $data['image_path'] = Storage::url($path);
            }
            
            $content->update($data);
            
            return redirect()
                ->route('admin.about-content.index', ['page_key' => $content->page_key])
                ->with('success', 'Content updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update content: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $content = AboutContent::findOrFail($id);
        $pageKey = $content->page_key;
        
        // Delete associated image if exists
        if ($content->image_path && Storage::exists(str_replace('/storage', 'public', $content->image_path))) {
            Storage::delete(str_replace('/storage', 'public', $content->image_path));
        }
        
        $content->delete();
        
        return redirect()
            ->route('admin.about-content.index', ['page_key' => $pageKey])
            ->with('success', 'Content deleted successfully');
    }
}
