<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PramukhPadadhikari extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'state_name',
        'division_code',
        'district_lgd_code',
        'vikaskhand_lgd_code',
        'naam',
        'sangathan_me_padnaam',
        'vibhagiy_padnaam',
        'vibhag_ka_naam',
        'vartaman_pata',
        'isthayi_pata',
        'sanchipt_vishesh',
        'sadasyata_kramank_evam_prakar',
        'mobile_number',
        'photo',
        'remark',
        'active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'active' => 'boolean',
    ];

    /**
     * Scope to get only active pramukh padadhikaris.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get inactive pramukh padadhikaris.
     */
    public function scopeInactive($query)
    {
        return $query->where('active', false);
    }

    /**
     * Get formatted mobile number.
     */
    public function getFormattedMobileAttribute()
    {
        $mobile = $this->mobile_number;
        if (strlen($mobile) === 10) {
            return '+91 ' . substr($mobile, 0, 5) . ' ' . substr($mobile, 5);
        }
        return $mobile;
    }

    /**
     * Get the status text.
     */
    public function getStatusTextAttribute()
    {
        return $this->active ? 'Active' : 'Inactive';
    }

    /**
     * Delete the photo file from storage.
     */
    public function deletePhotoFile()
    {
        if ($this->photo && \Storage::disk('public')->exists($this->photo)) {
            \Storage::disk('public')->delete($this->photo);
        }
    }

    /**
     * Get the photo URL.
     */
    public function getPhotoUrlAttribute()
    {
        return $this->photo ? asset('storage/' . $this->photo) : null;
    }

    /**
     * Get the division that this pramukh padadhikari belongs to.
     */
    public function division()
    {
        return $this->belongsTo(DivisionMaster::class, 'division_code', 'division_code');
    }

    /**
     * Get the district that this pramukh padadhikari belongs to.
     */
    public function district()
    {
        return $this->belongsTo(DistrictMaster::class, 'district_lgd_code', 'district_lgd_code');
    }

    /**
     * Get the vikaskhand that this pramukh padadhikari belongs to.
     */
    public function vikaskhand()
    {
        return $this->belongsTo(VikaskhandMaster::class, 'vikaskhand_lgd_code', 'sub_district_lgd_code');
    }
}
