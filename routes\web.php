<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\AboutContentController;
use App\Http\Controllers\Admin\ActivitiesController as AdminActivitiesController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\Admin\MembershipController as AdminMembershipController;
use App\Http\Controllers\Admin\MembershipVargMasterController;
use App\Http\Controllers\Admin\EducationQualificationController;
use App\Http\Controllers\Admin\OfficeMasterController;
use App\Http\Controllers\Admin\DepartmentMasterController;
use App\Http\Controllers\Admin\YadavVargController;
use App\Http\Controllers\Admin\DivisionMasterController;
use App\Http\Controllers\Admin\DistrictMasterController;
use App\Http\Controllers\Admin\VikaskhandMasterController;
use App\Http\Controllers\Member\AuthController as MemberAuthController;
use App\Http\Controllers\Member\DashboardController as MemberDashboardController;
use App\Http\Controllers\HumareBareController;
use App\Http\Controllers\ActivitiesController;
use App\Http\Controllers\MembershipController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\InterestRequestController;
use App\Http\Controllers\VaivahikAuth\AuthController as VaivahikAuthController;
use App\Http\Controllers\VaivahikAuth\DashboardController as VaivahikDashboardController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ContactController;

Route::get('/', [HomeController::class, 'index'])->name('home');

// Admin Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest routes (no auth required)
    Route::middleware('guest')->group(function () {
        Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
    });

    // Auth routes (admin auth required)
    Route::middleware('admin.auth')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

        // About Content Management
        Route::resource('about-content', AboutContentController::class);

        // Activities Management
        Route::resource('activities', AdminActivitiesController::class);

        // News Management
        Route::resource('news', AdminNewsController::class);

        // Membership Management
        Route::prefix('memberships')->name('memberships.')->group(function () {
            Route::get('/', [AdminMembershipController::class, 'index'])->name('index');
            Route::get('/{id}', [AdminMembershipController::class, 'show'])->name('show');
            Route::patch('/{id}/approve', [AdminMembershipController::class, 'approve'])->name('approve');
            Route::patch('/{id}/reject', [AdminMembershipController::class, 'reject'])->name('reject');
            Route::patch('/{id}/reset', [AdminMembershipController::class, 'reset'])->name('reset');
            Route::patch('/{id}/update-login', [AdminMembershipController::class, 'updateMemberLogin'])->name('update-login');
            Route::delete('/{id}', [AdminMembershipController::class, 'destroy'])->name('destroy');

            // Bulk actions
            Route::patch('/bulk-approve', [AdminMembershipController::class, 'bulkApprove'])->name('bulk-approve');
            Route::patch('/bulk-reject', [AdminMembershipController::class, 'bulkReject'])->name('bulk-reject');
        });

        // Ekadash Sadasyata Management
        Route::prefix('ekadash-sadasyata')->name('ekadash-sadasyata.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'show'])->name('show');
            Route::patch('/{id}/approve', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'approve'])->name('approve');
            Route::patch('/{id}/reject', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'reject'])->name('reject');
            Route::patch('/{id}/reset', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'reset'])->name('reset');
            Route::delete('/{id}', [App\Http\Controllers\Admin\EkadashSadasyataController::class, 'destroy'])->name('destroy');
        });

        // Vaivahik Users Management
        Route::prefix('vaivahik-users')->name('vaivahik-users.')->group(function () {
            Route::get('/', [AdminMembershipController::class, 'vaivahikUsers'])->name('index');
            Route::get('/{id}', [AdminMembershipController::class, 'showVaivahikUser'])->name('show');
            Route::patch('/{id}/approve', [AdminMembershipController::class, 'approveVaivahikUser'])->name('approve');
            Route::patch('/{id}/reject', [AdminMembershipController::class, 'rejectVaivahikUser'])->name('reject');
            Route::patch('/{id}/update-login', [AdminMembershipController::class, 'updateVaivahikLogin'])->name('update-login');
            Route::delete('/{id}', [AdminMembershipController::class, 'destroyVaivahikUser'])->name('destroy');
        });

        // Pramukh Padadhikari Routes
        Route::prefix('pramukh-padadhikaris')->name('pramukh-padadhikaris.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'store'])->name('store');
            Route::get('/{pramukhPadadhikari}', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'show'])->name('show');
            Route::get('/{pramukhPadadhikari}/edit', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'edit'])->name('edit');
            Route::put('/{pramukhPadadhikari}', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'update'])->name('update');
            Route::delete('/{pramukhPadadhikari}', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'destroy'])->name('destroy');
            Route::patch('/{pramukhPadadhikari}/toggle-status', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'toggleStatus'])->name('toggle-status');

            // Location-wise listing routes
            Route::get('/sambhag-wise', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'sambhagWise'])->name('sambhag-wise');
            Route::get('/jila-wise', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'jilaWise'])->name('jila-wise');
            Route::get('/vikaskhand-wise', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'vikaskhandWise'])->name('vikaskhand-wise');

            // AJAX routes for nested dropdowns
            Route::get('/ajax/districts-by-division', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'getDistrictsByDivision'])->name('ajax.districts-by-division');
            Route::get('/ajax/vikaskhands-by-district', [App\Http\Controllers\Admin\PramukhPadadhikariController::class, 'getVikaskhandsByDistrict'])->name('ajax.vikaskhands-by-district');
        });

        // Organization Documents Routes
        Route::prefix('organization-documents')->name('organization-documents.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'store'])->name('store');
            Route::get('/{id}', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'edit'])->name('edit');
            Route::put('/{id}', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'update'])->name('update');
            Route::delete('/{id}', [App\Http\Controllers\Admin\OrganizationDocumentController::class, 'destroy'])->name('destroy');
        });

        // Contact Submissions Management
        Route::resource('contact-submissions', App\Http\Controllers\Admin\ContactSubmissionController::class)->only(['index', 'show', 'update', 'destroy']);

        // Master Data Management Routes
        Route::resource('membership-varg-masters', MembershipVargMasterController::class);
        Route::resource('education-qualifications', EducationQualificationController::class);
        Route::resource('office-masters', OfficeMasterController::class);
        Route::resource('department-masters', DepartmentMasterController::class);
        Route::resource('yadav-vargs', YadavVargController::class);
        Route::resource('division-masters', DivisionMasterController::class);
        Route::resource('district-masters', DistrictMasterController::class);
        Route::resource('vikaskhand-masters', VikaskhandMasterController::class);

        // Anya Seva Routes
        Route::prefix('naukri-sahayta')->name('naukri-sahayta.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'edit'])->name('edit');
            Route::put('/{id}', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'update'])->name('update');
            Route::delete('/{id}', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'destroy'])->name('destroy');
            Route::get('/{id}/download-biodata', [App\Http\Controllers\Admin\NaukriSahayataController::class, 'downloadBiodata'])->name('download-biodata');
        });

        Route::prefix('multinational-companies')->name('multinational-companies.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'edit'])->name('edit');
            Route::put('/{id}', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'update'])->name('update');
            Route::delete('/{id}', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'destroy'])->name('destroy');
            Route::get('/{id}/download-biodata', [App\Http\Controllers\Admin\MultinationalCompanyController::class, 'downloadBiodata'])->name('download-biodata');
        });

        Route::prefix('yadav-vyapar-grahak')->name('yadav-vyapar-grahak.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'show'])->name('show');
            Route::get('/{id}/edit', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'edit'])->name('edit');
            Route::put('/{id}', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'update'])->name('update');
            Route::delete('/{id}', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'destroy'])->name('destroy');
            Route::get('/{id}/download-photo', [App\Http\Controllers\Admin\YadavVyaparGrahakController::class, 'downloadPhoto'])->name('download-photo');
        });

        // Darshanik Ishthal Management
        Route::prefix('darshanik-ishthal')->name('darshanik-ishthal.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'index'])->name('index');
            Route::get('/{id}', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'show'])->name('show');
            Route::patch('/{id}/verify', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'verify'])->name('verify');
            Route::patch('/{id}/unverify', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'unverify'])->name('unverify');
            Route::patch('/{id}/activate', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'activate'])->name('activate');
            Route::patch('/{id}/deactivate', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'deactivate'])->name('deactivate');
            Route::delete('/{id}', [App\Http\Controllers\Admin\DarshanikIshthalController::class, 'destroy'])->name('destroy');
        });

        // Add more admin routes here...
    });
});

// Humare Bare Me (About Us) Routes
Route::prefix('humare-bare-me')->group(function () {
    Route::get('/uddeshya-aur-itihas', [HumareBareController::class, 'uddeshyaAurItihas'])->name('humare-bare-me.uddeshya');
    Route::get('/pramukh-padadhikari', [HumareBareController::class, 'pramukhPadadhikari'])->name('humare-bare-me.pramukh');
    Route::get('/sanrachna-karyapranali', [HumareBareController::class, 'sanrachnaKaryapranali'])->name('humare-bare-me.sanrachna');
    Route::get('/sanghatan-dastavej', [HumareBareController::class, 'sanghatanDastavej'])->name('humare-bare-me.documents');
    Route::get('/documents/{id}/download', [HumareBareController::class, 'downloadDocument'])->name('humare-bare-me.documents.download');
});

// Public Pramukh Padadhikari Location-wise Routes
Route::prefix('pramukh-padadhikari')->name('pramukh-padadhikari.')->group(function () {
    Route::get('/sambhag-wise', [App\Http\Controllers\PramukhPadadhikariController::class, 'sambhagWise'])->name('sambhag-wise');
    Route::get('/jila-wise', [App\Http\Controllers\PramukhPadadhikariController::class, 'jilaWise'])->name('jila-wise');
    Route::get('/vikaskhand-wise', [App\Http\Controllers\PramukhPadadhikariController::class, 'vikaskhandWise'])->name('vikaskhand-wise');
});

// Documents/Downloads Route - Single page for all documents
Route::get('/dastavej', function () {
    return view('dastavej.index');
})->name('dastavej.index');

// Contact Routes - Single page for contact and suggestions
Route::get('/sampark', [ContactController::class, 'index'])->name('sampark.index');
Route::post('/sampark', [ContactController::class, 'submit'])->name('sampark.submit');

// Membership Routes
Route::prefix('sadasya')->group(function () {
    Route::get('/prakriya', function () {
        return view('sadasya.prakriya');
    })->name('sadasya.prakriya');

    Route::get('/aavedan', [MembershipController::class, 'showApplicationForm'])->name('sadasya.aavedan');

    Route::post('/aavedan', [MembershipController::class, 'submitSadasyata'])->name('sadasya.aavedan.submit');




    Route::get('/suchana', [MembershipController::class, 'suchanaView'])->name('sadasya.suchana');

   

    Route::get('/vaivahik-panjiyan-new', [MembershipController::class, 'vaivahikPanjiyanNew'])
        ->name('sadasya.vaivahik-panjiyan.new');

    Route::post('/vaivahik-panjiyan', [MembershipController::class, 'vaivahikPanjiyanSubmit'])
        ->name('sadasya.vaivahik-panjiyan.submit');
});

// Ekadash Sadasyata Routes
Route::prefix('ekadash-sadasyata')->group(function () {
    Route::get('/aavedan', [App\Http\Controllers\EkadashSadasyataController::class, 'showApplicationForm'])
        ->name('ekadash-sadasyata.aavedan');

    Route::post('/aavedan', [App\Http\Controllers\EkadashSadasyataController::class, 'submitApplication'])
        ->name('ekadash-sadasyata.aavedan.submit');

    Route::get('/applications', [App\Http\Controllers\EkadashSadasyataController::class, 'showApplications'])
        ->name('ekadash-sadasyata.applications');

    Route::get('/members', [App\Http\Controllers\EkadashSadasyataController::class, 'showMembers'])
        ->name('ekadash-sadasyata.members');
});

Route::get('/vaivahik-panjiyan-details/{id}', [MembershipController::class, 'showVaivahikPanjiyan'])
    ->name('sadasya.vaivahik-panjiyan.show');

// Anya Seva Public Report Routes
Route::get('/darshanik-ishthal-reports', [App\Http\Controllers\AnyaSevaController::class, 'showDarshanikIshthalReports'])
    ->name('darshanik-ishthal-reports');
Route::post('/darshanik-ishthal-details/{id}', [App\Http\Controllers\AnyaSevaController::class, 'verifyDarshanikIshthalDetails'])
    ->name('darshanik-ishthal-details');

Route::get('/naukri-sahayta-reports', [App\Http\Controllers\AnyaSevaController::class, 'showNaukriSahayataReports'])
    ->name('naukri-sahayta-reports');
Route::post('/naukri-sahayta-details/{id}', [App\Http\Controllers\AnyaSevaController::class, 'verifyNaukriSahayataDetails'])
    ->name('naukri-sahayta-details');

Route::get('/multinational-company-reports', [App\Http\Controllers\AnyaSevaController::class, 'showMultinationalCompanyReports'])
    ->name('multinational-company-reports');
Route::post('/multinational-company-details/{id}', [App\Http\Controllers\AnyaSevaController::class, 'verifyMultinationalCompanyDetails'])
    ->name('multinational-company-details');

Route::get('/yadav-vyapar-grahak-reports', [App\Http\Controllers\AnyaSevaController::class, 'showYadavVyaparGrahakReports'])
    ->name('yadav-vyapar-grahak-reports');
Route::post('/yadav-vyapar-grahak-details/{id}', [App\Http\Controllers\AnyaSevaController::class, 'verifyYadavVyaparGrahakDetails'])
    ->name('yadav-vyapar-grahak-details');

// Interest Request Routes
Route::prefix('interest')->name('interest.')->group(function () {
    Route::post('/submit/{profileId}', [InterestRequestController::class, 'submit'])->name('submit');
    Route::get('/check-access/{profileId}', [InterestRequestController::class, 'checkContactAccess'])->name('check-access');
    Route::post('/accept/{requestId}', [InterestRequestController::class, 'accept'])->name('accept');
    Route::post('/reject/{requestId}', [InterestRequestController::class, 'reject'])->name('reject');
});

// Vaivahik Panjiyan Authentication Routes
Route::prefix('vaivahik')->name('vaivahik.')->group(function () {
    // Guest routes
    Route::middleware('guest:vaivahik')->group(function () {
        Route::get('/login', [VaivahikAuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [VaivahikAuthController::class, 'login']);
        Route::post('/register', [VaivahikAuthController::class, 'register']);
    });

    // Registration route accessible to all (redirects to main form)
    Route::get('/register', [VaivahikAuthController::class, 'showRegistrationForm'])->name('register');

    // Account setup help page
    Route::get('/account-setup', function () {
        return view('vaivahik-auth.account-setup');
    })->name('account-setup');

    // Authenticated routes
    Route::middleware('auth:vaivahik')->group(function () {
        Route::get('/dashboard', [VaivahikDashboardController::class, 'index'])->name('dashboard');
        Route::get('/received-requests', [VaivahikDashboardController::class, 'receivedRequests'])->name('received-requests');
        Route::get('/sent-requests', [VaivahikDashboardController::class, 'sentRequests'])->name('sent-requests');
        Route::post('/respond-request/{requestId}', [VaivahikDashboardController::class, 'respondToRequest'])->name('respond-request');
        Route::get('/edit-profile', [VaivahikDashboardController::class, 'editProfile'])->name('edit-profile');
        Route::put('/update-profile', [VaivahikDashboardController::class, 'updateProfile'])->name('update-profile');
        Route::get('/contact-info/{requestId}', [VaivahikDashboardController::class, 'showContactInfo'])->name('contact-info');
        Route::post('/logout', [VaivahikAuthController::class, 'logout'])->name('logout');
    });
});

// For Vivah bandhan 
 Route::get('/vaivahik-sadasya-panjiyan', [MembershipController::class, 'vaivahikPanjiyan'])
        ->name('sadasya.vaivahik-panjiyan');



// Activities Routes - For frontend
Route::prefix('activities')->group(function () {
    Route::get('/', [ActivitiesController::class, 'index'])->name('activities.index');
    Route::get('/social', [ActivitiesController::class, 'social'])->name('activities.social');
    Route::get('/cultural', [ActivitiesController::class, 'cultural'])->name('activities.cultural');
    Route::get('/educational', [ActivitiesController::class, 'educational'])->name('activities.educational');
    Route::get('/government', [ActivitiesController::class, 'government'])->name('activities.government');
    Route::get('/gallery', [ActivitiesController::class, 'gallery'])->name('activities.gallery');
    Route::get('/{id}', [ActivitiesController::class, 'show'])->name('activities.show');
});

// News & Announcements Routes
Route::prefix('news')->group(function () {
    Route::get('/', [NewsController::class, 'index'])->name('news.index');
    Route::get('/general', [NewsController::class, 'general'])->name('news.general');
    Route::get('/meetings', [NewsController::class, 'meetings'])->name('news.meetings');
    Route::get('/obituaries', [NewsController::class, 'obituaries'])->name('news.obituaries');
    Route::get('/appointments', [NewsController::class, 'appointments'])->name('news.appointments');
    Route::get('/press', [NewsController::class, 'press'])->name('news.press');
    Route::get('/media', [NewsController::class, 'media'])->name('news.media');
    Route::get('/{id}', [NewsController::class, 'show'])->name('news.show');
});


// Anya Seva Routes - Frontend
Route::prefix('anya-seva')->name('anya-seva.')->group(function () {
    Route::get('/naukri-sahayta', [App\Http\Controllers\AnyaSevaController::class, 'showNaukriSahayataForm'])->name('naukri-sahayta');
    Route::post('/naukri-sahayta', [App\Http\Controllers\AnyaSevaController::class, 'submitNaukriSahayta'])->name('naukri-sahayta.submit');

    Route::get('/multinational-company', [App\Http\Controllers\AnyaSevaController::class, 'showMultinationalCompanyForm'])->name('multinational-company');
    Route::post('/multinational-company', [App\Http\Controllers\AnyaSevaController::class, 'submitMultinationalCompany'])->name('multinational-company.submit');

    Route::get('/yadav-vyapar-grahak', [App\Http\Controllers\AnyaSevaController::class, 'showYadavVyaparGrahakForm'])->name('yadav-vyapar-grahak');
    Route::post('/yadav-vyapar-grahak', [App\Http\Controllers\AnyaSevaController::class, 'submitYadavVyaparGrahak'])->name('yadav-vyapar-grahak.submit');

    Route::get('/darshanik-ishthal', [App\Http\Controllers\AnyaSevaController::class, 'showDarshanikIshthalForm'])->name('darshanik-ishthal');
    Route::post('/darshanik-ishthal', [App\Http\Controllers\AnyaSevaController::class, 'submitDarshanikIshthal'])->name('darshanik-ishthal.submit');
});

// Member Authentication Routes
Route::prefix('member')->name('member.')->group(function () {
    // Guest routes
    Route::middleware('guest:member')->group(function () {
        Route::get('/login', [MemberAuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [MemberAuthController::class, 'login']);
    });

    // Authenticated member routes
    Route::middleware('auth:member')->group(function () {
        Route::get('/dashboard', [MemberDashboardController::class, 'index'])->name('dashboard');
        Route::get('/application-status', [MemberDashboardController::class, 'applicationStatus'])->name('application-status');
        Route::get('/profile', [MemberDashboardController::class, 'profile'])->name('profile');
        Route::get('/profile/edit', [MemberDashboardController::class, 'editProfile'])->name('profile.edit');
        Route::post('/profile', [MemberDashboardController::class, 'updateProfile'])->name('profile.update');
        Route::get('/family-tree', [MemberDashboardController::class, 'familyTree'])->name('family-tree');
        Route::get('/download-card', [MemberDashboardController::class, 'downloadCard'])->name('download-card');
        Route::get('/preview-card', [MemberDashboardController::class, 'previewCard'])->name('preview-card');
        // Interest Request Management
        Route::get('/my-requests', [InterestRequestController::class, 'myRequests'])->name('my-requests');
        Route::get('/received-requests', [InterestRequestController::class, 'receivedRequests'])->name('received-requests');







        Route::post('/logout', [MemberAuthController::class, 'logout'])->name('logout');
    });
});
