@extends('layouts.admin') @section('title', 'यादव व्यापार एवं यादव ग्राहक') @section('content') <div class="p-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"> <div> <p class="text-2xl font-bold admin-text-secondary">यादव व्यापार एवं यादव ग्राहक</p> <p class="admin-text-secondary mt-1">यादव व्यापारियों और ग्राहकों की जानकारी प्रबंधित करें</p> </div> </div> <!-- Search and Filter Form --> <div class="admin-card mb-6"> <div class="p-4"> <form method="GET" action="{{ route('admin.yadav-vyapar-grahak.index') }}" class="flex flex-col lg:flex-row gap-4"> <div class="flex-1"> <input type="text" name="search" value="{{ request('search') }}" placeholder="नाम, सदस्यता क्रमांक, मोबाइल, आजीविका से खोजें..." class="admin-form-input w-full"> </div> <div class="flex flex-col sm:flex-row gap-2"> <select name="category" class="admin-form-input"> <option value="">सभी श्रेणियां</option> @foreach($categories as $key => $value) <option value="{{ $key }}" {{ request('category') == $key ? 'selected' : '' }}>{{ $value }}</option> @endforeach </select> <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"> <i class="fas fa-search mr-2"></i>खोजें </button> @if(request('search') || request('category')) <a href="{{ route('admin.yadav-vyapar-grahak.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"> <i class="fas fa-times mr-2"></i>साफ़ करें </a> @endif </div> </form> </div> </div> <!-- Success/Error Messages --> @if(session('success')) <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-4 relative" role="alert"> <span class="block sm:inline">{{ session('success') }}</span> </div> @endif @if(session('error')) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-4 relative" role="alert"> <span class="block sm:inline">{{ session('error') }}</span> </div> @endif <!-- Data Table --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <p class="text-lg font-semibold admin-text-secondary">सदस्यों की सूची ({{ $yadavVyaparGrahak->total() }} कुल)</p> </div> <div class="p-6"> @if($yadavVyaparGrahak->count() > 0) <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">नाम</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">श्रेणी</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">सदस्यता क्रमांक</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">आजीविका</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">जिला</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">मोबाइल</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">फोटो</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">मैप</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">दिनांक</th> <th class="px-6 py-3 text-left text-xs font-medium admin-text-secondary uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @foreach($yadavVyaparGrahak as $record) <tr class="hover:bg-gray-50:bg-navy-800"> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm font-medium admin-text-secondary">{{ $record->naam }}</div> <div class="text-xs admin-text-secondary">{{ $record->sadasyata_prakar }}</div> </td> <td class="px-6 py-4 whitespace-nowrap"> <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full @if($record->category == 'swasthya') bg-green-100 text-green-800 @elseif($record->category == 'gharelu') bg-blue-100 text-blue-800 @elseif($record->category == 'salahkar') bg-purple-100 text-purple-800 @else bg-gray-100 text-gray-800 @endif"> {{ $record->category_name }} </span> </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $record->sadasyata_kramank }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ Str::limit($record->ajivika, 20) }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $record->jila }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $record->mobile_number }}</td> <td class="px-6 py-4 whitespace-nowrap"> @if($record->photo) <a href="{{ route('admin.yadav-vyapar-grahak.download-photo', $record->id) }}" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 hover:bg-green-200 transition-colors"> <i class="fas fa-download mr-1"></i>डाउनलोड </a> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"> नहीं </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap"> @if($record->gmap_link) <a href="{{ $record->formatted_gmap_link }}" target="_blank" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"> <i class="fas fa-map-marker-alt mr-1"></i>देखें </a> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"> नहीं </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $record->created_at->format('d/m/Y') }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex space-x-2"> <a href="{{ route('admin.yadav-vyapar-grahak.show', $record->id) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" title="देखें"> <i class="fas fa-eye"></i> </a> <a href="{{ route('admin.yadav-vyapar-grahak.edit', $record->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" title="संपादित करें"> <i class="fas fa-edit"></i> </a> <button type="button" class="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs transition-colors duration-200" onclick="confirmDelete({{ $record->id }})" title="हटाएं"> <i class="fas fa-trash"></i> </button> </div> </td> </tr> @endforeach </tbody> </table> </div> <!-- Pagination --> <div class="flex justify-center mt-6"> {{ $yadavVyaparGrahak->appends(request()->query())->links() }} </div> @else <div class="text-center py-12"> <i class="fas fa-store text-6xl text-gray-400 mb-4"></i> <h3 class="text-lg font-medium admin-text-primary mb-2">कोई रिकॉर्ड नहीं मिला</h3> <p class="admin-text-secondary mb-6">अभी तक कोई यादव व्यापार ग्राहक की जानकारी नहीं आई है।</p> </div> @endif </div> </div> </div> <!-- Delete Confirmation Modal --> <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"> <div class="mt-3 text-center"> <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100"> <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i> </div> <h3 class="text-lg font-medium admin-text-primary mt-4">रिकॉर्ड हटाएं</h3> <div class="mt-2 px-7 py-3"> <p class="text-sm admin-text-secondary"> क्या आप वाकई इस रिकॉर्ड को हटाना चाहते हैं? यह कार्य पूर्ववत नहीं किया जा सकता। </p> </div> <div class="flex gap-3 mt-4"> <button type="button" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300" onclick="closeDeleteModal()"> रद्द करें </button> <form id="deleteForm" method="POST" style="display: inline;"> @csrf @method('DELETE') <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"> हटाएं </button> </form> </div> </div> </div> </div> @endsection @push('scripts') <script> function confirmDelete(recordId) { const deleteForm = document.getElementById('deleteForm'); deleteForm.action = `/admin/yadav-vyapar-grahak/${recordId}`; document.getElementById('deleteModal').classList.remove('hidden'); } function closeDeleteModal() { document.getElementById('deleteModal').classList.add('hidden'); } // Close modal when clicking outside document.getElementById('deleteModal').addEventListener('click', function(e) { if (e.target === this) { closeDeleteModal(); } }); </script> @endpush 