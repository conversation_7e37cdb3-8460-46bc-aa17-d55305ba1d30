<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interest_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('requester_id'); // Can be from memberships or vaivahik_panjiyans
            $table->string('requester_type')->default('vaivahik'); // 'member' or 'vaivahik'
            $table->foreignId('profile_id')->constrained('vaivahik_panjiyans')->onDelete('cascade');
            $table->enum('status', ['pending', 'accepted', 'rejected'])->default('pending');
            $table->text('message')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->timestamps();

            // Prevent duplicate requests
            $table->unique(['requester_id', 'requester_type', 'profile_id']);

            // Add indexes for better performance
            $table->index(['requester_id', 'requester_type']);
            $table->index(['profile_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interest_requests');
    }
};
