@extends('layouts.admin') @section('title', 'वैवाहिक उपयोगकर्ता प्रबंधन') @section('content') <div class="space-y-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"> <div> <h1 class="text-2xl font-bold admin-text-primary">वैवाहिक उपयोगकर्ता प्रबंधन</h1> <p class="text-gray-600 mt-1">पंजीकृत वैवाहिक उपयोगकर्ताओं का प्रबंधन करें</p> </div> </div> <!-- Success/Error Messages --> @if(session('success')) <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <p class="text-sm font-medium text-green-800">{{ session('success') }}</p> </div> </div> </div> @endif @if(session('error')) <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <p class="text-sm font-medium text-red-800">{{ session('error') }}</p> </div> </div> </div> @endif @if($errors->any()) <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4"> <div class="flex"> <div class="flex-shrink-0"> <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <div class="ml-3"> <div class="text-sm font-medium text-red-800"> <ul class="list-disc list-inside"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> </div> </div> </div> @endif <!-- Statistics Cards --> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> <div class="admin-card p-6 border-l-4 border-blue-500"> <div class="flex items-center justify-between"> <div> <p class="text-sm font-medium text-gray-600 uppercase tracking-wider">कुल उपयोगकर्ता</p> <p class="text-3xl font-bold admin-text-primary mt-2">{{ $stats['total'] }}</p> </div> <div class="p-3 bg-blue-100 rounded-full"> <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path> </svg> </div> </div> </div> <div class="admin-card p-6 border-l-4 border-green-500"> <div class="flex items-center justify-between"> <div> <p class="text-sm font-medium text-gray-600 uppercase tracking-wider">स्वीकृत</p> <p class="text-3xl font-bold admin-text-primary mt-2">{{ $stats['approved'] }}</p> </div> <div class="p-3 bg-green-100 rounded-full"> <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> </div> </div> </div> <div class="admin-card p-6 border-l-4 border-yellow-500"> <div class="flex items-center justify-between"> <div> <p class="text-sm font-medium text-gray-600 uppercase tracking-wider">प्रतीक्षा में</p> <p class="text-3xl font-bold admin-text-primary mt-2">{{ $stats['pending'] }}</p> </div> <div class="p-3 bg-yellow-100 rounded-full"> <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> </div> </div> </div> <div class="admin-card p-6 border-l-4 border-red-500"> <div class="flex items-center justify-between"> <div> <p class="text-sm font-medium text-gray-600 uppercase tracking-wider">अस्वीकृत</p> <p class="text-3xl font-bold admin-text-primary mt-2">{{ $stats['rejected'] }}</p> </div> <div class="p-3 bg-red-100 rounded-full"> <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> </div> </div> </div> </div> <!-- Users Table --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">वैवाहिक उपयोगकर्ता सूची</h3> </div> <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">नाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ईमेल</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">मोबाइल</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">उपजीविका</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">पारिवारिक सदस्यता</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">सक्रिय</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">पंजीकरण दिनांक</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @forelse($users as $user) <tr class="hover:bg-gray-50:bg-navy-800"> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-primary">{{ $user->id }}</td> <td class="px-6 py-4 whitespace-nowrap"> <div class="flex items-center"> @if($user->photo1) <img src="{{ asset('storage/' . $user->photo1) }}" class="w-10 h-10 rounded-full object-cover mr-3"> @else <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3"> <svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg> </div> @endif <div> <div class="text-sm font-medium admin-text-primary">{{ $user->naam }}</div> <div class="text-sm text-gray-500">{{ $user->ling }}</div> </div> </div> </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $user->owner_email }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $user->mobile }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $user->upjivika ?: ($user->vartaman_karya ?: '-') }} </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary"> {{ $user->family_member_membership_number ?: '-' }} </td> <td class="px-6 py-4 whitespace-nowrap"> @if($user->status === 'approved') <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">स्वीकृत</span> @elseif($user->status === 'pending') <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">प्रतीक्षा में</span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">अस्वीकृत</span> @endif </td> <td class="px-6 py-4 whitespace-nowrap"> @if($user->is_active) <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">सक्रिय</span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">निष्क्रिय</span> @endif </td> <td class="px-6 py-4 whitespace-nowrap text-sm admin-text-secondary">{{ $user->created_at->format('d/m/Y') }}</td> <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"> <div class="flex space-x-2"> <a href="{{ route('admin.vaivahik-users.show', $user->id) }}" class="inline-flex items-center px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs font-medium rounded-md transition-colors:bg-blue-900/50" title="विवरण देखें"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg> </a> <button type="button" class="inline-flex items-center px-3 py-1 bg-purple-100 hover:bg-purple-200 text-purple-800 text-xs font-medium rounded-md transition-colors:bg-purple-900/50" title="लॉगिन अपडेट करें" onclick="openQuickLoginModal({{ $user->id }}, '{{ $user->owner_email }}', {{ $user->is_active ? 'true' : 'false' }})"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> </button> @if($user->status === 'pending') <form method="POST" action="{{ route('admin.vaivahik-users.approve', $user->id) }}" class="inline"> @csrf @method('PATCH') <button type="submit" class="inline-flex items-center px-3 py-1 bg-green-100 hover:bg-green-200 text-green-800 text-xs font-medium rounded-md transition-colors:bg-green-900/50" title="स्वीकृत करें" onclick="return confirm('क्या आप इस उपयोगकर्ता को स्वीकृत करना चाहते हैं?')"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path> </svg> </button> </form> <form method="POST" action="{{ route('admin.vaivahik-users.reject', $user->id) }}" class="inline"> @csrf @method('PATCH') <button type="submit" class="inline-flex items-center px-3 py-1 bg-red-100 hover:bg-red-200 text-red-800 text-xs font-medium rounded-md transition-colors:bg-red-900/50" title="अस्वीकृत करें" onclick="return confirm('क्या आप इस उपयोगकर्ता को अस्वीकृत करना चाहते हैं?')"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </form> @endif <form method="POST" action="{{ route('admin.vaivahik-users.destroy', $user->id) }}" class="inline"> @csrf @method('DELETE') <button type="submit" class="inline-flex items-center px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 text-xs font-medium rounded-md transition-colors:bg-yellow-900/50" title="खाता हटाएं (प्रोफाइल सुरक्षित)" onclick="return confirm('क्या आप इस उपयोगकर्ता का खाता हटाना चाहते हैं? प्रोफाइल सुरक्षित रहेगी।')"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path> </svg> </button> </form> </div> </td> </tr> @empty <tr> <td colspan="8" class="px-6 py-4 text-center text-gray-500">कोई वैवाहिक उपयोगकर्ता नहीं मिला</td> </tr> @endforelse </tbody> </table> </div> <!-- Pagination --> <div class="px-6 py-4 border-t border-gray-200"> {{ $users->links() }} </div> </div> </div> <!-- Quick Login Update Modal --> <div id="quickLoginModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"> <form id="quickLoginForm" method="POST"> @csrf @method('PATCH') <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">त्वरित लॉगिन अपडेट</h3> <button type="button" class="text-gray-400 hover:text-gray-600:text-gray-300" onclick="closeQuickLoginModal()"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> <div class="space-y-4"> <div> <label for="quick_owner_email" class="block text-sm font-medium text-gray-700 mb-2">ईमेल पता</label> <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-navy-500 focus:border-navy-500" id="quick_owner_email" name="owner_email" required> </div> <div> <label for="quick_owner_password" class="block text-sm font-medium text-gray-700 mb-2">नया पासवर्ड (वैकल्पिक)</label> <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-navy-500 focus:border-navy-500" id="quick_owner_password" name="owner_password" placeholder="नया पासवर्ड दर्ज करें"> <p class="text-xs text-gray-500 mt-1">खाली छोड़ें यदि पासवर्ड नहीं बदलना है</p> </div> <div class="flex items-center"> <input type="hidden" name="is_active" value="0"> <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="quick_is_active" name="is_active" value="1"> <label for="quick_is_active" class="ml-2 block text-sm text-gray-700">खाता सक्रिय है</label> </div> </div> <div class="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200"> <button type="button" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors" onclick="closeQuickLoginModal()"> रद्द करें </button> <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"> अपडेट करें </button> </div> </form> </div> </div> <script> function openQuickLoginModal(userId, email, isActive) { document.getElementById('quickLoginForm').action = `/admin/vaivahik-users/${userId}/update-login`; document.getElementById('quick_owner_email').value = email || ''; document.getElementById('quick_owner_password').value = ''; document.getElementById('quick_is_active').checked = isActive === true || isActive === 'true'; document.getElementById('quickLoginModal').classList.remove('hidden'); } function closeQuickLoginModal() { document.getElementById('quickLoginModal').classList.add('hidden'); } // Close modal when clicking outside document.getElementById('quickLoginModal').addEventListener('click', function(e) { if (e.target === this) { closeQuickLoginModal(); } }); </script> @endsection 