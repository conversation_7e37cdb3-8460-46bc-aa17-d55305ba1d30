<?php

namespace Database\Seeders;

use App\Models\AboutContent;
use Illuminate\Database\Seeder;

class AboutContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        AboutContent::truncate();
        
        // Main About Content - हमारे बारे में
        AboutContent::create([
            'page_key' => AboutContent::PAGE_UDDESHYA,
            'section_key' => 'about-main',
            'title' => 'हमारे बारे में',
            'content' => $this->getMainAboutContent(),
            'display_order' => 1,
            'is_published' => true,
        ]);
        
        // Educational Empowerment Section
        AboutContent::create([
            'page_key' => AboutContent::PAGE_UDDESHYA,
            'section_key' => 'education',
            'title' => 'शैक्षणिक सशक्तिकरण',
            'content' => $this->getEducationContent(),
            'display_order' => 2,
            'is_published' => true,
        ]);
        
        // Social and Cultural Unity Section
        AboutContent::create([
            'page_key' => AboutContent::PAGE_UDDESHYA,
            'section_key' => 'social-unity',
            'title' => 'सामाजिक एवं सांस्कृतिक एकता',
            'content' => $this->getSocialUnityContent(),
            'display_order' => 3,
            'is_published' => true,
        ]);
        
        // Economic System and Membership Section
        AboutContent::create([
            'page_key' => AboutContent::PAGE_UDDESHYA,
            'section_key' => 'economic-system',
            'title' => 'आर्थिक व्यवस्था और सदस्यता',
            'content' => $this->getEconomicSystemContent(),
            'display_order' => 4,
            'is_published' => true,
        ]);
    }

    private function getMainAboutContent(): string
    {
        return '<h2 class="text-2xl font-bold text-navy-blue mb-4">हमारे बारे में</h2>
                
                <h3 class="text-xl font-semibold text-navy-blue mt-6 mb-3">उद्देश्य और इतिहास:</h3>
                <p class="mb-4">"छत्तीसगढ़ यादव शासकीय सेवक समिति रायपुर" द्वारा प्रस्तुत यह परियोजना समाज के सर्वांगीण विकास हेतु एक समर्पित एवं संगठित प्रयास है। इस योजना के माध्यम से समाज को शैक्षणिक, स्वास्थ्य, आर्थिक, सामाजिक और सांस्कृतिक रूप से सशक्त बनाने का लक्ष्य रखा गया है।</p>
                
                <h3 class="text-xl font-semibold text-navy-blue mt-6 mb-3">1. बुनियादी ढांचा विकास "Yadav\'s Multi Speciality Social Premises" रायपुर</h3>
                <p class="mb-4"><strong>सर्वसुविधायुक्त समाज भवन निर्माण रायपुर</strong> - क्षेत्रफल न्यूनतम एक या आधा एकड़ भूमि में।</p>
                
                <ul class="list-disc pl-6 mb-4 space-y-2">
                    <li>आगंतुकों हेतु ठहरने की व्यवस्था - आवासीय कक्ष, भोजन एवं जलपान की व्यवस्था</li>
                    <li>परिवहन की सुविधा: रेल्वे स्टेशन एवं बस स्टैंड से लाने ले जाने की व्यवस्था</li>
                    <li>इलाज के लिए आये हुए मरीज के साथ परिवार के सदस्यों को रुकने की व्यवस्था</li>
                    <li>विवाह घर/सभागार, कार्यालय कक्ष, बैठक कक्ष आदि उक्त भवन में शामिल होगा</li>
                </ul>';
    }

    private function getEducationContent(): string
    {
        return '<h3 class="text-xl font-semibold text-navy-blue mt-6 mb-3">2. शैक्षणिक सशक्तिकरण</h3>
                <p class="mb-4"><strong>आधुनिक स्कूलों की स्थापना</strong> - गुणवत्तायुक्त शिक्षा हेतु आधुनिक सुविधाओं से युक्त विद्यालयों/छात्रावास की स्थापना रायपुर में।</p>
                
                <ul class="list-disc pl-6 mb-4 space-y-2">
                    <li>नवोदय/सैनिक विद्यालय/NEET/JEE/व्यापम/PSC जैसे प्रतियोगी परीक्षाओं की तैयारी हेतु कोचिंग की व्यवस्था</li>
                    <li>संभाग स्तर/जिला स्तर पर प्रारंभ की जावेगी</li>
                    <li>रोजगार मूलक कौशल प्रशिक्षण की व्यवस्था जैसे:
                        <ul class="list-disc pl-6 mt-2 space-y-1">
                            <li>A.C. रिपेयरिंग</li>
                            <li>प्लम्बर</li>
                            <li>रेफ्रिजेटर</li>
                            <li>मोबाइल</li>
                            <li>इलेक्ट्रीशियन</li>
                            <li>कम्प्यूटर रिपेयरिंग एवं बेसिक कोर्स</li>
                            <li>डिजिटल मार्केटिंग जैसे रोजगार मूलक प्रशिक्षण देने का कार्य किये जायेंगे</li>
                        </ul>
                    </li>
                </ul>';
    }

    private function getSocialUnityContent(): string
    {
        return '<h3 class="text-xl font-semibold text-navy-blue mt-6 mb-3">3. सामाजिक एवं सांस्कृतिक एकता</h3>
                <p class="mb-4">उक्त संगठन का वेब साइट होगा जिसमें प्रत्येक सदस्य परिवार का पारिवारिक विवरण एक्सेल शीट के माध्यम से उपलब्ध कराया जाएगा, ताकि संगठन द्वारा परिवारों के बच्चों को वैवाहिक समन्वय एवं कैरियर मार्गदर्शन में सहायता प्रदान की जा सके।</p>
                
                <h4 class="text-lg font-semibold text-navy-blue mt-4 mb-2">4. समाज हित में विचार</h4>
                <p class="mb-4"><strong>वंशवृक्ष पोथी</strong> - डिजिटल पोथी जिसमें प्रत्येक यादव परिवार की 7 पीढ़ियों की जानकारी होगी। परिवारों का डेटा एकत्र कर सुरक्षित डिजिटल डेटाबेस तैयार किया जावेगा।</p>
                
                <p class="mb-4">सभी सदस्यों से समाज के हित हेतु विभिन्न माध्यमों से सुझाव आमंत्रित किये जाएंगे। विचारों पर चर्चा कर उपयुक्त योजना का निर्माण एवं क्रियान्वयन किया जावेगा साथ ही वार्षिक आमसभा, क्षेत्रीय बैठकें, कार्यशाला आयोजित की जावेगी।</p>';
    }

    private function getEconomicSystemContent(): string
    {
        return '<h3 class="text-xl font-semibold text-navy-blue mt-6 mb-3">आर्थिक व्यवस्था</h3>
                <p class="mb-4">प्रदेश में यादव की जनसंख्या लगभग 35 लाख एवं परिवार की संख्या लगभग 15 लाख। यदि प्रत्येक परिवार से 11 रुपये भी सहयोग करते हैं तो प्रत्येक माह लगभग 1.65 करोड़ की राशि होती है जो कि समाज हित में और समाज को सशक्त बनाने में काफी सहयोग साबित होगा।</p>

                <h4 class="text-lg font-semibold text-navy-blue mt-4 mb-2">सदस्यता की श्रेणियां</h4>
                <p class="mb-4">इस योजना में जितने शासकीय सेवक जो की यादव समाज परिवार से है:</p>

                <div class="bg-blue-50 p-4 rounded-lg mb-4">
                    <h5 class="font-semibold text-navy-blue mb-2">शासकीय सेवकों के लिए:</h5>
                    <ul class="list-disc pl-6 space-y-1">
                        <li><strong>संरक्षक सदस्य:</strong> 10,000 रुपये (एकबार)</li>
                        <li><strong>आजीवन सदस्य:</strong> 5,000 रुपये (एकबार)</li>
                        <li><strong>वार्षिक सदस्य:</strong> 1,200 रुपये (प्रतिवर्ष)</li>
                        <li><strong>मासिक योगदान:</strong> 100 रुपये प्रति माह</li>
                    </ul>
                </div>

                <div class="bg-green-50 p-4 rounded-lg mb-4">
                    <h5 class="font-semibold text-navy-blue mb-2">अशासकीय सदस्यों के लिए:</h5>
                    <p class="mb-2">छत्तीसगढ़ यादव शासकीय सेवक की उप समिति "एकादश उत्थान यादव कल्याण" के नाम से उपसमिति है।</p>
                    <ul class="list-disc pl-6 space-y-1">
                        <li>न्यूनतम योगदान: 11 रुपये या इच्छानुसार राशि</li>
                        <li>मासिक योगदान: 11 रुपये या इच्छानुसार राशि</li>
                    </ul>
                </div>

                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h5 class="font-semibold text-navy-blue mb-2">महत्वपूर्ण नोट:</h5>
                    <p><strong>सदस्यता ग्रहण हेतु दी गई राशि अमानत राशि के रूप में होगी।</strong> यदि सदस्यता वापस लेते हैं तो सदस्यता राशि वापसी योग्य होगी।</p>
                </div>';
    }
}
