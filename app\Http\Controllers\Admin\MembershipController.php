<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Membership;
use App\Models\VaivahikPanjiyan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class MembershipController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of membership applications.
     */
    public function index(Request $request)
    {
        $status = $request->query('status', 'all');
        
        $query = Membership::query()
            ->with(['membershipVarg', 'educationQualification', 'officeMaster', 'departmentMaster', 'yadavVarg', 'divisionMaster', 'districtMaster', 'vikaskhandMaster'])
            ->orderBy('created_at', 'desc');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $memberships = $query->paginate(15);
        
        return view('admin.memberships.index', [
            'memberships' => $memberships,
            'status' => $status,
            'statusOptions' => [
                'all' => 'All Applications',
                Membership::STATUS_PENDING => 'Pending',
                Membership::STATUS_APPROVED => 'Approved',
                Membership::STATUS_REJECTED => 'Rejected',
            ]
        ]);
    }

    /**
     * Display the specified membership application.
     */
    public function show(string $id)
    {
        $membership = Membership::with(['membershipVarg', 'educationQualification', 'officeMaster', 'departmentMaster', 'yadavVarg', 'divisionMaster', 'districtMaster', 'vikaskhandMaster'])
            ->findOrFail($id);
        return view('admin.memberships.show', compact('membership'));
    }

    /**
     * Approve a membership application.
     */
    public function approve(Request $request, string $id)
    {
        $membership = Membership::findOrFail($id);

        if ($membership->status !== Membership::STATUS_PENDING) {
            return redirect()->back()->with('error', 'Only pending applications can be approved.');
        }

        $membership->update([
            'status' => Membership::STATUS_APPROVED,
            'rejection_reason' => null,
        ]);

        // Log the approval action
        \Log::info('Membership approved', [
            'membership_id' => $membership->id,
            'membership_number' => $membership->membership_number,
            'member_name' => $membership->name,
            'approved_by' => auth()->user()->name,
        ]);

        return redirect()->route('admin.memberships.index')
            ->with('success', "Membership application for {$membership->name} (#{$membership->membership_number}) has been approved successfully.");
    }

    /**
     * Reject a membership application.
     */
    public function reject(Request $request, string $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        $membership = Membership::findOrFail($id);

        if ($membership->status !== Membership::STATUS_PENDING) {
            return redirect()->back()->with('error', 'Only pending applications can be rejected.');
        }

        $membership->update([
            'status' => Membership::STATUS_REJECTED,
            'rejection_reason' => $request->rejection_reason,
        ]);

        // Log the rejection action
        \Log::info('Membership rejected', [
            'membership_id' => $membership->id,
            'membership_number' => $membership->membership_number,
            'member_name' => $membership->name,
            'rejection_reason' => $request->rejection_reason,
            'rejected_by' => auth()->user()->name,
        ]);

        return redirect()->route('admin.memberships.index')
            ->with('success', "Membership application for {$membership->name} (#{$membership->membership_number}) has been rejected.");
    }

    /**
     * Reset a membership application status to pending.
     */
    public function reset(string $id)
    {
        $membership = Membership::findOrFail($id);
        
        $membership->update([
            'status' => Membership::STATUS_PENDING,
            'rejection_reason' => null,
        ]);
        
        return redirect()->route('admin.memberships.index')
            ->with('success', 'Membership application status reset to pending.');
    }

    /**
     * Delete a membership application.
     */
    public function destroy(string $id)
    {
        $membership = Membership::findOrFail($id);
        
        // Delete associated files
        if ($membership->photo && Storage::disk('public')->exists($membership->photo)) {
            Storage::disk('public')->delete($membership->photo);
        }
        
        if ($membership->signature && Storage::disk('public')->exists($membership->signature)) {
            Storage::disk('public')->delete($membership->signature);
        }
        
        $membership->delete();
        
        return redirect()->route('admin.memberships.index')
            ->with('success', 'Membership application deleted successfully.');
    }

    /**
     * Bulk approve membership applications.
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'membership_ids' => 'required|array',
            'membership_ids.*' => 'exists:memberships,id',
        ]);

        $count = Membership::whereIn('id', $request->membership_ids)
            ->where('status', Membership::STATUS_PENDING)
            ->update([
                'status' => Membership::STATUS_APPROVED,
                'rejection_reason' => null,
            ]);

        return redirect()->route('admin.memberships.index')
            ->with('success', "Successfully approved {$count} membership application(s).");
    }

    /**
     * Bulk reject membership applications.
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'membership_ids' => 'required|array',
            'membership_ids.*' => 'exists:memberships,id',
            'rejection_reason' => 'required|string|max:500',
        ]);

        $count = Membership::whereIn('id', $request->membership_ids)
            ->where('status', Membership::STATUS_PENDING)
            ->update([
                'status' => Membership::STATUS_REJECTED,
                'rejection_reason' => $request->rejection_reason,
            ]);

        return redirect()->route('admin.memberships.index')
            ->with('success', "Successfully rejected {$count} membership application(s).");
    }

    /**
     * Get membership statistics for dashboard
     */
    public function getStats()
    {
        return [
            'total' => Membership::count(),
            'pending' => Membership::where('status', Membership::STATUS_PENDING)->count(),
            'approved' => Membership::where('status', Membership::STATUS_APPROVED)->count(),
            'rejected' => Membership::where('status', Membership::STATUS_REJECTED)->count(),
        ];
    }

    /**
     * Display vaivahik users list
     */
    public function vaivahikUsers()
    {
        $users = VaivahikPanjiyan::whereNotNull('owner_email')
            ->latest()
            ->paginate(15);

        $stats = [
            'total' => VaivahikPanjiyan::whereNotNull('owner_email')->count(),
            'approved' => VaivahikPanjiyan::whereNotNull('owner_email')
                ->where('status', VaivahikPanjiyan::STATUS_APPROVED)->count(),
            'pending' => VaivahikPanjiyan::whereNotNull('owner_email')
                ->where('status', VaivahikPanjiyan::STATUS_PENDING)->count(),
            'rejected' => VaivahikPanjiyan::whereNotNull('owner_email')
                ->where('status', VaivahikPanjiyan::STATUS_REJECTED)->count(),
        ];

        return view('admin.vaivahik-users.index', compact('users', 'stats'));
    }

    /**
     * Show vaivahik user details
     */
    public function showVaivahikUser($id)
    {
        $user = VaivahikPanjiyan::whereNotNull('owner_email')->findOrFail($id);
        return view('admin.vaivahik-users.show', compact('user'));
    }

    /**
     * Approve vaivahik user
     */
    public function approveVaivahikUser($id)
    {
        $user = VaivahikPanjiyan::whereNotNull('owner_email')->findOrFail($id);
        $user->update([
            'status' => VaivahikPanjiyan::STATUS_APPROVED,
            'is_active' => true
        ]);

        return redirect()->back()->with('success', 'वैवाहिक उपयोगकर्ता स्वीकृत किया गया।');
    }

    /**
     * Reject vaivahik user
     */
    public function rejectVaivahikUser($id)
    {
        $user = VaivahikPanjiyan::whereNotNull('owner_email')->findOrFail($id);
        $user->update([
            'status' => VaivahikPanjiyan::STATUS_REJECTED,
            'is_active' => false
        ]);

        return redirect()->back()->with('success', 'वैवाहिक उपयोगकर्ता अस्वीकृत किया गया।');
    }

    /**
     * Update vaivahik user login information
     */
    public function updateVaivahikLogin(Request $request, $id)
    {
        try {
            \Log::info('Vaivahik login update request', [
                'id' => $id,
                'data' => $request->all()
            ]);

            $request->validate([
                'owner_email' => 'required|email|unique:vaivahik_panjiyans,owner_email,' . $id . ',id',
                'owner_password' => 'nullable|min:8',
                'is_active' => 'nullable'
            ]);

            $user = VaivahikPanjiyan::findOrFail($id);

            $updateData = [
                'owner_email' => $request->owner_email,
                'is_active' => $request->input('is_active', 0) == 1
            ];

            if ($request->filled('owner_password')) {
                $updateData['owner_password'] = Hash::make($request->owner_password);
            }

            $user->update($updateData);

            \Log::info('Vaivahik login updated successfully', [
                'id' => $id,
                'email' => $request->owner_email
            ]);

            return redirect()->back()->with('success', 'लॉगिन जानकारी सफलतापूर्वक अपडेट की गई।');
        } catch (\Exception $e) {
            \Log::error('Vaivahik login update failed', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return redirect()->back()->with('error', 'लॉगिन जानकारी अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }

    /**
     * Delete vaivahik user account (keep profile)
     */
    public function destroyVaivahikUser($id)
    {
        $user = VaivahikPanjiyan::whereNotNull('owner_email')->findOrFail($id);

        // Remove login credentials but keep profile
        $user->update([
            'owner_email' => null,
            'owner_password' => null,
            'is_active' => false,
            'status' => VaivahikPanjiyan::STATUS_PENDING
        ]);

        return redirect()->route('admin.vaivahik-users.index')
            ->with('success', 'वैवाहिक उपयोगकर्ता खाता हटाया गया। प्रोफाइल सुरक्षित है।');
    }

    /**
     * Update member login information
     */
    public function updateMemberLogin(Request $request, $id)
    {
        try {
            $request->validate([
                'mobile_number' => 'required|string|size:10|unique:memberships,mobile_number,' . $id . ',id',
                'password' => 'nullable|min:8'
            ]);

            $member = Membership::findOrFail($id);

            $updateData = [
                'mobile_number' => $request->mobile_number
            ];

            if ($request->filled('password')) {
                // Let the model mutator handle the hashing to avoid double hashing
                $updateData['password'] = $request->password;
            }

            $member->update($updateData);

            \Log::info('Member login updated', [
                'member_id' => $id,
                'mobile_number' => $request->mobile_number,
                'password_updated' => $request->filled('password')
            ]);

            return redirect()->back()->with('success', 'सदस्य लॉगिन जानकारी सफलतापूर्वक अपडेट की गई।');
        } catch (\Exception $e) {
            \Log::error('Member login update failed', [
                'member_id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return redirect()->back()->with('error', 'लॉगिन जानकारी अपडेट करने में त्रुटि: ' . $e->getMessage());
        }
    }
}
