@extends('layouts.app')

@section('title', 'सदस्य बनने की प्रक्रिया')

@section('content')
<div class="bg-gradient-to-b from-white to-gray-50 min-h-screen">
    <div class="container-custom py-16">
        <!-- Page Header -->
        <div class="text-center mb-16">
            <div class="inline-block mx-auto mb-3">
                <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">सदस्य बनने की प्रक्रिया</h1>
            <p class="text-gray-600 max-w-2xl mx-auto">हमारे समाज का सदस्य बनने के लिए निम्नलिखित प्रक्रिया का पालन करें</p>
        </div>

        <!-- Process Steps -->
        <div class="max-w-4xl mx-auto">
            <!-- Step 1 -->
            <div class="bg-white rounded-xl shadow-md p-8 mb-8 relative hover:shadow-lg transition-shadow">
                <div class="absolute -left-4 top-8 w-8 h-8 bg-saffron-500 rounded-full flex items-center justify-center text-white font-bold shadow-md">1</div>
                <h3 class="text-xl font-semibold text-navy-900 mb-4">आवेदन पत्र भरें</h3>
                <p class="text-gray-600 mb-4">सदस्यता के लिए आवेदन पत्र डाउनलोड करें या ऑनलाइन भरें। आवेदन पत्र में सभी आवश्यक जानकारी सही-सही भरें।</p>
                <a href="/sadasya/aavedan" class="btn-primary inline-flex items-center">
                    <span>आवेदन पत्र भरें</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </a>
            </div>

            <!-- Step 2 -->
            <div class="bg-white rounded-xl shadow-md p-8 mb-8 relative hover:shadow-lg transition-shadow">
                <div class="absolute -left-4 top-8 w-8 h-8 bg-saffron-500 rounded-full flex items-center justify-center text-white font-bold shadow-md">2</div>
                <h3 class="text-xl font-semibold text-navy-900 mb-4">आवश्यक दस्तावेज़ जमा करें</h3>
                <p class="text-gray-600 mb-4">निम्नलिखित दस्तावेज़ों की प्रतियां जमा करें:</p>
                <ul class="list-disc list-inside text-gray-600 space-y-2 mb-4">
                    <li>आधार कार्ड</li>
                    <li>पैन कार्ड</li>
                    <li>पासपोर्ट साइज़ फोटो (2 प्रतियां)</li>
                    <li>निवास प्रमाण पत्र</li>
                </ul>
            </div>

            <!-- Step 3 -->
            <div class="bg-white rounded-xl shadow-md p-8 mb-8 relative hover:shadow-lg transition-shadow">
                <div class="absolute -left-4 top-8 w-8 h-8 bg-saffron-500 rounded-full flex items-center justify-center text-white font-bold shadow-md">3</div>
                <h3 class="text-xl font-semibold text-navy-900 mb-4">सदस्यता शुल्क का भुगतान</h3>
                <p class="text-gray-600 mb-4">निम्नलिखित शुल्क का भुगतान करें:</p>
                <div class="bg-gray-50 rounded-lg p-6 mb-4 shadow-inner">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <p class="text-gray-600">संरक्षक सदस्यता</p>
                            <p class="text-2xl font-semibold text-navy-900">₹10,000</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <p class="text-gray-600">आजीवन सदस्यता</p>
                            <p class="text-2xl font-semibold text-navy-900">₹5,000</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                            <p class="text-gray-600">वार्षिक सदस्यता</p>
                            <p class="text-2xl font-semibold text-navy-900">₹1,200</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4 -->
            <div class="bg-white rounded-xl shadow-md p-8 relative hover:shadow-lg transition-shadow">
                <div class="absolute -left-4 top-8 w-8 h-8 bg-saffron-500 rounded-full flex items-center justify-center text-white font-bold shadow-md">4</div>
                <h3 class="text-xl font-semibold text-navy-900 mb-4">सदस्यता की पुष्टि</h3>
                <p class="text-gray-600 mb-4">आपके आवेदन की जांच के बाद, आपको सदस्यता की पुष्टि के लिए एक ईमेल और एसएमएस भेजा जाएगा। सदस्यता कार्ड आपके पंजीकृत पते पर भेज दिया जाएगा।</p>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="max-w-4xl mx-auto mt-16 bg-white rounded-xl shadow-md p-8 hover:shadow-lg transition-shadow">
            <h3 class="text-xl font-semibold text-navy-900 mb-4">संपर्क करें</h3>
            <p class="text-gray-600 mb-4">सदस्यता से संबंधित किसी भी प्रश्न के लिए, कृपया निम्नलिखित विवरणों पर संपर्क करें:</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-start bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <div>
                        <p class="font-medium text-navy-900">फोन नंबर</p>
                        <p class="text-gray-600">{{ config('constants.MOBILE') }}</p>
                    </div>
                </div>
                <div class="flex items-start bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-saffron-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                        <p class="font-medium text-navy-900">ईमेल</p>
                        <p class="text-gray-600">{{ config('constants.EMAIL') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .btn-primary {
        @apply inline-flex items-center justify-center px-6 py-3 bg-saffron-500 text-white rounded-lg hover:bg-saffron-600 focus:ring-4 focus:ring-saffron-500 focus:ring-opacity-50 transition-colors shadow-md;
    }

    .shadow-soft {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.05);
    }

    .shadow-inner {
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .shadow-md {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .shadow-sm {
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .shadow-lg {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>
@endpush
@endsection 