<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('darshanik_ishthal', function (Blueprint $table) {
            // Make fields nullable except submitted_by_name and submitted_by_mobile
            $table->string('place_name')->nullable()->change();
            $table->string('guide_name')->nullable()->change();
            $table->string('guide_mobile', 10)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('darshanik_ishthal', function (Blueprint $table) {
            // Revert fields back to not nullable
            $table->string('place_name')->nullable(false)->change();
            $table->string('guide_name')->nullable(false)->change();
            $table->string('guide_mobile', 10)->nullable(false)->change();
        });
    }
};
