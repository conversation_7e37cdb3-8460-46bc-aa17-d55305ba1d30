@extends('layouts.admin') @section('title', 'यादव व्यापार ग्राहक संपादित करें') @section('content') <div class="p-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"> <div> <p class="text-2xl font-bold admin-text-secondary">यादव व्यापार ग्राहक संपादित करें</p> <p class="admin-text-secondary mt-1">{{ $yadavVyapar->naam }} की जानकारी</p> </div> <a href="{{ route('admin.yadav-vyapar-grahak.show', $yadavVyapar->id) }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-arrow-left"></i> वापस जाएं </a> </div> <!-- Edit Form --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary">रिकॉर्ड की जानकारी संपादित करें</h3> </div> <div class="p-6"> <form action="{{ route('admin.yadav-vyapar-grahak.update', $yadavVyapar->id) }}" method="POST" enctype="multipart/form-data" class="space-y-6"> @csrf @method('PUT') <!-- Display validation errors --> @if ($errors->any()) <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"> <div class="flex"> <div class="flex-shrink-0"> <i class="fas fa-exclamation-triangle text-red-400"></i> </div> <div class="ml-3"> <h3 class="text-sm font-medium text-red-800">कृपया निम्नलिखित त्रुटियों को ठीक करें:</h3> <div class="mt-2 text-sm text-red-700"> <ul class="list-disc list-inside space-y-1"> @foreach ($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> </div> </div> </div> @endif <!-- Category and Membership Information --> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label for="category" class="block text-sm font-medium admin-text-secondary mb-2">श्रेणी *</label> <select name="category" id="category" class="admin-form-input w-full @error('category') border-red-500 @enderror" required> <option value="">श्रेणी चुनें</option> @foreach($categories as $key => $value) <option value="{{ $key }}" {{ old('category', $yadavVyapar->category) == $key ? 'selected' : '' }}>{{ $value }}</option> @endforeach </select> @error('category') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="sadasyata_kramank" class="block text-sm font-medium admin-text-secondary mb-2">सदस्यता क्रमांक *</label> <input type="text" name="sadasyata_kramank" id="sadasyata_kramank" class="admin-form-input w-full @error('sadasyata_kramank') border-red-500 @enderror" value="{{ old('sadasyata_kramank', $yadavVyapar->sadasyata_kramank) }}" required> @error('sadasyata_kramank') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> </div> <!-- Sadasyata Prakar --> <div> <label for="sadasyata_prakar" class="block text-sm font-medium admin-text-secondary mb-2">सदस्यता प्रकार *</label> <input type="text" name="sadasyata_prakar" id="sadasyata_prakar" class="admin-form-input w-full @error('sadasyata_prakar') border-red-500 @enderror" value="{{ old('sadasyata_prakar', $yadavVyapar->sadasyata_prakar) }}" required> @error('sadasyata_prakar') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <!-- Personal Information --> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label for="naam" class="block text-sm font-medium admin-text-secondary mb-2">नाम *</label> <input type="text" name="naam" id="naam" class="admin-form-input w-full @error('naam') border-red-500 @enderror" value="{{ old('naam', $yadavVyapar->naam) }}" required> @error('naam') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="ajivika" class="block text-sm font-medium admin-text-secondary mb-2">आजीविका *</label> <input type="text" name="ajivika" id="ajivika" class="admin-form-input w-full @error('ajivika') border-red-500 @enderror" value="{{ old('ajivika', $yadavVyapar->ajivika) }}" required> @error('ajivika') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="mobile_number" class="block text-sm font-medium admin-text-secondary mb-2">मोबाइल नंबर *</label> <input type="tel" name="mobile_number" id="mobile_number" class="admin-form-input w-full @error('mobile_number') border-red-500 @enderror" value="{{ old('mobile_number', $yadavVyapar->mobile_number) }}" pattern="[6-9][0-9]{9}" maxlength="10" required> @error('mobile_number') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="ward" class="block text-sm font-medium admin-text-secondary mb-2">वार्ड *</label> <input type="text" name="ward" id="ward" class="admin-form-input w-full @error('ward') border-red-500 @enderror" value="{{ old('ward', $yadavVyapar->ward) }}" required> @error('ward') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="vikaskhand" class="block text-sm font-medium admin-text-secondary mb-2">विकासखंड *</label> <input type="text" name="vikaskhand" id="vikaskhand" class="admin-form-input w-full @error('vikaskhand') border-red-500 @enderror" value="{{ old('vikaskhand', $yadavVyapar->vikaskhand) }}" required> @error('vikaskhand') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <div> <label for="jila" class="block text-sm font-medium admin-text-secondary mb-2">जिला *</label> <input type="text" name="jila" id="jila" class="admin-form-input w-full @error('jila') border-red-500 @enderror" value="{{ old('jila', $yadavVyapar->jila) }}" required> @error('jila') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> </div> <!-- Address --> <div> <label for="address" class="block text-sm font-medium admin-text-secondary mb-2">पता *</label> <textarea name="address" id="address" rows="3" class="admin-form-input w-full @error('address') border-red-500 @enderror" required>{{ old('address', $yadavVyapar->address) }}</textarea> @error('address') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <!-- Google Maps Link --> <div> <label for="gmap_link" class="block text-sm font-medium admin-text-secondary mb-2">गूगल मैप लिंक (वैकल्पिक)</label> <input type="url" name="gmap_link" id="gmap_link" class="admin-form-input w-full @error('gmap_link') border-red-500 @enderror" value="{{ old('gmap_link', $yadavVyapar->gmap_link) }}" placeholder="https://maps.google.com/..."> <div class="mt-2 text-sm text-gray-600"> गूगल मैप लिंक या निर्देशांक (coordinates) </div> @error('gmap_link') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <!-- Current Photo --> @if($yadavVyapar->photo) <div class="bg-blue-50 p-4 rounded-lg"> <h4 class="text-sm font-medium admin-text-secondary mb-2">वर्तमान फोटो</h4> <div class="flex items-center justify-between"> <div class="flex items-center"> <img src="{{ $yadavVyapar->photo_url }}" alt="{{ $yadavVyapar->naam }}" class="w-16 h-16 object-cover rounded-lg mr-3 border"> <span class="text-sm admin-text-primary">{{ $yadavVyapar->photo_file_name }}</span> </div> <a href="{{ route('admin.yadav-vyapar-grahak.download-photo', $yadavVyapar->id) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"> <i class="fas fa-download mr-1"></i>डाउनलोड </a> </div> </div> @endif <!-- New Photo Upload --> <div> <label for="photo" class="block text-sm font-medium admin-text-secondary mb-2"> नई फोटो अपलोड करें (वैकल्पिक) </label> <input type="file" name="photo" id="photo" class="admin-form-input w-full @error('photo') border-red-500 @enderror" accept="image/jpeg,image/jpg,image/png"> <div class="mt-2 text-sm text-gray-600"> समर्थित फ़ाइल प्रकार: JPG, JPEG, PNG | अधिकतम आकार: 2MB </div> @error('photo') <p class="text-red-500 text-xs mt-1">{{ $message }}</p> @enderror </div> <!-- Submit Buttons --> <div class="flex flex-col sm:flex-row gap-4 justify-end"> <a href="{{ route('admin.yadav-vyapar-grahak.show', $yadavVyapar->id) }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 text-center"> रद्द करें </a> <button type="submit" class="bg-saffron-500 hover:bg-saffron-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"> अपडेट करें </button> </div> </form> </div> </div> </div> @endsection @push('scripts') <script> document.addEventListener('DOMContentLoaded', function() { // Mobile number validation const mobileInput = document.getElementById('mobile_number'); if (mobileInput) { mobileInput.addEventListener('input', function(e) { let value = e.target.value.replace(/\D/g, ''); if (value.length > 10) { value = value.slice(0, 10); } e.target.value = value; }); } }); </script> @endpush 