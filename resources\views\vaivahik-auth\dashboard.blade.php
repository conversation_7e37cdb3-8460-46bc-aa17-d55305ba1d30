@extends('layouts.app')

@section('title', 'वैवाहिक पंजीयन डैशबोर्ड')

@section('content')
    <div class="min-h-screen bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="flex items-center space-x-4 mb-4 md:mb-0">
                        @if($user->photo1)
                            <img src="{{ asset('storage/' . $user->photo1) }}" alt="{{ $user->naam }}"
                                 class="w-16 h-16 rounded-full object-cover border-4 border-pink-200">
                        @else
                            <div class="w-16 h-16 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-xl">{{ substr($user->naam, 0, 1) }}</span>
                            </div>
                        @endif
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">स्वागत, {{ $user->naam }}</h1>
                            <p class="text-gray-600">{{ $user->owner_email }}</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {{ $user->status === 'approved' ? 'bg-green-100 text-green-800' : 
                                   ($user->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ $user->status === 'approved' ? 'स्वीकृत' : ($user->status === 'pending' ? 'प्रतीक्षा में' : 'अस्वीकृत') }}
                            </span>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <a href="{{ route('vaivahik.edit-profile') }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            प्रोफाइल संपादित करें
                        </a>
                        <form method="POST" action="{{ route('vaivahik.logout') }}" class="inline">
                            @csrf
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                </svg>
                                लॉग आउट
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">प्राप्त अनुरोध</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['received_requests'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">प्रतीक्षा में</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['pending_requests'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">स्वीकृत अनुरोध</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['accepted_requests'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-pink-100">
                            <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">भेजे गए अनुरोध</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['sent_requests'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Received Requests -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">प्राप्त रुचि अनुरोध</h2>
                        <a href="{{ route('vaivahik.received-requests') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            सभी देखें →
                        </a>
                    </div>
                    
                    @if($stats['received_requests'] > 0)
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $stats['pending_requests'] }} नए अनुरोध</p>
                                    <p class="text-sm text-gray-600">आपकी समीक्षा की प्रतीक्षा में</p>
                                </div>
                                <a href="{{ route('vaivahik.received-requests') }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                    देखें
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"/>
                            </svg>
                            <p class="mt-2 text-sm text-gray-600">अभी तक कोई रुचि अनुरोध नहीं मिला</p>
                        </div>
                    @endif
                </div>

                <!-- Sent Requests -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">भेजे गए अनुरोध</h2>
                        <a href="{{ route('vaivahik.sent-requests') }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            सभी देखें →
                        </a>
                    </div>
                    
                    @if($stats['sent_requests'] > 0)
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $stats['sent_requests'] }} अनुरोध भेजे</p>
                                    <p class="text-sm text-gray-600">{{ $stats['accepted_requests'] }} स्वीकृत</p>
                                </div>
                                <a href="{{ route('vaivahik.sent-requests') }}" 
                                   class="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                    देखें
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                            <p class="mt-2 text-sm text-gray-600">अभी तक कोई रुचि अनुरोध नहीं भेजा</p>
                            <a href="{{ route('sadasya.vaivahik-panjiyan') }}" 
                               class="mt-2 inline-block text-blue-600 hover:text-blue-800 text-sm font-medium">
                                प्रोफाइल देखें →
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Profile Status -->
            @if($user->status !== 'approved')
                <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-yellow-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <h3 class="text-lg font-medium text-yellow-800">प्रोफाइल स्वीकृति प्रतीक्षा में</h3>
                            <p class="text-yellow-700">आपकी प्रोफाइल की समीक्षा की जा रही है। स्वीकृति के बाद आप सभी सुविधाओं का उपयोग कर सकेंगे।</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
