<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NewsImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'news_id',
        'image_path',
        'alt_text',
        'caption',
        'sort_order',
        'is_featured',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_featured' => 'boolean',
    ];

    /**
     * Get the news item that owns this image.
     */
    public function news(): BelongsTo
    {
        return $this->belongsTo(News::class);
    }

    /**
     * Get the full URL for the image.
     */
    public function getImageUrl(): string
    {
        // If the path already starts with /storage, use it directly with asset()
        if (str_starts_with($this->image_path, '/storage/')) {
            return url($this->image_path);
        }

        // Otherwise, treat it as a relative path
        return asset($this->image_path);
    }

    /**
     * Scope to get images ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * Scope to get featured images.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
}
