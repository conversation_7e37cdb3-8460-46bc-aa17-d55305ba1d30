<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OrganizationDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class OrganizationDocumentController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $documents = OrganizationDocument::orderBy('display_order')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.organization-documents.index', compact('documents'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.organization-documents.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'file' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png|max:10240', // 10MB max
                'display_order' => 'nullable|integer',
            ]);

            // Handle file upload
            if ($request->hasFile('file') && $request->file('file')->isValid()) {
                $file = $request->file('file');
                $originalName = $file->getClientOriginalName();
                $fileName = time() . '_' . Str::slug(pathinfo($originalName, PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();

                // Store file in public/documents directory
                $filePath = $file->storeAs('public/documents', $fileName);

                // Create document record
                $data = $validated;
                $data['file_path'] = $filePath;
                $data['file_name'] = $originalName;
                $data['file_type'] = $file->getClientOriginalExtension();
                $data['file_size'] = $file->getSize();
                $data['is_published'] = $request->has('is_published') ? 1 : 0;

                OrganizationDocument::create($data);

                return redirect()
                    ->route('admin.organization-documents.index')
                    ->with('success', 'Document uploaded successfully');
            }

            return redirect()->back()
                ->withInput()
                ->withErrors(['file' => 'File upload failed. Please try again.']);

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to upload document: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $document = OrganizationDocument::findOrFail($id);
        return view('admin.organization-documents.show', compact('document'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $document = OrganizationDocument::findOrFail($id);
        return view('admin.organization-documents.edit', compact('document'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $document = OrganizationDocument::findOrFail($id);

            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'file' => 'nullable|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,txt,jpg,jpeg,png|max:10240', // 10MB max
                'display_order' => 'nullable|integer',
            ]);

            $data = $validated;
            $data['is_published'] = $request->has('is_published') ? 1 : 0;

            // Handle file upload if new file is provided
            if ($request->hasFile('file') && $request->file('file')->isValid()) {
                // Delete old file
                $document->deleteFile();

                $file = $request->file('file');
                $originalName = $file->getClientOriginalName();
                $fileName = time() . '_' . Str::slug(pathinfo($originalName, PATHINFO_FILENAME)) . '.' . $file->getClientOriginalExtension();

                // Store new file
                $filePath = $file->storeAs('public/documents', $fileName);

                $data['file_path'] = $filePath;
                $data['file_name'] = $originalName;
                $data['file_type'] = $file->getClientOriginalExtension();
                $data['file_size'] = $file->getSize();
            }

            $document->update($data);

            return redirect()
                ->route('admin.organization-documents.index')
                ->with('success', 'Document updated successfully');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update document: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $document = OrganizationDocument::findOrFail($id);
            $document->delete(); // This will also delete the file due to model boot method

            return redirect()
                ->route('admin.organization-documents.index')
                ->with('success', 'Document deleted successfully');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to delete document: ' . $e->getMessage()]);
        }
    }
}
