<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Mpdf\Mpdf;
use Mpdf\Output\Destination;

class PdfService
{
    /**
     * Generate membership card PDF using mPDF with better Hindi support
     */
    public function generateMembershipCard($member)
    {
        try {
            // Create mPDF instance with optimized settings for A4 single page
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'orientation' => 'P',
                'margin_left' => 12,
                'margin_right' => 12,
                'margin_top' => 12,
                'margin_bottom' => 12,
                'default_font_size' => 12,
                'default_font' => 'Arial Unicode MS',
                'autoScriptToLang' => true,
                'autoLangToFont' => true,
                'tempDir' => storage_path('app/temp')
            ]);

            // Set document properties
            $mpdf->SetTitle('सदस्यता कार्ड - ' . $member->name);
            $mpdf->SetAuthor('छत्तीसगढ़ यादव शासकीय सेवक समिति');

            // Generate HTML content
            $html = $this->generatePdfHTML($member);

            // Write HTML to PDF
            $mpdf->WriteHTML($html);

            return $mpdf;
        } catch (\Exception $e) {
            Log::error('mPDF Generation Error: ' . $e->getMessage());
            Log::error('mPDF Stack Trace: ' . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Download membership card PDF
     */
    public function downloadMembershipCard($member)
    {
        $mpdf = $this->generateMembershipCard($member);
        $filename = "membership-card-{$member->membership_number}-" . date('Y-m-d') . ".pdf";

        return $mpdf->Output($filename, Destination::DOWNLOAD);
    }

    /**
     * Generate HTML content for PDF download (uses file paths)
     */
    public function generatePdfHTML($member)
    {
        // Generate children information if available
        $childrenHtml = '';
        if ($member->children && count($member->children) > 0) {
            $childrenInfo = '';
            foreach ($member->children as $index => $child) {
                $childrenInfo .= ($child['name'] ?? 'N/A') . ' (' . ($child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') : 'N/A') . ')';
                if ($index < count($member->children) - 1) {
                    $childrenInfo .= ', ';
                }
            }
            $childrenHtml = '
                <div class="row">
                    <span class="label">बच्चों की जानकारी:</span>
                    <span class="value">' . $childrenInfo . '</span>
                </div>';
        }

        // Generate photo HTML if available (using file paths for PDF)
        $photoHtml = '';
        if ($member->photo && \Storage::disk('public')->exists($member->photo)) {
            $photoPath = storage_path('app/public/' . $member->photo);
            $photoHtml = '<img src="' . $photoPath . '" style="width: 120px; height: 150px; object-fit: cover; border: 2px solid #333;" alt="Member Photo">';
        } else {
            $photoHtml = '<div style="width: 120px; height: 150px; border: 2px solid #333; display: flex; align-items: center; justify-content: center; background: #f5f5f5; font-size: 11px; text-align: center; color: #666;">फोटो<br>उपलब्ध नहीं</div>';
        }

        // Generate signature HTML if available (using file paths for PDF)
        $signatureHtml = '';
        if ($member->signature && \Storage::disk('public')->exists($member->signature)) {
            $signaturePath = storage_path('app/public/' . $member->signature);
            $signatureHtml = '<img src="' . $signaturePath . '" style="width: 160px; height: 60px; object-fit: contain; border-bottom: 2px solid #333;" alt="Member Signature">';
        } else {
            $signatureHtml = '<div style="width: 160px; height: 60px; border-bottom: 2px solid #333; display: flex; align-items: center; justify-content: center; background: #f9f9f9; font-size: 10px; color: #666;">हस्ताक्षर</div>';
        }

        return $this->buildHTMLTemplate($member, $childrenHtml, $photoHtml, $signatureHtml);
    }

    /**
     * Generate HTML content for browser preview (uses asset URLs)
     */
    public function generateHTML($member)
    {
        // Generate children information if available
        $childrenHtml = '';
        if ($member->children && count($member->children) > 0) {
            $childrenInfo = '';
            foreach ($member->children as $index => $child) {
                $childrenInfo .= ($child['name'] ?? 'N/A') . ' (' . ($child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d/m/Y') : 'N/A') . ')';
                if ($index < count($member->children) - 1) {
                    $childrenInfo .= ', ';
                }
            }
            $childrenHtml = '
                <div class="row">
                    <span class="label">बच्चों की जानकारी:</span>
                    <span class="value">' . $childrenInfo . '</span>
                </div>';
        }

        // Generate photo HTML if available
        $photoHtml = '';
        if ($member->photo && \Storage::disk('public')->exists($member->photo)) {
            $photoUrl = asset('storage/' . $member->photo);
            $photoHtml = '<img src="' . $photoUrl . '" style="width: 120px; height: 150px; object-fit: cover; border: 2px solid #333;" alt="Member Photo">';
        } else {
            $photoHtml = '<div style="width: 120px; height: 150px; border: 2px solid #333; display: flex; align-items: center; justify-content: center; background: #f5f5f5; font-size: 11px; text-align: center; color: #666;">फोटो<br>उपलब्ध नहीं</div>';
        }


        // Generate signature HTML if available
        $signatureHtml = '';
        if ($member->signature && \Storage::disk('public')->exists($member->signature)) {
            $signatureUrl = asset('storage/' . $member->signature);
            $signatureHtml = '<img src="' . $signatureUrl . '" style="width: 160px; height: 60px; object-fit: contain; border-bottom: 2px solid #333;" alt="Member Signature">';
        } else {
            $signatureHtml = '<div style="width: 160px; height: 60px; border-bottom: 2px solid #333; display: flex; align-items: center; justify-content: center; background: #f9f9f9; font-size: 10px; color: #666;">हस्ताक्षर</div>';
        }


        return $this->buildHTMLTemplate($member, $childrenHtml, $photoHtml, $signatureHtml);
    }

    /**
     * Build HTML template with provided content
     */
    private function buildHTMLTemplate($member, $childrenHtml, $photoHtml, $signatureHtml)
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        @page { margin: 12mm; }
        body {
            font-family: "Arial Unicode MS", Arial, sans-serif;
            font-size: 13px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            color: #000;
        }
        .header {
            background: linear-gradient(135deg, #4a90a4 0%, #2c5f6f 100%);
            color: white;
            padding: 18px;
            text-align: center;
            margin-bottom: 18px;
            border-radius: 6px;
        }
        .org-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 6px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .sub-text {
            font-size: 14px;
            margin-bottom: 2px;
        }
        .content {
            padding: 12px;
        }
        .intro-section {
            margin-bottom: 12px;
            font-size: 13px;
        }
        .subject-line {
            font-weight: bold;
            margin-bottom: 12px;
            text-decoration: underline;
            font-size: 14px;
        }
        .row {
            margin-bottom: 6px;
            border-bottom: 1px dotted #333;
            padding-bottom: 3px;
            display: table;
            width: 100%;
        }
        .label {
            font-weight: bold;
            display: table-cell;
            width: 200px;
            vertical-align: top;
            padding-right: 8px;
            font-size: 13px;
        }
        .value {
            display: table-cell;
            vertical-align: top;
            font-size: 13px;
        }
        .section-title {
            font-weight: bold;
            text-align: center;
            margin: 15px 0 10px 0;
            text-decoration: underline;
            font-size: 15px;
        }
        .declaration {
            border: 2px solid #4a90a4;
            padding: 10px;
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .declaration-text {
            font-size: 12px;
            text-align: justify;
            line-height: 1.3;
        }
        .proposer-text {
            font-size: 12px;
            line-height: 1.3;
        }
        .signature-area {
            margin-top: 18px;
        }
        .sig-table {
            width: 100%;
            border-collapse: collapse;
        }
        .sig-cell {
            width: 50%;
            text-align: center;
            padding: 8px;
        }
        .sig-line {
            border-bottom: 2px solid #333;
            width: 160px;
            height: 30px;
            margin: 12px auto 4px auto;
        }
        .sig-label {
            font-size: 12px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 15px;
            border-top: 2px solid #4a90a4;
            padding-top: 12px;
            background: #f8f9fa;
            border-radius: 4px;
            line-height: 1.3;
        }
        .photo-signature-section {
            margin: 15px 0;
            border: 1px solid #333;
            padding: 10px;
            background: #fafafa;
        }
        .photo-signature-table {
            width: 100%;
            border-collapse: collapse;
        }
        .photo-cell {
            width: 30%;
            text-align: center;
            vertical-align: top;
            padding: 8px;
        }
        .details-cell {
            width: 70%;
            vertical-align: top;
            padding: 8px;
        }
        .photo-container {
            text-align: center;
            margin-bottom: 10px;
        }
        .signature-container {
            text-align: center;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="org-name">छत्तीसगढ़ यादव शासकीय सेवक समिति</div>
        <div class="sub-text">पं. क्रमांक. ' . $member->membership_number . ' दिनांक ' . $member->created_at->format('d.m.Y') . '</div>
        <div class="sub-text">पता : A-26, गायत्री नगर, रायपुर</div>
    </div>

    <!-- Photo and Basic Info Section -->
    <div class="photo-signature-section">
        <table class="photo-signature-table">
            <tr>
                <td class="photo-cell">
                    <div class="photo-container">
                        <div style="font-weight: bold; margin-bottom: 5px; font-size: 12px;">सदस्य फोटो</div>
                        ' . $photoHtml . '
                    </div>
                </td>
                <td class="details-cell">
                    <div style="font-weight: bold; margin-bottom: 8px; font-size: 14px; text-decoration: underline;">सदस्य विवरण</div>
                    <div style="font-size: 12px; line-height: 1.4;">
                        <strong>नाम:</strong> ' . $member->name . '<br>
                        <strong>पिता/पति:</strong> ' . $member->fathers_husband_name . '<br>
                        <strong>सदस्यता संख्या:</strong> ' . $member->membership_number . '<br>
                        <strong>मोबाइल:</strong> ' . $member->mobile_number . '<br>
                        <strong>स्थिति:</strong> ' . $member->getStatusDisplayText() . '<br>
                        <strong>सदस्यता प्रकार:</strong> ' . ucfirst($member->membership_type) . '
                    </div>
                </td>
            </tr>
        </table>
    </div>

    <!-- Content -->
    <div class="content">
        <div class="intro-section">
            <strong>प्रति,</strong><br>
            <span style="margin-left: 20px;">अध्यक्ष / सचिव</span><br>
            <span style="margin-left: 20px;">छ.ग यादव शासकीय सेवक समिति</span><br>
            <span style="margin-left: 20px;">गायत्री नगर रायपुर</span>
        </div>

        <div class="subject-line">
            विषय:- छ.ग. यादव शासकीय सेवक समिति की सदस्यता ग्रहण करने बाबत्।
        </div>

        <div class="intro-section">
            <strong>महोदय,</strong><br>
            निवेदन है कि मैं छत्तीसगढ़ / केन्द्र शासन के कार्मिक हूं मैं समिति की गतिविधियों एवं कार्य योजना से परिचित हूं। सदस्य बनकर यादव समाज की सेवा करना चाहता हूं।
        </div>

        <div class="intro-section">
            <strong>मेरी संपूर्ण जानकारी निम्न है:-</strong>
        </div>

        <!-- Member Details -->
        <div class="row">
            <span class="label">1. नाम/पिता का नाम:</span>
            <span class="value">' . $member->name . ' / ' . $member->fathers_husband_name . '</span>
        </div>

        <div class="row">
            <span class="label">2. पद/पदेश स्थान:</span>
            <span class="value">' . ($member->department_name ?? 'N/A') . '</span>
        </div>

        <div class="row">
            <span class="label">3. जन्म तिथि:</span>
            <span class="value">' . ($member->birth_date ? $member->birth_date->format('d/m/Y') : 'N/A') . '</span>
        </div>

        <div class="row">
            <span class="label">4. मोबाइल / वैवाहिक स्थिति:</span>
            <span class="value">' . $member->mobile_number . ' / ' . ($member->marital_status ?? 'N/A') . '</span>
        </div>

        <div class="row">
            <span class="label">5. संस्थान/कार्यालय:</span>
            <span class="value">' . ($member->office ?? 'N/A') . '</span>
        </div>

        <div class="row">
            <span class="label">6. सदस्य का संपूर्ण पता:</span>
            <span class="value">' . ($member->address ?? 'N/A') . '</span>
        </div>

        <div class="row">
            <span class="label">7. सदस्य बनने का आधार:</span>
            <span class="value">' . ($member->caste_details ?? 'यादव समुदाय') . '</span>
        </div>

        ' . $childrenHtml . '

        <!-- Declaration -->
        <div class="declaration">
            <div class="section-title">घोषणा पत्र</div>
            <div class="declaration-text">
                मैं घोषणा करता/करती हूं कि उपरोक्त जानकारी सत्य है। असत्य पाये जाने की स्थिति में मेरी सदस्यता समाप्त करने का अधिकार प्रबंधकारिणी समिति का होगा।
            </div>
        </div>

        <!-- Proposer Section -->
        <div class="declaration">
            <div class="section-title">प्रस्तावक सदस्य</div>
            <div class="proposer-text">
                <strong>सदस्य का नाम:</strong> ' . ($member->member_name_signature ?? 'N/A') . '<br>
                <strong>पदनाम:</strong> ' . ($member->vibhag ?? 'N/A') . '<br>
                <strong>कार्यालय पता:</strong> ' . ($member->proposer_address ?? 'N/A') . '
            </div>
        </div>

        <!-- Signature Section -->
        <div class="signature-area">
            <table class="sig-table">
                <tr>
                    <td class="sig-cell">
                        <div style="margin-bottom: 8px;">
                            ' . $signatureHtml . '
                        </div>
                        <div class="sig-label">सदस्य हस्ताक्षर</div>
                    </td>
                    <td class="sig-cell">
                        <div class="sig-line"></div>
                        <div class="sig-label">सचिव हस्ताक्षर</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <strong>यह एक आधिकारिक दस्तावेज है। कृपया इसे सुरक्षित रखें।</strong><br>
            छत्तीसगढ़ यादव शासकीय सेवक समिति | जेनरेट किया गया: ' . now()->format('d M Y, h:i A') . '<br>
            सदस्यता संख्या: ' . $member->membership_number . ' | स्थिति: ' . $member->getStatusDisplayText() . '
        </div>
    </div>
</body>
</html>';
    }
}
