<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Models\Membership;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Show the member login form.
     */
    public function showLoginForm(Request $request)
    {
        // If there's an intended URL parameter, store it in the session
        if ($request->has('intended')) {
            $intendedUrl = $request->get('intended');
            Log::info('Intended URL received: ' . $intendedUrl);
            // For debugging, let's temporarily accept any URL that starts with our domain
            if (str_starts_with($intendedUrl, config('app.url')) || str_starts_with($intendedUrl, 'http://127.0.0.1:8000')) {
                session(['url.intended' => $intendedUrl]);
                Log::info('Intended URL stored in session: ' . $intendedUrl);
            } else {
                Log::info('Intended URL rejected: ' . $intendedUrl);
            }
        }

        return view('member.auth.login');
    }

    /**
     * Handle member login request.
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        // Find member by email
        $member = Membership::where('email', $credentials['email'])->first();

        if (!$member) {
            throw ValidationException::withMessages([
                'email' => ['इस ईमेल से कोई सदस्य नहीं मिला।'],
            ]);
        }

        // Check if member has a password set
        if (!$member->password) {
            throw ValidationException::withMessages([
                'password' => ['आपका पासवर्ड सेट नहीं है। कृपया व्यवस्थापक से संपर्क करें।'],
            ]);
        }

        // Verify password
        if (!Hash::check($credentials['password'], $member->password)) {
            throw ValidationException::withMessages([
                'password' => ['गलत पासवर्ड।'],
            ]);
        }

        // Check if membership is approved
        if (!$member->isApproved()) {
            throw ValidationException::withMessages([
                'email' => ['आपका सदस्यता आवेदन अभी भी स्वीकृति की प्रतीक्षा में है।'],
            ]);
        }

        Auth::guard('member')->login($member, $request->boolean('remember'));

        $request->session()->regenerate();

        return redirect()->intended(route('member.dashboard'))
            ->with('success', 'सफलतापूर्वक लॉगिन हो गए। स्वागत है, ' . $member->name . '!');
    }



    /**
     * Handle member logout request.
     */
    public function logout(Request $request)
    {
        Auth::guard('member')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')
            ->with('success', 'सफलतापूर्वक लॉगआउट हो गए। धन्यवाद!');
    }
}
