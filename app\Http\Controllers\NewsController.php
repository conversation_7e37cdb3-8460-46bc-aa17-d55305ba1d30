<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * Display the main news listing page.
     */
    public function index()
    {
        $recentNews = News::where('is_published', true)
            ->orderBy('publication_date', 'desc')
            ->take(10)
            ->get();

        $featured = News::getFeaturedNews();

        // Add category labels for the view
        $categoryLabels = $this->getAllCategoryLabels();

        return view('news.index', compact('recentNews', 'featured', 'categoryLabels'));
    }

    /**
     * Get all category labels.
     */
    private function getAllCategoryLabels()
    {
        return [
            News::CATEGORY_GENERAL => 'सामान्य',
            News::CATEGORY_MEETINGS => 'बैठकें',
            News::CATEGORY_OBITUARIES => 'श्रद्धांजलि',
            News::CATEGORY_APPOINTMENTS => 'नियुक्तियां',
            News::CATEGORY_PRESS => 'प्रेस विज्ञप्ति',
            News::CATEGORY_MEDIA => 'मीडिया कवरेज'
        ];
    }
    
    /**
     * Display general news.
     */
    public function general(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_GENERAL, $request);
    }

    /**
     * Display meetings news.
     */
    public function meetings(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_MEETINGS, $request);
    }

    /**
     * Display obituaries.
     */
    public function obituaries(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_OBITUARIES, $request);
    }

    /**
     * Display appointments.
     */
    public function appointments(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_APPOINTMENTS, $request);
    }

    /**
     * Display press releases.
     */
    public function press(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_PRESS, $request);
    }

    /**
     * Display media coverage.
     */
    public function media(Request $request)
    {
        return $this->showCategoryNews(News::CATEGORY_MEDIA, $request);
    }

    /**
     * Helper method to display news by category using unified template.
     */
    private function showCategoryNews($category, Request $request)
    {
        $year = $request->input('year');
        $news = News::getByCategory($category, $year);

        // Get category information
        $categoryInfo = $this->getCategoryInfo($category);

        // Get related categories (all except current)
        $relatedCategories = $this->getRelatedCategories($category);

        return view('news.category', array_merge($categoryInfo, [
            'news' => $news,
            'category' => $category,
            'relatedCategories' => $relatedCategories
        ]));
    }

    /**
     * Get category-specific information.
     */
    private function getCategoryInfo($category)
    {
        $categoryMap = [
            News::CATEGORY_GENERAL => [
                'categoryTitle' => 'सामान्य समाचार',
                'categoryLabel' => 'सामान्य',
                'categoryDescription' => 'हमारे समुदाय के सामान्य समाचार और घटनाक्रम।'
            ],
            News::CATEGORY_MEETINGS => [
                'categoryTitle' => 'बैठक समाचार',
                'categoryLabel' => 'बैठकें',
                'categoryDescription' => 'आगामी बैठकों और सभाओं की जानकारी।'
            ],
            News::CATEGORY_OBITUARIES => [
                'categoryTitle' => 'श्रद्धांजलि',
                'categoryLabel' => 'श्रद्धांजलि',
                'categoryDescription' => 'हमारे समुदाय के दिवंगत सदस्यों को श्रद्धांजलि।'
            ],
            News::CATEGORY_APPOINTMENTS => [
                'categoryTitle' => 'नियुक्ति समाचार',
                'categoryLabel' => 'नियुक्तियां',
                'categoryDescription' => 'नई नियुक्तियों और पदोन्नति की जानकारी।'
            ],
            News::CATEGORY_PRESS => [
                'categoryTitle' => 'प्रेस विज्ञप्ति',
                'categoryLabel' => 'प्रेस विज्ञप्ति',
                'categoryDescription' => 'आधिकारिक प्रेस विज्ञप्ति और घोषणाएं।'
            ],
            News::CATEGORY_MEDIA => [
                'categoryTitle' => 'मीडिया कवरेज',
                'categoryLabel' => 'मीडिया कवरेज',
                'categoryDescription' => 'मीडिया में हमारे समुदाय की कवरेज।'
            ]
        ];

        return $categoryMap[$category] ?? [
            'categoryTitle' => ucfirst($category) . ' News',
            'categoryLabel' => ucfirst($category),
            'categoryDescription' => 'Latest ' . $category . ' news and updates.'
        ];
    }

    /**
     * Get related categories for navigation.
     */
    private function getRelatedCategories($currentCategory)
    {
        $allCategories = [
            News::CATEGORY_GENERAL => 'सामान्य',
            News::CATEGORY_MEETINGS => 'बैठकें',
            News::CATEGORY_OBITUARIES => 'श्रद्धांजलि',
            News::CATEGORY_APPOINTMENTS => 'नियुक्तियां',
            News::CATEGORY_PRESS => 'प्रेस विज्ञप्ति',
            News::CATEGORY_MEDIA => 'मीडिया कवरेज'
        ];

        // Remove current category from related categories
        unset($allCategories[$currentCategory]);

        return $allCategories;
    }
    
    /**
     * Display a single news item.
     */
    public function show($id)
    {
        $news = News::findOrFail($id);
        
        if (!$news->is_published) {
            abort(404);
        }
        
        return view('news.show', compact('news'));
    }
} 