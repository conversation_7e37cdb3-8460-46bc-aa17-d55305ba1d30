@import url('https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=Noto+Sans+Devanagari:wght@400;500;600;700;800&display=swap');
@import './family-tree.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-hindi text-gray-800 antialiased;
  }
  
  /* New CSS for mobile menu */
  #mobile-menu {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  #mobile-menu.active {
    transform: translateX(0);
  }
  
  #mobile-overlay {
    transition: opacity 0.3s ease;
  }
  
  #mobile-overlay.hidden {
    opacity: 0;
    pointer-events: none;
  }
  
  #mobile-overlay:not(.hidden) {
    opacity: 1;
  }
  
  /* Animations for mobile menu items */
  .animate-slide-in {
    animation: slideIn 0.4s ease-out forwards;
    animation-fill-mode: forwards;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
    animation-fill-mode: forwards;
  }
  
  /* End of new CSS */
  
  /* Only apply heading styles to frontend, not admin */
  .frontend-content h1,
  .frontend-content h2,
  .frontend-content h3,
  .frontend-content h4,
  .frontend-content h5,
  .frontend-content h6 {
    @apply font-headings text-navy-900 font-semibold;
  }

  .frontend-content h1 {
    @apply text-3xl md:text-4xl lg:text-5xl leading-tight;
  }

  .frontend-content h2 {
    @apply text-2xl md:text-3xl leading-snug;
  }

  .frontend-content h3 {
    @apply text-xl md:text-2xl;
  }

  .frontend-content p {
    @apply leading-relaxed mb-4 text-gray-700;
  }

  .frontend-content ul {
    @apply list-disc pl-6 mb-4 space-y-2;
  }

  .frontend-content ol {
    @apply list-decimal pl-6 mb-4 space-y-2;
  }

  .frontend-content li {
    @apply text-gray-700 leading-relaxed;
  }

  .frontend-content strong {
    @apply font-semibold text-navy-800;
  }

  .frontend-content .bg-blue-50 {
    @apply border-l-4 border-blue-400;
  }

  .frontend-content .bg-green-50 {
    @apply border-l-4 border-green-400;
  }

  .frontend-content .bg-yellow-50 {
    @apply border-l-4 border-yellow-400;
  }

  /* Improved focus styles for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-saffron-500 ring-offset-2;
  }
}

/* Custom styles for Yadav Samaj */
@layer components {
    /* Typography & Content Styles */
    .section-title {
        @apply font-headings text-2xl md:text-3xl font-bold text-navy-800 mb-8 relative;
    }

    .section-title::after {
        content: "";
        @apply absolute bottom-0 left-0 w-20 h-1 bg-saffron-500 rounded-full;
        transform: translateY(0.75rem);
    }
    
    .section-title-centered {
        @apply font-headings text-2xl md:text-3xl font-bold text-navy-800 mb-10 relative text-center;
    }

    .section-title-centered::after {
        content: "";
        @apply absolute bottom-0 left-1/2 w-20 h-1 bg-saffron-500 rounded-full;
        transform: translate(-50%, 0.75rem);
    }

    .section-title-ornate {
        @apply font-headings text-2xl md:text-3xl font-bold text-navy-800 mb-10 relative text-center;
    }

    .section-title-ornate::before,
    .section-title-ornate::after {
        content: "•";
        @apply text-saffron-500 mx-4 text-2xl inline-block;
    }
    
    /* UI Elements */
    .btn {
        @apply inline-flex items-center justify-center px-5 py-2.5 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    
    .btn-primary {
        @apply btn bg-navy-800 text-white hover:bg-navy-700 focus:ring-navy-500 shadow-md hover:shadow-lg;
    }
    
    .btn-secondary {
        @apply btn bg-saffron-500 text-white hover:bg-saffron-400 focus:ring-saffron-300 shadow-md hover:shadow-lg;
    }

    .btn-tertiary {
        @apply btn bg-green-600 text-white hover:bg-green-500 focus:ring-green-400 shadow-md hover:shadow-lg;
    }
    
    .btn-outline {
        @apply btn border-2 border-navy-800 text-navy-800 hover:bg-navy-50 focus:ring-navy-500;
    }

    .btn-outline-saffron {
        @apply btn border-2 border-saffron-500 text-saffron-600 hover:bg-saffron-50 focus:ring-saffron-300;
    }
    
    .btn-sm {
        @apply px-3 py-1.5 text-sm;
    }
    
    .btn-lg {
        @apply px-6 py-3 text-lg;
    }

    .btn-with-icon {
        @apply flex items-center justify-center space-x-2;
    }

    .btn-with-icon svg {
        @apply transition-transform duration-300;
    }

    .btn-with-icon:hover svg {
        @apply transform translate-x-1;
    }
    
    /* Cards & Containers */
    .card {
        @apply bg-white rounded-xl shadow-card transition-all duration-300 overflow-hidden hover:shadow-lg;
    }

    .card-bordered {
        @apply card border border-gray-100;
    }
    
    .card-hover {
        @apply hover:translate-y-[-5px] transition-transform duration-300;
    }

    .card-accent {
        @apply card border-t-4 border-t-saffron-500;
    }

    .card-accent-green {
        @apply card border-t-4 border-t-green-500;
    }

    .card-accent-navy {
        @apply card border-t-4 border-t-navy-500;
    }
    
    .container-custom {
        @apply container mx-auto px-4 md:px-6 lg:px-8;
    }
    
    /* Enhanced Navbar Header */
    .navbar-header {
        transition: all 0.3s ease-in-out;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
        border-bottom: 2px solid #e5e7eb;
    }

    .navbar-header:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #f8fafc 100%);
        border-bottom-color: #ff9933;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    /* Navigation Elements - Enhanced Government Style with Better Visibility */
    .nav-link {
        @apply text-navy-700 font-medium hover:text-saffron-600 transition-all duration-300 relative py-2.5 px-3;
        border-bottom: 3px solid transparent;
        font-size: 0.85rem;
        white-space: nowrap;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
        border-radius: 6px;
        margin: 0 1px;
        border-left: none;
        border-right: none;
        border-top: none;
        cursor: pointer;
        position: relative;
        line-height: 1.2;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .nav-link:hover {
        @apply text-saffron-600;
        border-bottom-color: #ff9933;
        background: linear-gradient(135deg, rgba(255, 153, 51, 0.15) 0%, rgba(255, 153, 51, 0.08) 100%);
        border-radius: 6px 6px 0 0;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(255, 153, 51, 0.2);
    }

    .nav-link-active {
        @apply text-saffron-600 font-semibold;
        border-bottom-color: #ff9933;
        background: linear-gradient(135deg, rgba(255, 153, 51, 0.2) 0%, rgba(255, 153, 51, 0.1) 100%);
        border-radius: 6px 6px 0 0;
        box-shadow: 0 2px 4px rgba(255, 153, 51, 0.3);
    }

    /* Government menu style with orange dots */
    .nav-link-active::before {
        content: "•";
        @apply text-saffron-500 mr-1 text-base;
    }

    /* Enhanced dropdown menu styling with better visibility */
    .dropdown-menu {
        @apply absolute z-10 min-w-[200px] shadow-elevated rounded-lg py-3 mt-1 hidden;
        @apply animate-fade-in border border-navy-200;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 25px rgba(30, 41, 59, 0.15), 0 0 0 1px rgba(255, 153, 51, 0.1);
        transition: all 0.3s ease-in-out;
    }

    /* Enhanced dropdown item styling with better contrast */
    .dropdown-item {
        @apply block w-full px-4 py-3 text-navy-700 hover:bg-gradient-to-r hover:from-saffron-50 hover:to-orange-50 transition-all duration-200;
        border-radius: 6px;
        margin: 2px 4px;
        font-weight: 500;
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    }

    .dropdown-item:hover {
        color: #ff9933;
        transform: translateX(4px);
        background: linear-gradient(135deg, rgba(255, 153, 51, 0.1) 0%, rgba(255, 153, 51, 0.05) 100%);
        box-shadow: 0 2px 4px rgba(255, 153, 51, 0.2);
    }

    /* Responsive navigation adjustments */
    @media (min-width: 1024px) and (max-width: 1279px) {
        .nav-link {
            font-size: 0.7rem;
            padding-left: 0.25rem;
            padding-right: 0.25rem;
            padding-top: 0.375rem;
            padding-bottom: 0.375rem;
            line-height: 1.1;
        }

        .dropdown-menu {
            min-width: 150px;
        }

        .dropdown-item {
            font-size: 0.7rem;
            padding: 0.25rem 0.375rem;
        }
    }

    /* Extra compact for very small laptops */
    @media (min-width: 1024px) and (max-width: 1199px) {
        .nav-link {
            font-size: 0.65rem;
            padding-left: 0.125rem;
            padding-right: 0.125rem;
        }

        .dropdown-menu {
            min-width: 140px;
        }

        .dropdown-item {
            font-size: 0.65rem;
            padding: 0.25rem 0.375rem;
        }
    }

    @media (min-width: 1280px) {
        .nav-link {
            font-size: 0.85rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
    }

    /* Prevent text wrapping in navigation */
    .nav-link, .dropdown-item {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Ensure dropdown buttons don't wrap */
    .dropdown button {
        white-space: nowrap;
    }
    
    /* Responsive dropdown adjustments */
    @media (min-width: 1280px) {
        .dropdown-menu {
            min-width: 240px;
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
    }

    .dropdown-item-active {
        @apply text-saffron-500 bg-saffron-50;
    }

    /* Enhanced navbar hover areas - eliminate gaps */
    .dropdown:hover .dropdown-menu,
    .dropdown-menu:hover {
        display: block !important;
    }

    .dropdown-submenu:hover .dropdown-submenu-content,
    .dropdown-submenu-content:hover {
        display: block !important;
    }

    /* Smooth transitions for better UX */
    .dropdown-menu,
    .dropdown-submenu-content {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .dropdown:hover .dropdown-menu,
    .dropdown-submenu:hover .dropdown-submenu-content {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        display: block !important;
    }

    /* Navbar container improvements */
    .navbar-header {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .mega-menu {
        @apply absolute left-0 right-0 bg-white shadow-elevated p-6 mt-1 hidden z-20 animate-fade-in border-t border-gray-100;
    }
    
    /* Form Elements */
    .form-input {
        @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy-500 focus:border-navy-500 transition-colors duration-200;
    }

    .form-input-outline {
        @apply form-input bg-transparent;
    }

    .form-input-pill {
        @apply form-input rounded-full px-5;
    }
    
    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-1.5;
    }

    .form-group {
        @apply mb-4;
    }
    
    /* Dividers & Decorative Elements */
    .decorative-divider {
        @apply flex items-center my-8;
    }
    
    .decorative-divider::before, 
    .decorative-divider::after {
        content: "";
        @apply flex-grow h-px bg-gray-200;
    }
    
    .decorative-divider::before {
        @apply mr-4;
    }
    
    .decorative-divider::after {
        @apply ml-4;
    }
    
    .decorative-icon {
        @apply text-saffron-500;
    }

    /* Cultural design elements */
    .rangoli-pattern {
        @apply relative;
    }

    .rangoli-pattern::before {
        content: "";
        @apply absolute -z-10 w-32 h-32 bg-pattern-saffron opacity-20 rounded-full;
        top: 0;
        right: 0;
        transform: translate(30%, -30%);
    }

    .rangoli-pattern::after {
        content: "";
        @apply absolute -z-10 w-40 h-40 bg-pattern-saffron opacity-15 rounded-full;
        bottom: 0;
        left: 0;
        transform: translate(-30%, 30%);
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-navy-100 text-navy-800;
    }

    .badge-secondary {
        @apply badge bg-saffron-100 text-saffron-800;
    }

    .badge-success {
        @apply badge bg-green-100 text-green-800;
    }

    /* Lists */
    .icon-list li {
        @apply flex items-start mb-3;
    }

    .icon-list li svg {
        @apply flex-shrink-0 text-saffron-500 mt-0.5 mr-3 h-5 w-5;
    }

    /* Modern Government Header Styles */
    .modern-govt-header {
        position: relative;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    }

    .modern-logo-container {
        @apply border-3 border-white rounded-full bg-gradient-to-br from-white to-gray-50;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.8);
        position: relative;
        overflow: hidden;
    }

    .modern-logo-container::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,153,51,0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .modern-logo-container:hover::before {
        opacity: 1;
        animation: shimmer 1.5s ease-in-out;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .modern-govt-title {
        @apply font-bold;
        font-size: clamp(1.25rem, 4vw, 2.5rem);
        line-height: 1.2;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
    }

    .modern-govt-subtitle {
        @apply font-semibold;
        font-size: clamp(1rem, 3vw, 1.75rem);
        text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        letter-spacing: 0.25px;
    }

    .modern-govt-description {
        font-size: clamp(0.8rem, 2vw, 1rem);
        letter-spacing: 0.25px;
    }

    /* Border utilities */
    .border-b-3 {
        border-bottom-width: 3px;
    }

    .border-3 {
        border-width: 3px;
    }

    /* Member Dashboard Theme Consistency */
    .member-dashboard-light {
        background-color: #f9fafb !important;
        color: #111827 !important;
    }

    .member-dashboard-light .sidebar {
        background-color: #ffffff !important;
        border-color: #e5e7eb !important;
    }

    .member-dashboard-light .card {
        background-color: #ffffff !important;
        border-color: #e5e7eb !important;
        color: #111827 !important;
    }

    /* Force light theme for member dashboard by default */
    .member-layout {
        --tw-bg-opacity: 1;
        background-color: rgb(249 250 251 / var(--tw-bg-opacity));
    }

    .member-layout .sidebar-bg {
        --tw-bg-opacity: 1;
        background-color: rgb(255 255 255 / var(--tw-bg-opacity));
    }

    /* Modern responsive styling for mobile */
    @media (max-width: 768px) {
        .modern-govt-header .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .modern-logo-container {
            width: 4rem;
            height: 4rem;
        }

        .modern-govt-title {
            font-size: 1.25rem;
            line-height: 1.3;
            margin-bottom: 0.25rem;
        }

        .modern-govt-subtitle {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .modern-govt-description {
            font-size: 0.8rem;
        }
    }

    @media (min-width: 641px) and (max-width: 768px) {
        .govt-title {
            font-size: 1.25rem;
            line-height: 1.2;
        }
        .govt-subtitle {
            font-size: 1rem;
        }
    }

    @media (min-width: 769px) and (max-width: 1024px) {
        .govt-title {
            font-size: 1.5rem;
            line-height: 1.2;
        }
        .govt-subtitle {
            font-size: 1.25rem;
        }
    }

    @media (min-width: 1025px) {
        .govt-title {
            font-size: 1.75rem;
            line-height: 1.1;
        }
        .govt-subtitle {
            font-size: 1.5rem;
        }
    }

    @media (min-width: 1280px) {
        .govt-title {
            font-size: 2rem;
            line-height: 1.1;
        }
        .govt-subtitle {
            font-size: 1.75rem;
        }
    }
}

/* Mobile Menu Animation */
.mobile-menu {
    transition: transform 0.3s ease-in-out;
    transform: translateX(-100%);
    box-shadow: 5px 0 25px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateX(0);
}

/* Additional Animation Utilities */
.hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
}

.hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
}

/* Enhanced hover effects */
.hover-glow {
    @apply transition-all duration-300;
}

.hover-glow:hover {
    box-shadow: 0 0 15px rgba(255, 153, 51, 0.3);
}

.hover-underline {
    @apply relative inline-block;
}

.hover-underline::after {
    content: '';
    @apply absolute left-0 bottom-0 w-0 h-0.5 bg-current transition-all duration-300 ease-out;
}

.hover-underline:hover::after {
    @apply w-full;
}

/* Decorative Elements */
.pattern-bg {
    background-color: #ffffff;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.6'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Cultural pattern backgrounds */
.paisley-bg {
    background-color: #ffffff;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ff9933' fill-opacity='0.08'%3E%3Cpath d='M20 18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm12 12c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm-12 18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm12-18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM8 18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0 24c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm24 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm0-6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Loader animations */
.loader {
    @apply animate-spin w-6 h-6 border-2 border-t-transparent border-navy-600 rounded-full;
}

/* Marquee Animations */
.marquee-container {
    height: 300px;
    overflow: hidden;
    position: relative;
    mask-image: linear-gradient(to top, transparent 0%, black 10%, black 90%, transparent 100%);
    -webkit-mask-image: linear-gradient(to top, transparent 0%, black 10%, black 90%, transparent 100%);
}

.marquee-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    /* Ensure content starts visible */
    transform: translateY(0);
    /* Add padding to ensure content is always visible */
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.marquee-content:hover {
    animation-play-state: paused;
}

/* Responsive marquee heights */
@media (max-width: 768px) {
    .marquee-container {
        height: 250px;
    }

    /* Adjust grid gap for mobile */
    .grid.lg\\:grid-cols-3 {
        gap: 1rem;
    }
}

@media (max-width: 640px) {
    .marquee-container {
        height: 200px;
    }

    /* Stack columns on mobile */
    .grid.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Smaller text on mobile */
    .marquee-content .text-sm {
        font-size: 0.8rem;
    }

    .marquee-content .text-xs {
        font-size: 0.7rem;
    }

    /* Improve touch targets on mobile */
    .marquee-content a {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}

/* Line clamp utilities for better text truncation */
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* Hamburger Menu Animation */
.hamburger div {
    transition: all 0.3s ease-in-out;
}

.hamburger.active div:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active div:nth-child(2) {
    opacity: 0;
}

.hamburger.active div:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Indian flag colored gradient */
.indian-flag-gradient {
    background: linear-gradient(to bottom, rgba(255,153,51,0.1) 0%, rgba(255,255,255,0.1) 33%, rgba(19,136,8,0.1) 66%);
}

/* Swastika good luck symbol (Hindu cultural symbol) */
.swastika-symbol {
    position: relative;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.swastika-symbol::before {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    border-right: 3px solid currentColor;
    border-bottom: 3px solid currentColor;
}

.swastika-symbol::after {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    border-top: 3px solid currentColor;
    border-left: 3px solid currentColor;
}

/* Om symbol styling */
.om-symbol {
    font-family: 'Noto Sans Devanagari', sans-serif;
    content: "ॐ";
    display: inline-block;
}

.om-symbol::before {
    content: "ॐ";
}

/* Define the pattern backgrounds explicitly */
.bg-pattern-saffron {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff9933' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.bg-pattern-light {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.6'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
