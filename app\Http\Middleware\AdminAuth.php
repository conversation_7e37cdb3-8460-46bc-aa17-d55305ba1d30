<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated and has admin role/flag
        if (!Auth::check() || !Auth::user()->is_admin) {
            // Redirect to admin login if not authenticated as admin
            return redirect()->route('admin.login')->with('error', 'You must be logged in as an admin to access this area.');
        }

        return $next($request);
    }
} 