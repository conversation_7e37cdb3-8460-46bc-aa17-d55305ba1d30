<?php

namespace App\Console\Commands;

use App\Models\Membership;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class FixMemberPasswords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'members:fix-passwords';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix double-hashed member passwords by resetting them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing member passwords...');

        // Get all members with passwords
        $members = Membership::whereNotNull('password')->get();

        $this->info("Found {$members->count()} members with passwords");

        $fixed = 0;
        $skipped = 0;

        foreach ($members as $member) {
            // Test if current password is working with a common test
            $testPasswords = ['password', 'test123', 'member123', '12345678'];
            $isWorking = false;

            foreach ($testPasswords as $testPassword) {
                if (Hash::check($testPassword, $member->password)) {
                    $isWorking = true;
                    break;
                }
            }

            if (!$isWorking) {
                // Password seems to be double-hashed, reset it
                $newPassword = 'temp' . $member->id . '123';
                
                // Use updateQuietly to bypass the mutator and set the password directly
                $member->updateQuietly(['password' => Hash::make($newPassword)]);
                
                $this->line("Fixed password for member {$member->id} (Mobile: {$member->mobile_number}) - New password: {$newPassword}");
                $fixed++;
            } else {
                $this->line("Member {$member->id} password is working correctly - skipped");
                $skipped++;
            }
        }

        $this->info("Password fix completed!");
        $this->info("Fixed: {$fixed} members");
        $this->info("Skipped: {$skipped} members");
        
        if ($fixed > 0) {
            $this->warn("Please inform the affected members of their new temporary passwords.");
            $this->warn("They should change their passwords after logging in.");
        }

        return 0;
    }
}
