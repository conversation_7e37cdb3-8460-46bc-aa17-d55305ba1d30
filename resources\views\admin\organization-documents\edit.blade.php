@extends('layouts.admin') @section('title', 'दस्तावेज संपादित करें') @section('content') <div class="p-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"> <div> <h1 class="text-2xl font-bold admin-text-primary">दस्तावेज संपादित करें</h1> <p class="admin-text-secondary mt-1">{{ $document->title }}</p> </div> <a href="{{ route('admin.organization-documents.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-arrow-left"></i> वापस जाएं </a> </div> <!-- Error Messages --> @if($errors->any()) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6 relative" role="alert"> <div class="font-bold">त्रुटि!</div> <div class="mt-2">कृपया निम्नलिखित समस्याओं को ठीक करें:</div> <ul class="mt-2 ml-4 list-disc"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> @endif <!-- Document Edit Form --> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"> <div class="lg:col-span-2"> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">दस्तावेज की जानकारी</h3> </div> <div class="p-6"> <form action="{{ route('admin.organization-documents.update', $document->id) }}" method="POST" enctype="multipart/form-data"> @csrf @method('PUT') <!-- Title --> <div class="mb-6"> <label for="title" class="block text-sm font-medium admin-text-primary mb-2"> शीर्षक <span class="text-red-500">*</span> </label> <input type="text" class="admin-form-input w-full @error('title') border-red-500 @enderror" id="title" name="title" value="{{ old('title', $document->title) }}" required> @error('title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Description --> <div class="mb-6"> <label for="description" class="block text-sm font-medium admin-text-primary mb-2">विवरण</label> <textarea class="admin-form-input w-full @error('description') border-red-500 @enderror" id="description" name="description" rows="4" placeholder="दस्तावेज के बारे में संक्षिप्त विवरण...">{{ old('description', $document->description) }}</textarea> @error('description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Current File Info --> <div class="mb-6"> <label class="block text-sm font-medium admin-text-primary mb-2">वर्तमान फ़ाइल</label> <div class="bg-blue-50 rounded-lg p-4 border border-blue-200"> <div class="flex items-center"> <i class="fas fa-file-alt text-blue-600 text-xl mr-3"></i> <div> <div class="font-semibold admin-text-primary">{{ $document->file_name }}</div> <div class="text-sm admin-text-secondary mt-1"> <span class="inline-block mr-4">प्रकार: {{ strtoupper($document->file_type) }}</span> <span class="inline-block mr-4">आकार: {{ $document->file_size_human }}</span> <span class="inline-block">डाउनलोड: {{ $document->download_count }}</span> </div> </div> </div> </div> </div> <!-- File Upload (Optional for edit) --> <div class="mb-6"> <label for="file" class="block text-sm font-medium admin-text-primary mb-2"> नई फ़ाइल अपलोड करें (वैकल्पिक) </label> <input type="file" class="admin-form-input w-full @error('file') border-red-500 @enderror" id="file" name="file" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png"> <div class="mt-2 text-sm admin-text-secondary"> <div class="bg-yellow-50 p-3 rounded-lg border border-yellow-200"> <div class="flex items-start"> <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i> <div> <strong class="text-yellow-800">नोट:</strong> नई फ़ाइल अपलोड करने पर पुरानी फ़ाइल बदल जाएगी<br> <strong>समर्थित फ़ाइल प्रकार:</strong> PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, JPEG, PNG<br> <strong>अधिकतम फ़ाइल आकार:</strong> 10MB </div> </div> </div> </div> @error('file') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div class="mb-3"> <label for="display_order" class="form-label">प्रदर्शन क्रम</label> <input type="number" class="form-control @error('display_order') is-invalid @enderror" id="display_order" name="display_order" value="{{ old('display_order', $document->display_order) }}" min="0"> <div class="form-text">कम संख्या वाले दस्तावेज पहले दिखाए जाएंगे</div> @error('display_order') <div class="invalid-feedback">{{ $message }}</div> @enderror </div> <!-- Published Status --> <div class="mb-3"> <div class="form-check"> <input type="checkbox" class="form-check-input" id="is_published" name="is_published" value="1" {{ old('is_published', $document->is_published) ? 'checked' : '' }}> <label class="form-check-label" for="is_published"> प्रकाशित करें </label> </div> <div class="form-text">अगर चेक किया गया है तो दस्तावेज वेबसाइट पर दिखाई देगा</div> </div> <!-- Submit Buttons --> <div class="d-flex gap-2"> <button type="submit" class="btn btn-primary"> <i class="fas fa-save"></i> अपडेट करें </button> <a href="{{ route('admin.organization-documents.index') }}" class="btn btn-secondary"> रद्द करें </a> </div> </form> </div> </div> </div> <!-- Document Info & Actions --> <div class="lg:col-span-1 space-y-6"> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold text-blue-600">दस्तावेज की जानकारी</h3> </div> <div class="p-6"> <div class="space-y-4"> <div class="flex justify-between items-center"> <span class="font-medium admin-text-primary">बनाया गया:</span> <span class="admin-text-secondary text-sm">{{ $document->created_at->format('d/m/Y H:i') }}</span> </div> <div class="flex justify-between items-center"> <span class="font-medium admin-text-primary">अपडेट किया गया:</span> <span class="admin-text-secondary text-sm">{{ $document->updated_at->format('d/m/Y H:i') }}</span> </div> <div class="flex justify-between items-center"> <span class="font-medium admin-text-primary">कुल डाउनलोड:</span> <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"> {{ $document->download_count }} </span> </div> <div class="flex justify-between items-center"> <span class="font-medium admin-text-primary">स्थिति:</span> @if($document->is_published) <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"> प्रकाशित </span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800"> अप्रकाशित </span> @endif </div> </div> <div class="border-t border-gray-200 my-6"></div> <div class="space-y-3"> <a href="{{ route('admin.organization-documents.show', $document->id) }}" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"> <i class="fas fa-eye"></i> विस्तार से देखें </a> @if($document->fileExists()) <a href="{{ Storage::url($document->file_path) }}" target="_blank" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"> <i class="fas fa-download"></i> फ़ाइल डाउनलोड करें </a> @else <button class="w-full bg-red-500 text-white px-4 py-2 rounded-lg font-medium cursor-not-allowed opacity-50 flex items-center justify-center gap-2" disabled> <i class="fas fa-exclamation-triangle"></i> फ़ाइल नहीं मिली </button> @endif </div> </div> </div> <!-- Help Section --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold text-yellow-600">सहायता</h3> </div> <div class="p-6"> <h4 class="text-base font-semibold text-blue-600 mb-4">संपादन के लिए सुझाव:</h4> <ul class="space-y-3"> <li class="flex items-start"> <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i> <span class="admin-text-secondary">केवल आवश्यक फ़ील्ड को ही बदलें</span> </li> <li class="flex items-start"> <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i> <span class="admin-text-secondary">नई फ़ाइल तभी अपलोड करें जब जरूरत हो</span> </li> <li class="flex items-start"> <i class="fas fa-lightbulb text-yellow-500 mr-3 mt-1"></i> <span class="admin-text-secondary">प्रदर्शन क्रम से दस्तावेजों को व्यवस्थित करें</span> </li> </ul> </div> </div> </div> </div> </div> @endsection @push('scripts') <script> // File upload validation document.getElementById('file').addEventListener('change', function(e) { const file = e.target.files[0]; if (file) { // Check file size (10MB = 10 * 1024 * 1024 bytes) if (file.size > 10 * 1024 * 1024) { alert('फ़ाइल का आकार 10MB से अधिक नहीं होना चाहिए।'); this.value = ''; return; } } }); // Form validation document.querySelector('form').addEventListener('submit', function(e) { const titleInput = document.getElementById('title'); if (!titleInput.value.trim()) { e.preventDefault(); alert('कृपया शीर्षक दर्ज करें।'); titleInput.focus(); return; } }); </script> @endpush 