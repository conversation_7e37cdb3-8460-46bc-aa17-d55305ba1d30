!function(t,r){"use strict";"function"==typeof define&&define.amd&&define.amd.jQuery?define(["jquery"],function(e){return r(e,t)}):"object"==typeof exports?module.exports=r:r(t.jQuery||t.$,t)}(this,function(v,u){"use strict";var k=v.ajax,b=[],S=[],C=[],l=/=\?(&|$)/,c=(new Date).getTime(),n=500;function M(t,r){L.debug(t,["Checking mock data against request data",t,r]);var n=!0;if(v.isFunction(t))return!!t(r);if("string"==typeof r){if(v.isFunction(t.test))return t.test(r);if("object"!=typeof t)return t===r;r=function(e){var t,r,n,o,s={},a=String(e).split(/&/);for(t=0,r=a.length;t<r;++t){n=a[t];try{n=(n=decodeURIComponent(n.replace(/\+/g," "))).split(/=/)}catch(e){continue}s[n[0]]?(s[n[0]].splice||(o=s[n[0]],s[n[0]]=[],s[n[0]].push(o)),s[n[0]].push(n[1])):s[n[0]]=n[1]}return L.debug(null,["Getting query params from string",e,s]),s}(r)}return v.each(t,function(e){if(void 0===r[e])return n=!1;n="object"==typeof r[e]&&null!==r[e]?(n=n&&v.isArray(r[e])?v.isArray(t[e])&&r[e].length===t[e].length:n)&&M(t[e],r[e]):t[e]&&v.isFunction(t[e].test)?n&&t[e].test(r[e]):n&&t[e]===r[e]}),n}function a(e,t){return e[t]===v.mockjaxSettings[t]}function o(e){return"number"==typeof e&&0<=e}function i(e){if(v.isArray(e)&&2===e.length){var t=e[0],r=e[1];if(o(t)&&o(r))return Math.floor(Math.random()*(r-t))+t}else if(o(e))return e;return n}function O(t,r,n){L.debug(t,["Sending fake XHR request",t,r,n]);o=this;var o,s=function(){return function(){this.status=t.status,this.statusText=t.statusText,this.readyState=1;function e(){var e;this.readyState=4,"json"===r.dataType&&"object"==typeof t.responseText?this.responseText=JSON.stringify(t.responseText):"xml"===r.dataType?"string"==typeof t.responseXML?(this.responseXML=function(t){void 0===u.DOMParser&&u.ActiveXObject&&(u.DOMParser=function(){},DOMParser.prototype.parseFromString=function(e){var t=new ActiveXObject("Microsoft.XMLDOM");return t.async="false",t.loadXML(e),t});try{var e=(new DOMParser).parseFromString(t,"text/xml");if(!v.isXMLDoc(e))throw new Error("Unable to parse XML");if(1===v("parsererror",e).length)throw new Error("Error: "+v(e).text());return e}catch(e){t=void 0===e.name?e:e.name+": "+e.message;v(document).trigger("xmlParseError",[t])}}(t.responseXML),this.responseText=t.responseXML):this.responseXML=t.responseXML:"object"==typeof t.responseText&&null!==t.responseText?(t.contentType="application/json",this.responseText=JSON.stringify(t.responseText)):this.responseText=t.responseText,v.isArray(t.status)?(e=Math.floor(Math.random()*t.status.length),this.status=t.status[e]):"number"!=typeof t.status&&"string"!=typeof t.status||(this.status=t.status),"string"==typeof t.statusText&&(this.statusText=t.statusText),e=this.onload||this.onreadystatechange,v.isFunction(e)?(t.isTimeout&&(this.status=-1),e.call(this,t.isTimeout?"timeout":void 0)):t.isTimeout&&(this.status=-1)}if(v.isFunction(t.response)){if(2===t.response.length)return void t.response(n,function(){e.call(o)});t.response(n)}e.call(o)}.apply(o)};t.proxy?(L.info(t,["Retrieving proxy file: "+t.proxy,t]),k({global:!1,url:t.proxy,type:t.proxyType,data:t.data,async:r.async,dataType:"script"===r.dataType?"text/plain":r.dataType,complete:function(e){t.responseXML=t.responseText=e.responseText,a(t,"status")&&(t.status=e.status),a(t,"statusText")&&(t.statusText=e.statusText),!1===r.async?s():this.responseTimer=setTimeout(s,i(t.responseTime))}})):!1===r.async?s():this.responseTimer=setTimeout(s,i(t.responseTime))}function w(e,t,r){if("GET"===(s=e).type.toUpperCase()?l.test(s.url)||(s.url+=(/\?/.test(s.url)?"&":"?")+(s.jsonp||"callback")+"=?"):s.data&&l.test(s.data)||(s.data=(s.data?s.data+"&":"")+(s.jsonp||"callback")+"=?"),e.dataType="json",e.data&&l.test(e.data)||l.test(e.url)){var n=e,o=t,s=r,a=s&&s.context||n,i="string"==typeof n.jsonpCallback&&n.jsonpCallback||"jsonp"+c++,s=(n.data&&(n.data=(n.data+"").replace(l,"="+i+"$1")),n.url=n.url.replace(l,"="+i+"$1"),u[i]=u[i]||function(){f(n,a,o),g(n,a),u[i]=void 0;try{delete u[i]}catch(e){}},n.jsonpCallback=i,/^(\w+:)?\/\/([^\/?#]+)/.exec(e.url)),s=s&&(s[1]&&s[1]!==location.protocol||s[2]!==location.host);if(e.dataType="script","GET"===e.type.toUpperCase()&&s)return function(t,r,e){L.debug(r,["Performing JSONP request",r,t,e]);var n=e&&e.context||t,o=v.Deferred?new v.Deferred:null;if(r.response&&v.isFunction(r.response))r.response(e);else if("object"==typeof r.responseText)v.globalEval("("+JSON.stringify(r.responseText)+")");else{if(r.proxy)return L.info(r,["Performing JSONP proxy request: "+r.proxy,r]),k({global:!1,url:r.proxy,type:r.proxyType,data:r.data,dataType:"script"===t.dataType?"text/plain":t.dataType,complete:function(e){v.globalEval("("+e.responseText+")"),p(t,r,n,o)}}),o;v.globalEval("("+("string"==typeof r.responseText?'"'+r.responseText+'"':r.responseText)+")")}return p(t,r,n,o),o}(e,t,r)||!0}return null}function p(e,t,r,n){var o;setTimeout(function(){if(f(e,r,t),g(e,r),n){try{o=v.parseJSON(t.responseText)}catch(e){}n.resolveWith(r,[o||t.responseText]),L.log(t,["JSONP mock call complete",t,n])}},i(t.responseTime))}function f(e,t,r){e.success&&e.success.call(t,r.responseText||"","success",{}),e.global&&(e.context?v(e.context):v.event).trigger("ajaxSuccess",[{},e])}function g(e,t){e.complete&&e.complete.call(t,{statusText:"success",status:200},"success"),e.global&&(e.context?v(e.context):v.event).trigger("ajaxComplete",[{},e]),e.global&&!--v.active&&v.event.trigger("ajaxStop")}v.extend({ajax:function e(t,n){var r,o,s;L.debug(null,["Ajax call intercepted",t,n]),"object"==typeof t?(n=t,t=void 0):(n=n||{}).url=t||n.url,(o=v.ajaxSetup({},n)).type=o.method=o.method||o.type;for(var a=function(e,t){var r=n[e.toLowerCase()];return function(){v.isFunction(r)&&r.apply(this,[].slice.call(arguments)),t["onAfter"+e]()}},i=0;i<b.length;i++){var u=v.mockjaxSettings.matchInRegistrationOrder?i:b.length-1-i;if(u=b[u]){if(s=function(e,r){if(v.isFunction(e))return e(r);var t=e.namespace||void 0===e.namespace&&v.mockjaxSettings.namespace;if(v.isFunction(e.url.test)){if(t&&(t=t.replace(/(\/+)$/,""),n=e.url.source.replace(/^(\^+)/,"").replace(/^/,"^("+t+")?/?"),e.url=new RegExp(n)),!e.url.test(r.url))return null}else{var n=e.url,t=(n=t?[t.replace(/(\/+)$/,""),e.url.replace(/^(\/+)/,"")].join("/"):n).indexOf("*");if(n!==r.url&&-1===t||!new RegExp(n.replace(/[-[\]{}()+?.,\\^$|#\s]/g,"\\$&").replace(/\*/g,".+")).test(r.url))return null}if(e.requestHeaders){if(void 0===r.headers)return null;var o=!1;if(v.each(e.requestHeaders,function(e,t){if(r.headers[e]!==t)return!(o=!0)}),o)return null}return!(!e.data||r.data&&M(e.data,r.data))||e&&e.type&&e.type.toLowerCase()!==r.type.toLowerCase()?null:e}(u,o)){if(v.mockjaxSettings.retainAjaxCalls&&S.push(o),L.info(s,["MOCK "+o.type.toUpperCase()+": "+o.url,v.ajaxSetup({},o)]),(301===s.status||302===s.status)&&("GET"===o.type.toUpperCase()||"HEAD"===o.type.toUpperCase())&&s.headers.Location){L.debug("Doing mock redirect to",s.headers.Location,o.type);for(var l={},c=Object.keys(n),p=0;p<c.length;p++)l[c[p]]=n[c[p]];return l.url=s.headers.Location,l.headers={Referer:n.url},e(l)}if(!o.dataType||"JSONP"!==o.dataType.toUpperCase()||!(r=w(o,s,n))){n.crossDomain=!1,s.cache=o.cache,s.timeout=o.timeout,s.global=o.global,s.isTimeout&&(1<s.responseTime?n.timeout=s.responseTime-1:(s.responseTime=2,n.timeout=1)),v.isFunction(s.onAfterSuccess)&&(n.success=a("Success",s)),v.isFunction(s.onAfterError)&&(n.error=a("Error",s)),v.isFunction(s.onAfterComplete)&&(n.complete=a("Complete",s)),j=T=y=h=m=x=d=g=f=void 0;var f=s,g=n;if(f.url instanceof RegExp&&f.hasOwnProperty("urlParams")){var d=f.url.exec(g.url);if(1!==d.length){d.shift();for(var x=0,m=d.length,h=f.urlParams.length,y=Math.min(m,h),T={};x<y;x++){var j=f.urlParams[x];T[j]=d[x]}g.urlParams=T}}!function(o,s,a,i){r=k.call(v,v.extend(!0,{},a,{xhr:function(){return t=o,r=s,e=a,n=i,L.debug(t,["Creating new mock XHR object",t,r,e,n]),void 0===(t=v.extend(!0,{},v.mockjaxSettings,t)).headers&&(t.headers={}),void 0===r.headers&&(r.headers={}),t.contentType&&(t.headers["content-type"]=t.contentType),{status:t.status,statusText:t.statusText,readyState:1,open:function(){},send:function(){n.fired=!0,O.call(this,t,r,e)},abort:function(){clearTimeout(this.responseTimer)},setRequestHeader:function(e,t){r.headers[e]=t},getResponseHeader:function(e){return t.headers&&t.headers[e]?t.headers[e]:"last-modified"===e.toLowerCase()?t.lastModified||(new Date).toString():"etag"===e.toLowerCase()?t.etag||"":"content-type"===e.toLowerCase()?t.contentType||"text/plain":void 0},getAllResponseHeaders:function(){var r="";return t.contentType&&(t.headers["content-type"]=t.contentType),v.each(t.headers,function(e,t){r+=e+": "+t+"\n"}),r}};var t,r,e,n}}))}(s,o,n,u)}return r}L.debug(u,["Mock does not match request",t,o])}}if(L.log(null,["No mock matched to request",t,n]),v.mockjaxSettings.retainAjaxCalls&&C.push(n),!0===v.mockjaxSettings.throwUnmocked)throw new Error("AJAX not mocked: "+n.url);return L.log("Real ajax call to",n.url),k.apply(v,[n])}});var L={_log:function(e,t,r){var n=v.mockjaxSettings.logging;if(e&&void 0!==e.logging&&(n=e.logging),r=0===r?r:r||s.LOG,t=t.splice?t:[t],!(!1===n||n<r))return v.mockjaxSettings.log?v.mockjaxSettings.log(e,t[1]||t[0]):v.mockjaxSettings.logger&&v.mockjaxSettings.logger[v.mockjaxSettings.logLevelMethods[r]]?v.mockjaxSettings.logger[v.mockjaxSettings.logLevelMethods[r]].apply(v.mockjaxSettings.logger,t):void 0},debug:function(e,t){return L._log(e,t,s.DEBUG)},log:function(e,t){return L._log(e,t,s.LOG)},info:function(e,t){return L._log(e,t,s.INFO)},warn:function(e,t){return L._log(e,t,s.WARN)},error:function(e,t){return L._log(e,t,s.ERROR)}},s={DEBUG:4,LOG:3,INFO:2,WARN:1,ERROR:0};return v.mockjaxSettings={log:null,logger:u.console,logging:2,logLevelMethods:["error","warn","info","log","debug"],matchInRegistrationOrder:!0,namespace:null,status:200,statusText:"OK",responseTime:n,isTimeout:!1,throwUnmocked:!1,retainAjaxCalls:!0,contentType:"text/plain",response:"",responseText:"",responseXML:"",proxy:"",proxyType:"GET",lastModified:null,etag:"",headers:{etag:"IJF@H#@923uf8023hFO@I#H#","content-type":"text/plain"}},v.mockjax=function(e){var t;return v.isArray(e)?v.map(e,function(e){return v.mockjax(e)}):(t=b.length,b[t]=e,L.log(e,["Created new mock handler",e]),t)},v.mockjax._logger=L,v.mockjax.clear=function(e){"string"==typeof e||e instanceof RegExp?b=function(t){for(var e,r=[],n=t instanceof RegExp?function(e){return t.test(e)}:function(e){return t===e},o=0,s=b.length;o<s;o++)n((e=b[o]).url)?L.log(e,["Clearing mock: "+(e&&e.url),e]):r.push(e);return r}(e):e||0===e?(L.log(b[e],["Clearing mock: "+(b[e]&&b[e].url),b[e]]),b[e]=null):(L.log(null,"Clearing all mocks"),b=[]),S=[],C=[]},v.mockjax.clearRetainedAjaxCalls=function(){S=[],C=[],L.debug(null,"Cleared retained ajax calls")},v.mockjax.handler=function(e){if(1===arguments.length)return b[e]},v.mockjax.handlers=function(){return b},v.mockjax.mockedAjaxCalls=function(){return S},v.mockjax.unfiredHandlers=function(){for(var e=[],t=0,r=b.length;t<r;t++){var n=b[t];null===n||n.fired||e.push(n)}return e},v.mockjax.unmockedAjaxCalls=function(){return C},v.mockjax});