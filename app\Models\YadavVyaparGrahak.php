<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class YadavVyaparGrahak extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'yadav_vyapar_grahak';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'category',
        'sadasyata_kramank',
        'sadasyata_prakar',
        'naam',
        'ajivika',
        'ward',
        'vikaskhand',
        'jila',
        'mobile_number',
        'address',
        'photo',
        'gmap_link',
    ];

    /**
     * Category constants
     */
    public const CATEGORY_SWASTHYA = 'swasthya';
    public const CATEGORY_GHARELU = 'gharelu';
    public const CATEGORY_SALAHKAR = 'salahkar';
    public const CATEGORY_ANYA_VYAVASAY = 'anya vyavasay';

    /**
     * Get all available categories
     */
    public static function getCategories()
    {
        return [
            self::CATEGORY_SWASTHYA => 'स्वास्थ्य',
            self::CATEGORY_GHARELU => 'घरेलू',
            self::CATEGORY_SALAHKAR => 'सलाहकार',
            self::CATEGORY_ANYA_VYAVASAY => 'अन्य व्यवसाय',
        ];
    }

    /**
     * Get category name in Hindi
     */
    public function getCategoryNameAttribute()
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * Get photo file URL if exists
     */
    public function getPhotoUrlAttribute()
    {
        if ($this->photo && Storage::exists($this->photo)) {
            return Storage::url($this->photo);
        }
        return null;
    }

    /**
     * Check if photo file exists
     */
    public function photoExists()
    {
        return $this->photo && Storage::exists($this->photo);
    }

    /**
     * Delete photo file from storage
     */
    public function deletePhotoFile()
    {
        if ($this->photoExists()) {
            Storage::delete($this->photo);
        }
    }

    /**
     * Get photo file name
     */
    public function getPhotoFileNameAttribute()
    {
        if ($this->photo) {
            return basename($this->photo);
        }
        return null;
    }

    /**
     * Get formatted Google Maps link
     */
    public function getFormattedGmapLinkAttribute()
    {
        if (!$this->gmap_link) {
            return null;
        }

        // If it's already a proper Google Maps link, return as is
        if (strpos($this->gmap_link, 'maps.google.com') !== false || strpos($this->gmap_link, 'goo.gl/maps') !== false) {
            return $this->gmap_link;
        }

        // If it's coordinates, format as Google Maps link
        if (preg_match('/^-?\d+\.?\d*,-?\d+\.?\d*$/', $this->gmap_link)) {
            return 'https://maps.google.com/?q=' . $this->gmap_link;
        }

        return $this->gmap_link;
    }

    /**
     * Check if gmap link is valid coordinates
     */
    public function hasValidCoordinates()
    {
        return preg_match('/^-?\d+\.?\d*,-?\d+\.?\d*$/', $this->gmap_link);
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Delete photo file when model is deleted
        static::deleting(function ($yadavVyapar) {
            $yadavVyapar->deletePhotoFile();
        });
    }
}
