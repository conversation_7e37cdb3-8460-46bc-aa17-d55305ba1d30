<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EkadashSadasyata;
use App\Models\EducationQualification;
use App\Models\OfficeMaster;
use App\Models\DepartmentMaster;
use App\Models\YadavVarg;
use App\Models\DivisionMaster;
use App\Models\DistrictMaster;
use App\Models\VikaskhandMaster;

class EkadashSadasyataController extends Controller
{
    /**
     * Show the Ekadash Sadasyata application form with master data.
     */
    public function showApplicationForm()
    {
        $educationQualifications = EducationQualification::active()->ordered()->get();
        $officeMasters = OfficeMaster::active()->ordered()->get();
        $departmentMasters = DepartmentMaster::active()->ordered()->get();
        $yadavVargs = YadavVarg::active()->ordered()->get();
        $divisionMasters = DivisionMaster::active()->ordered()->get();
        $districtMasters = DistrictMaster::active()->ordered()->get();
        $vikaskhandMasters = VikaskhandMaster::active()->ordered()->get();

        return view('ekadash-sadasyata.aavedan', compact(
            'educationQualifications', 'officeMasters', 'departmentMasters',
            'yadavVargs', 'divisionMasters', 'districtMasters', 'vikaskhandMasters'
        ));
    }

    /**
     * Submit the Ekadash Sadasyata application
     */
    public function submitApplication(Request $request)
    {
        // Validate the incoming request data
        $validatedData = $request->validate([
            'photo' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'signature' => 'nullable|image|mimes:jpg,jpeg,png|max:1024',
            'name' => 'required|string|max:255',
            'fathers_husband_name' => 'nullable|string|max:255',
            'mobile_number' => 'required|digits:10|starts_with:6,7,8,9|unique:ekadash_sadasyata,mobile_number',
            'birth_date' => 'required|date',
            'marital_status' => 'nullable|string',
            'vivah_tithi' => 'nullable|date',
            'family_members' => 'nullable|integer|min:1',
            'education' => 'nullable|string',
            'education_qualification_id' => 'nullable|exists:education_qualifications,id',
            'course_stream_name' => 'nullable|string|max:255',
            'caste_details' => 'nullable|string|max:255',
            'vibhagiy_padnaam' => 'nullable|string|max:255',
            'department_name' => 'nullable|string|max:255',
            'department_master_id' => 'nullable|exists:department_masters,id',
            'office' => 'nullable|string|max:255',
            'office_master_id' => 'nullable|exists:office_masters,id',
            'karyalay_ka_pata' => 'nullable|string',
            'vartaman_pata' => 'nullable|string',
            'isthayi_pata' => 'nullable|string',
            'yadav_varg_id' => 'nullable|exists:yadav_vargs,id',
            'division_master_id' => 'required|exists:division_masters,id',
            'district_master_id' => 'required|exists:district_masters,id',
            'vikaskhand_master_id' => 'required|exists:vikaskhand_masters,id',
            'child_name.*' => 'nullable|string|max:255',
            'child_gender.*' => 'nullable|string',
            'child_dob.*' => 'nullable|date',
            'child_education.*' => 'nullable|string|max:255',
            'child_occupation.*' => 'nullable|string|max:255',
            'child_marital_status.*' => 'nullable|string',
            'child_spouse_name.*' => 'nullable|string|max:255',
            'child_spouse_occupation.*' => 'nullable|string|max:255',
            'member_name_signature' => 'required|string|max:255',
            'vibhag' => 'nullable|string',
            'mobile' => 'required|digits:10|starts_with:6,7,8,9',
            'proposer_address' => 'nullable|string',
            'declaration' => 'required|accepted',
        ], [
            'photo.image' => 'फोटो एक वैध इमेज फाइल होनी चाहिए।',
            'photo.mimes' => 'फोटो JPG, JPEG या PNG फॉर्मेट में होनी चाहिए।',
            'photo.max' => 'फोटो का साइज 2MB से कम होना चाहिए।',
            'signature.image' => 'हस्ताक्षर एक वैध इमेज फाइल होनी चाहिए।',
            'signature.mimes' => 'हस्ताक्षर JPG, JPEG या PNG फॉर्मेट में होनी चाहिए।',
            'signature.max' => 'हस्ताक्षर का साइज 1MB से कम होना चाहिए।',
            'name.required' => 'नाम आवश्यक है।',
            'mobile_number.required' => 'मोबाइल नंबर आवश्यक है।',
            'mobile_number.digits' => 'मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile_number.starts_with' => 'मोबाइल नंबर 6, 7, 8 या 9 से शुरू होना चाहिए।',
            'mobile_number.unique' => 'यह मोबाइल नंबर पहले से पंजीकृत है।',
            'birth_date.required' => 'जन्म तिथि आवश्यक है।',
            'division_master_id.required' => 'संभाग चुनना आवश्यक है।',
            'district_master_id.required' => 'जिला चुनना आवश्यक है।',
            'vikaskhand_master_id.required' => 'विकासखंड चुनना आवश्यक है।',
            'member_name_signature.required' => 'सदस्य का नाम (हस्ताक्षर) आवश्यक है।',
            'mobile.required' => 'प्रस्तावक का मोबाइल नंबर आवश्यक है।',
            'mobile.digits' => 'प्रस्तावक का मोबाइल नंबर 10 अंकों का होना चाहिए।',
            'mobile.starts_with' => 'प्रस्तावक का मोबाइल नंबर 6, 7, 8 या 9 से शुरू होना चाहिए।',
            'declaration.required' => 'घोषणा स्वीकार करना आवश्यक है।',
            'declaration.accepted' => 'घोषणा स्वीकार करना आवश्यक है।',
            'family_members.integer' => 'परिवार के सदस्यों की संख्या एक संख्या होनी चाहिए।',
            'family_members.min' => 'परिवार में कम से कम 1 सदस्य होना चाहिए।',
        ]);

        // Handle file uploads
        if ($request->hasFile('photo')) {
            $validatedData['photo'] = $request->file('photo')->store('photos', 'public');
        }

        if ($request->hasFile('signature')) {
            $validatedData['signature'] = $request->file('signature')->store('signatures', 'public');
        }

        // Process children data
        $children = [];
        if ($request->has('child_name')) {
            foreach ($request->child_name as $index => $name) {
                if (!empty($name)) {
                    $children[] = [
                        'name' => $name,
                        'gender' => $request->child_gender[$index] ?? '',
                        'dob' => $request->child_dob[$index] ?? '',
                        'education' => $request->child_education[$index] ?? '',
                        'occupation' => $request->child_occupation[$index] ?? '',
                        'marital_status' => $request->child_marital_status[$index] ?? '',
                        'spouse_name' => $request->child_spouse_name[$index] ?? '',
                        'spouse_occupation' => $request->child_spouse_occupation[$index] ?? '',
                    ];
                }
            }
        }
        $validatedData['children'] = $children;

        // Process ancestors data (7 generations)
        $ancestors = [];
        for ($level = 1; $level <= 7; $level++) {
            $ancestorKey = "ancestor_name_{$level}";
            if ($request->has($ancestorKey)) {
                foreach ($request->input($ancestorKey, []) as $index => $name) {
                    if (!empty($name)) {
                        $ancestors[] = [
                            'level' => $level,
                            'name' => $name,
                            'birth_date' => $request->input("ancestor_birth_date_{$level}")[$index] ?? '',
                            'death_date' => $request->input("ancestor_death_date_{$level}")[$index] ?? '',
                            'birth_place' => $request->input("ancestor_birth_place_{$level}")[$index] ?? '',
                            'occupation' => $request->input("ancestor_occupation_{$level}")[$index] ?? '',
                            'spouse' => $request->input("ancestor_spouse_{$level}")[$index] ?? '',
                            'children_count' => $request->input("ancestor_children_count_{$level}")[$index] ?? '',
                            'gotra' => $request->input("ancestor_gotra_{$level}")[$index] ?? '',
                        ];
                    }
                }
            }
        }
        $validatedData['ancestors'] = $ancestors;

        // Set declaration as true (already validated)
        $validatedData['declaration'] = true;

        // Set default status as pending
        $validatedData['status'] = EkadashSadasyata::STATUS_PENDING;

        // Set fixed fee
        $validatedData['fee'] = 11.00;

        // Save the Ekadash Sadasyata record
        $ekadashSadasyata = EkadashSadasyata::create($validatedData);

        // Redirect with success message and membership number
        return redirect()->route('ekadash-sadasyata.aavedan')->with([
            'success' => 'एकादश सदस्यता पंजीयन सफल रहा। स्वीकृति के बाद आपका नाम सदस्य सूची में दिखाई देगा।',
            'membership_number' => $ekadashSadasyata->membership_number,
            'fee_info' => 'सदस्यता शुल्क: ₹11 (न्यूनतम)'
        ]);
    }

    /**
     * Show all Ekadash Sadasyata applications with table view
     */
    public function showApplications()
    {
        $applications = EkadashSadasyata::with(['districtMaster', 'vikaskhandMaster', 'educationQualification', 'departmentMaster'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('ekadash-sadasyata.applications', compact('applications'));
    }

    /**
     * Show all approved Ekadash Sadasyata members
     */
    public function showMembers(Request $request)
    {
        $query = EkadashSadasyata::approved()
            ->active()
            ->with(['educationQualification', 'officeMaster', 'departmentMaster', 'divisionMaster', 'districtMaster', 'vikaskhandMaster']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('fathers_husband_name', 'like', "%{$search}%")
                  ->orWhere('mobile_number', 'like', "%{$search}%")
                  ->orWhere('membership_number', 'like', "%{$search}%");
            });
        }

        // Filter by division
        if ($request->filled('division_master_id')) {
            $query->where('division_master_id', $request->division_master_id);
        }

        // Filter by district
        if ($request->filled('district_master_id')) {
            $query->where('district_master_id', $request->district_master_id);
        }

        // Filter by vikaskhand
        if ($request->filled('vikaskhand_master_id')) {
            $query->where('vikaskhand_master_id', $request->vikaskhand_master_id);
        }

        $members = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get master data for filters
        $divisionMasters = DivisionMaster::active()->ordered()->get();
        $districtMasters = DistrictMaster::active()->ordered()->get();
        $vikaskhandMasters = VikaskhandMaster::active()->ordered()->get();

        return view('ekadash-sadasyata.members', compact('members', 'divisionMasters', 'districtMasters', 'vikaskhandMasters'));
    }
}
