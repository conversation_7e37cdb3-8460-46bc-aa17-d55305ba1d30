@extends('layouts.app')

@section('title', 'प्रोफाइल संपादित करें - वैवाहिक पंजीयन')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-4 mb-4 md:mb-0">
                    <a href="{{ route('vaivahik.dashboard') }}" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">प्रोफाइल संपादित करें</h1>
                        <p class="text-gray-600">अपनी व्यक्तिगत जानकारी को अपडेट करें</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <form action="{{ route('vaivahik.update-profile') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
            @csrf
            @method('PUT')

            <!-- Basic Information Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">व्यक्तिगत जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="naam" class="block text-sm font-medium text-navy-700 mb-2">नाम *</label>
                            <input type="text" name="naam" id="naam" value="{{ old('naam', $user->naam) }}"
                                   class="form-input w-full" required>
                            @error('naam')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Gender -->
                        <div>
                            <label for="ling" class="block text-sm font-medium text-navy-700 mb-2">लिंग</label>
                            <select name="ling" id="ling" class="form-input w-full">
                                <option value="">-- चुनें --</option>
                                <option value="पुरुष" {{ old('ling', $user->ling) == 'पुरुष' ? 'selected' : '' }}>पुरुष</option>
                                <option value="महिला" {{ old('ling', $user->ling) == 'महिला' ? 'selected' : '' }}>महिला</option>
                                <option value="अन्य" {{ old('ling', $user->ling) == 'अन्य' ? 'selected' : '' }}>अन्य</option>
                            </select>
                            @error('ling')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Mobile -->
                        <div>
                            <label for="mobile" class="block text-sm font-medium text-navy-700 mb-2">मोबाइल नंबर</label>
                            <input type="tel" name="mobile" id="mobile" value="{{ old('mobile', $user->mobile) }}"
                                   class="form-input w-full" maxlength="10" pattern="[0-9]{10}">
                            <p class="text-xs text-gray-500 mt-1">10 अंकों का मोबाइल नंबर दर्ज करें</p>
                            @error('mobile')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Owner Email -->
                        <div>
                            <label for="owner_email" class="block text-sm font-medium text-navy-700 mb-2">ईमेल पता *</label>
                            <input type="email" name="owner_email" id="owner_email" value="{{ old('owner_email', $user->owner_email) }}"
                                   class="form-input w-full" required>
                            @error('owner_email')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Birth Date -->
                        <div>
                            <label for="janmatithi" class="block text-sm font-medium text-navy-700 mb-2">जन्म तिथि</label>
                            <input type="date" name="janmatithi" id="janmatithi" value="{{ old('janmatithi', $user->janmatithi?->format('Y-m-d')) }}"
                                   class="form-input w-full">
                            @error('janmatithi')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Birth Time and Day -->
                        <div>
                            <label for="janmasamay_din" class="block text-sm font-medium text-navy-700 mb-2">जन्म समय व दिन</label>
                            <input type="text" name="janmasamay_din" id="janmasamay_din" value="{{ old('janmasamay_din', $user->janmasamay_din) }}"
                                   class="form-input w-full">
                            @error('janmasamay_din')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Information Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">पारिवारिक जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Father's Name -->
                        <div>
                            <label for="pita_ka_naam" class="block text-sm font-medium text-navy-700 mb-2">पिता का नाम</label>
                            <input type="text" name="pita_ka_naam" id="pita_ka_naam" value="{{ old('pita_ka_naam', $user->pita_ka_naam) }}"
                                   class="form-input w-full">
                            @error('pita_ka_naam')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Mother's Name -->
                        <div>
                            <label for="mata_ka_naam" class="block text-sm font-medium text-navy-700 mb-2">माता का नाम</label>
                            <input type="text" name="mata_ka_naam" id="mata_ka_naam" value="{{ old('mata_ka_naam', $user->mata_ka_naam) }}"
                                   class="form-input w-full">
                            @error('mata_ka_naam')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Siblings Details -->
                    <div class="mt-6">
                        <label for="bhai_bahan_vivran" class="block text-sm font-medium text-navy-700 mb-2">भाई बहन का विवरण</label>
                        <textarea name="bhai_bahan_vivran" id="bhai_bahan_vivran" rows="3" class="form-input w-full">{{ old('bhai_bahan_vivran', $user->bhai_bahan_vivran) }}</textarea>
                        @error('bhai_bahan_vivran')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Address -->
                    <div class="mt-6">
                        <label for="pata" class="block text-sm font-medium text-navy-700 mb-2">पता</label>
                        <textarea name="pata" id="pata" rows="3" class="form-input w-full">{{ old('pata', $user->pata) }}</textarea>
                        @error('pata')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Caste Information Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">जाति विवरण</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Jati -->
                        <div>
                            <label for="jati" class="block text-sm font-medium text-navy-700 mb-2">जाति</label>
                            <input type="text" name="jati" id="jati" value="{{ old('jati', $user->jati) }}"
                                   class="form-input w-full">
                            @error('jati')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Upjati -->
                        <div>
                            <label for="upjati" class="block text-sm font-medium text-navy-700 mb-2">उप जाति</label>
                            <input type="text" name="upjati" id="upjati" value="{{ old('upjati', $user->upjati) }}"
                                   class="form-input w-full">
                            @error('upjati')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Gotra -->
                        <div>
                            <label for="gotra" class="block text-sm font-medium text-navy-700 mb-2">गोत्र</label>
                            <input type="text" name="gotra" id="gotra" value="{{ old('gotra', $user->gotra) }}"
                                   class="form-input w-full">
                            @error('gotra')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education and Professional Information Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">शिक्षा और व्यवसाय</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Educational Qualification -->
                        <div>
                            <label for="saikshanik_yogyata" class="block text-sm font-medium text-navy-700 mb-2">शैक्षणिक योग्यता</label>
                            <input type="text" name="saikshanik_yogyata" id="saikshanik_yogyata" value="{{ old('saikshanik_yogyata', $user->saikshanik_yogyata) }}"
                                   class="form-input w-full">
                            @error('saikshanik_yogyata')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Upjivika (Occupation) -->
                        <div>
                            <label for="upjivika" class="block text-sm font-medium text-navy-700 mb-2">उपजीविका/व्यवसाय *</label>
                            <input type="text" name="upjivika" id="upjivika" value="{{ old('upjivika', $user->upjivika) }}"
                                   class="form-input w-full @error('upjivika') border-red-500 @enderror" required
                                   placeholder="जैसे: शिक्षक, डॉक्टर, व्यापारी">
                            @error('upjivika')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Current Work (Legacy field) -->
                        <div>
                            <label for="vartaman_karya" class="block text-sm font-medium text-navy-700 mb-2">वर्तमान कार्य / व्यवसाय (पुराना)</label>
                            <input type="text" name="vartaman_karya" id="vartaman_karya" value="{{ old('vartaman_karya', $user->vartaman_karya) }}"
                                   class="form-input w-full">
                            @error('vartaman_karya')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="text-xs text-gray-500 mt-1">यह फील्ड पुराना है, कृपया ऊपर उपजीविका फील्ड का उपयोग करें</p>
                        </div>

                        <!-- Family Member Membership Number -->
                        <div>
                            <label for="family_member_membership_number" class="block text-sm font-medium text-navy-700 mb-2">
                                परिवारिक सदस्य की सदस्यता संख्या *
                                <span class="text-red-500 text-xs">(आवश्यक)</span>
                            </label>
                            <input type="text" name="family_member_membership_number" id="family_member_membership_number"
                                   value="{{ old('family_member_membership_number', $user->family_member_membership_number) }}"
                                   class="form-input w-full @error('family_member_membership_number') border-red-500 @enderror"
                                   required placeholder="जैसे: CGYS000123">
                            @error('family_member_membership_number')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @else
                                <p class="text-xs text-gray-500 mt-1">कृपया अपने परिवार के सदस्य की मान्य सदस्यता संख्या दर्ज करें</p>
                            @enderror
                        </div>

                        <!-- Height -->
                        <div>
                            <label for="uchai" class="block text-sm font-medium text-navy-700 mb-2">ऊंचाई (से.मी.)</label>
                            <input type="number" name="uchai" id="uchai" value="{{ old('uchai', $user->uchai) }}"
                                   class="form-input w-full">
                            @error('uchai')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Upload Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">फोटो अपडेट करें</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Photo 1 -->
                        <div>
                            <label for="photo1" class="block text-sm font-medium text-navy-700 mb-2">फोटो 1</label>
                            @if($user->photo1)
                                <div class="mb-3">
                                    <img src="{{ asset('storage/' . $user->photo1) }}" alt="Photo 1" class="h-24 w-24 rounded-lg object-cover border">
                                    <p class="text-xs text-gray-500 mt-1">वर्तमान फोटो</p>
                                </div>
                            @endif
                            <input type="file" name="photo1" id="photo1" accept="image/*"
                                   class="form-input w-full text-sm">
                            <p class="text-xs text-gray-500 mt-1">JPG, PNG अधिकतम 2MB</p>
                            @error('photo1')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Photo 2 -->
                        <div>
                            <label for="photo2" class="block text-sm font-medium text-navy-700 mb-2">फोटो 2</label>
                            @if($user->photo2)
                                <div class="mb-3">
                                    <img src="{{ asset('storage/' . $user->photo2) }}" alt="Photo 2" class="h-24 w-24 rounded-lg object-cover border">
                                    <p class="text-xs text-gray-500 mt-1">वर्तमान फोटो</p>
                                </div>
                            @endif
                            <input type="file" name="photo2" id="photo2" accept="image/*"
                                   class="form-input w-full text-sm">
                            <p class="text-xs text-gray-500 mt-1">JPG, PNG अधिकतम 2MB</p>
                            @error('photo2')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Photo 3 -->
                        <div>
                            <label for="photo3" class="block text-sm font-medium text-navy-700 mb-2">फोटो 3</label>
                            @if($user->photo3)
                                <div class="mb-3">
                                    <img src="{{ asset('storage/' . $user->photo3) }}" alt="Photo 3" class="h-24 w-24 rounded-lg object-cover border">
                                    <p class="text-xs text-gray-500 mt-1">वर्तमान फोटो</p>
                                </div>
                            @endif
                            <input type="file" name="photo3" id="photo3" accept="image/*"
                                   class="form-input w-full text-sm">
                            <p class="text-xs text-gray-500 mt-1">JPG, PNG अधिकतम 2MB</p>
                            @error('photo3')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Photo 4 -->
                        <div>
                            <label for="photo4" class="block text-sm font-medium text-navy-700 mb-2">फोटो 4</label>
                            @if($user->photo4)
                                <div class="mb-3">
                                    <img src="{{ asset('storage/' . $user->photo4) }}" alt="Photo 4" class="h-24 w-24 rounded-lg object-cover border">
                                    <p class="text-xs text-gray-500 mt-1">वर्तमान फोटो</p>
                                </div>
                            @endif
                            <input type="file" name="photo4" id="photo4" accept="image/*"
                                   class="form-input w-full text-sm">
                            <p class="text-xs text-gray-500 mt-1">JPG, PNG अधिकतम 2MB</p>
                            @error('photo4')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Biodata Upload -->
                    <div class="mt-6">
                        <label for="biodata" class="block text-sm font-medium text-navy-700 mb-2">बायोडाटा (PDF)</label>
                        @if($user->biodata)
                            <div class="mb-3">
                                <a href="{{ asset('storage/' . $user->biodata) }}" target="_blank" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
                                    </svg>
                                    वर्तमान बायोडाटा देखें
                                </a>
                            </div>
                        @endif
                        <input type="file" name="biodata" id="biodata" accept=".pdf"
                               class="form-input w-full">
                        <p class="text-xs text-gray-500 mt-1">केवल PDF फाइल, अधिकतम 5MB</p>
                        @error('biodata')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Password Change Section -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">पासवर्ड बदलें (वैकल्पिक)</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Current Password -->
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-navy-700 mb-2">वर्तमान पासवर्ड</label>
                            <input type="password" name="current_password" id="current_password"
                                   class="form-input w-full">
                            @error('current_password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-navy-700 mb-2">नया पासवर्ड</label>
                            <input type="password" name="password" id="password"
                                   class="form-input w-full" minlength="8">
                            @error('password')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div class="md:col-span-2">
                            <label for="password_confirmation" class="block text-sm font-medium text-navy-700 mb-2">नया पासवर्ड पुष्टि करें</label>
                            <input type="password" name="password_confirmation" id="password_confirmation"
                                   class="form-input w-full" minlength="8">
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        पासवर्ड बदलने के लिए सभी तीन फील्ड भरना आवश्यक है। न्यूनतम 8 अक्षर होने चाहिए।
                    </p>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
                <a href="{{ route('vaivahik.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
                    रद्द करें
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    प्रोफाइल अपडेट करें
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    .form-input {
        @apply border border-gray-300 focus:border-pink-500 focus:ring focus:ring-pink-200 focus:ring-opacity-50 rounded-md shadow-sm px-4 py-2;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile number formatting
        const mobileInput = document.getElementById('mobile');
        if (mobileInput) {
            mobileInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 10) {
                    value = value.slice(0, 10);
                }
                e.target.value = value;
            });
        }

        // Password validation
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('password_confirmation');
        const currentPasswordInput = document.getElementById('current_password');

        function validatePasswords() {
            if (passwordInput && confirmPasswordInput) {
                if (passwordInput.value && confirmPasswordInput.value) {
                    if (passwordInput.value !== confirmPasswordInput.value) {
                        confirmPasswordInput.setCustomValidity('पासवर्ड मेल नहीं खाते');
                    } else {
                        confirmPasswordInput.setCustomValidity('');
                    }
                }
            }
        }

        if (passwordInput) {
            passwordInput.addEventListener('input', validatePasswords);
        }
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', validatePasswords);
        }

        // Form validation
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Check if password fields are filled
                const password = passwordInput ? passwordInput.value.trim() : '';
                const confirmPassword = confirmPasswordInput ? confirmPasswordInput.value.trim() : '';
                const currentPassword = currentPasswordInput ? currentPasswordInput.value.trim() : '';

                // If any password field is filled, all must be filled
                if (password || confirmPassword || currentPassword) {
                    if (!password || !confirmPassword || !currentPassword) {
                        e.preventDefault();
                        alert('पासवर्ड बदलने के लिए सभी तीन पासवर्ड फील्ड भरना आवश्यक है।');
                        return false;
                    }

                    if (password !== confirmPassword) {
                        e.preventDefault();
                        alert('नया पासवर्ड और पुष्टि पासवर्ड मेल नहीं खाते।');
                        return false;
                    }

                    if (password.length < 8) {
                        e.preventDefault();
                        alert('पासवर्ड कम से कम 8 अक्षर का होना चाहिए।');
                        return false;
                    }
                }
            });
        }
    });
</script>
@endsection