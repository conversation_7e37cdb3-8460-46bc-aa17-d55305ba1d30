@extends('layouts.admin') @section('title', 'यादव व्यापार ग्राहक विवरण') @section('content') <div class="p-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"> <div> <p class="text-2xl font-bold admin-text-secondary">यादव व्यापार ग्राहक विवरण</p> <p class="admin-text-secondary mt-1">{{ $yadavVyapar->naam }} की जानकारी</p> </div> <div class="flex gap-2"> <a href="{{ route('admin.yadav-vyapar-grahak.edit', $yadavVyapar->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-edit"></i> संपादित करें </a> <a href="{{ route('admin.yadav-vyapar-grahak.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-arrow-left"></i> वापस जाएं </a> </div> </div> <!-- Details Grid --> <div class="grid grid-cols-1 lg:grid-cols-2 gap-6"> <!-- Category and Membership Information --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary flex items-center"> <i class="fas fa-tags mr-2 text-blue-500"></i> श्रेणी और सदस्यता विवरण </h3> </div> <div class="p-6 space-y-4"> <div class="grid grid-cols-1 sm:grid-cols-2 gap-4"> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">श्रेणी</label> <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full @if($yadavVyapar->category == 'swasthya') bg-green-100 text-green-800 @elseif($yadavVyapar->category == 'gharelu') bg-blue-100 text-blue-800 @elseif($yadavVyapar->category == 'salahkar') bg-purple-100 text-purple-800 @else bg-gray-100 text-gray-800 @endif"> {{ $yadavVyapar->category_name }} </span> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">सदस्यता क्रमांक</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->sadasyata_kramank }}</p> </div> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">सदस्यता प्रकार</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->sadasyata_prakar }}</p> </div> </div> </div> <!-- Personal Information --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary flex items-center"> <i class="fas fa-user mr-2 text-green-500"></i> व्यक्तिगत जानकारी </h3> </div> <div class="p-6 space-y-4"> <div class="grid grid-cols-1 sm:grid-cols-2 gap-4"> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">नाम</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->naam }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">आजीविका</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->ajivika }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">मोबाइल नंबर</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->mobile_number }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">वार्ड</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->ward }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">विकासखंड</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->vikaskhand }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">जिला</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->jila }}</p> </div> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">पता</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->address }}</p> </div> </div> </div> <!-- Photo Information --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary flex items-center"> <i class="fas fa-image mr-2 text-purple-500"></i> फोटो </h3> </div> <div class="p-6"> @if($yadavVyapar->photo) <div class="text-center"> <img src="{{ $yadavVyapar->photo_url }}" alt="{{ $yadavVyapar->naam }}" class="w-32 h-32 object-cover rounded-lg mx-auto mb-4 border-2 border-gray-200"> <div class="flex justify-center"> <a href="{{ route('admin.yadav-vyapar-grahak.download-photo', $yadavVyapar->id) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-download"></i> डाउनलोड </a> </div> </div> @else <div class="text-center py-8"> <i class="fas fa-image text-4xl text-gray-400 mb-2"></i> <p class="text-sm admin-text-secondary">कोई फोटो अपलोड नहीं की गई</p> </div> @endif </div> </div> <!-- Location Information --> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary flex items-center"> <i class="fas fa-map-marker-alt mr-2 text-red-500"></i> स्थान की जानकारी </h3> </div> <div class="p-6"> @if($yadavVyapar->gmap_link) <div class="bg-gray-50 p-4 rounded-lg"> <div class="flex items-center justify-between mb-3"> <div class="flex items-center"> <i class="fas fa-map-marker-alt text-red-500 text-2xl mr-3"></i> <div> <p class="text-sm font-medium admin-text-primary">गूगल मैप लिंक</p> <p class="text-xs admin-text-secondary">{{ $yadavVyapar->hasValidCoordinates() ? 'निर्देशांक' : 'मैप लिंक' }}</p> </div> </div> <a href="{{ $yadavVyapar->formatted_gmap_link }}" target="_blank" class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-external-link-alt"></i> खोलें </a> </div> <div class="text-sm admin-text-secondary break-all"> <strong>लिंक:</strong> {{ $yadavVyapar->gmap_link }} </div> </div> @else <div class="text-center py-8"> <i class="fas fa-map-marker-alt text-4xl text-gray-400 mb-2"></i> <p class="text-sm admin-text-secondary">कोई स्थान की जानकारी प्रदान नहीं की गई</p> </div> @endif </div> </div> <!-- Record Information --> <div class="admin-card lg:col-span-2"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-secondary flex items-center"> <i class="fas fa-info-circle mr-2 text-orange-500"></i> रिकॉर्ड की जानकारी </h3> </div> <div class="p-6"> <div class="grid grid-cols-1 sm:grid-cols-2 gap-4"> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">जमा करने की तारीख</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->created_at->format('d/m/Y H:i') }}</p> </div> <div> <label class="block text-sm font-medium admin-text-secondary mb-1">अंतिम अपडेट</label> <p class="text-sm admin-text-primary bg-gray-50 p-2 rounded">{{ $yadavVyapar->updated_at->format('d/m/Y H:i') }}</p> </div> </div> </div> </div> </div> <!-- Action Buttons --> <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center"> <a href="{{ route('admin.yadav-vyapar-grahak.edit', $yadavVyapar->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"> <i class="fas fa-edit"></i> संपादित करें </a> <button type="button" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2" onclick="confirmDelete({{ $yadavVyapar->id }})"> <i class="fas fa-trash"></i> हटाएं </button> <a href="{{ route('admin.yadav-vyapar-grahak.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"> <i class="fas fa-arrow-left"></i> वापस जाएं </a> </div> </div> <!-- Delete Confirmation Modal --> <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"> <div class="mt-3 text-center"> <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100"> <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i> </div> <h3 class="text-lg font-medium admin-text-primary mt-4">रिकॉर्ड हटाएं</h3> <div class="mt-2 px-7 py-3"> <p class="text-sm admin-text-secondary"> क्या आप वाकई इस रिकॉर्ड को हटाना चाहते हैं? यह कार्य पूर्ववत नहीं किया जा सकता। </p> </div> <div class="flex gap-3 mt-4"> <button type="button" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300" onclick="closeDeleteModal()"> रद्द करें </button> <form id="deleteForm" method="POST" style="display: inline;"> @csrf @method('DELETE') <button type="submit" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"> हटाएं </button> </form> </div> </div> </div> </div> @endsection @push('scripts') <script> function confirmDelete(recordId) { const deleteForm = document.getElementById('deleteForm'); deleteForm.action = `/admin/yadav-vyapar-grahak/${recordId}`; document.getElementById('deleteModal').classList.remove('hidden'); } function closeDeleteModal() { document.getElementById('deleteModal').classList.add('hidden'); } // Close modal when clicking outside document.getElementById('deleteModal').addEventListener('click', function(e) { if (e.target === this) { closeDeleteModal(); } }); </script> @endpush 