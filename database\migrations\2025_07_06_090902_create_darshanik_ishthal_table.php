<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('darshanik_ishthal', function (Blueprint $table) {
            $table->id();
            $table->string('place_name'); // दर्शनिक स्थल का नाम
            $table->string('guide_name'); // यादव टूर ट्रैवल्स गाइड का नाम
            $table->string('guide_mobile', 10); // गाइड का मोबाइल नंबर
            $table->text('guide_address')->nullable(); // गाइड का पता
            $table->text('place_details')->nullable(); // स्थान की जानकारी
            $table->text('guide_details')->nullable(); // गाइड की जानकारी
            $table->text('transport_info')->nullable(); // वाहन संबंधित जानकारी
            $table->text('accommodation_info')->nullable(); // रुकने संबंधित जानकारी
            $table->text('other_info')->nullable(); // अन्य जानकारी
            $table->string('submitted_by_name'); // सबमिट करने वाले का नाम
            $table->string('submitted_by_mobile', 10); // सबमिट करने वाले का मोबाइल
            $table->string('membership_number')->nullable(); // सदस्यता संख्या (वैकल्पिक)
            $table->boolean('is_verified')->default(false); // सत्यापन स्थिति
            $table->boolean('is_active')->default(true); // सक्रिय स्थिति
            $table->timestamps();

            // Indexes for better performance
            $table->index('place_name');
            $table->index('guide_name');
            $table->index('is_verified');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('darshanik_ishthal');
    }
};
