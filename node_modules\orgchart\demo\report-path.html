<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Organization Chart Plugin</title>
  <link rel="icon" href="img/logo.png">
  <link rel="stylesheet" href="css/jquery.orgchart.css">
  <link rel="stylesheet" href="css/style.css">
  <style type="text/css">
    #edit-panel {
      margin: 0.5rem;
      padding: 0.5rem;
      text-align: center;
      border: 1px solid #aaa;
    }
    #edit-panel * { font-size: 1rem; }
    button, input { padding: 0.5rem 1rem; }
  </style>
</head>
<body>
  <div id="chart-container"></div>
  <div id="edit-panel" class="view-state">
    <input type="text" id="selected-node" placeholder="please select node" readonly="true">
    <button type="button" id="btn-report-path">draw report path</button>
    <button type="button" id="btn-reset">reset</button>
  </div>

  <script type="text/javascript" src="js/jquery.min.js"></script>
  <script type="text/javascript" src="js/jquery.orgchart.js"></script>
  <script type="text/javascript">
    $(function() {

    var datascource = {
      'name': 'Lao Lao',
      'title': 'general manager',
      'children': [
        { 'name': 'Bo Miao', 'title': 'department manager',
          'children': [
            { 'name': 'Li Jing', 'title': 'senior engineer' },
            { 'name': 'Li Xin', 'title': 'senior engineer',
              'children': [
                { 'name': 'To To', 'title': 'engineer' },
                { 'name': 'Fei Fei', 'title': 'engineer' },
                { 'name': 'Xuan Xuan', 'title': 'engineer' }
              ]
            }
          ]
        },
        { 'name': 'Su Miao', 'title': 'department manager',
          'children': [
            { 'name': 'Pang Pang', 'title': 'senior engineer' },
            { 'name': 'Hei Hei', 'title': 'senior engineer',
              'children': [
                { 'name': 'Xiang Xiang', 'title': 'UE engineer' },
                { 'name': 'Dan Dan', 'title': 'engineer' },
                { 'name': 'Zai Zai', 'title': 'engineer' }
              ]
            }
          ]
        }
      ]
    };

    var oc = $('#chart-container').orgchart({
      'data' : datascource,
      'nodeContent': 'title'
    });

    oc.$chart.find('.node').on('click', function() {
      $('#selected-node').val($(this).children('.title').text());
    });

    $('#btn-report-path').on('click', function() {
      var $selected = $('#chart-container').find('.node.focused');
      if ($selected.length) {
        $selected.parents('.nodes').children(':has(.focused)').find('.node:first').each(function(index, superior) {
          if (!$(superior).find('.horizontalEdge:first').closest('.node').parent().siblings().is('.hidden')) {
            $(superior).find('.horizontalEdge:first').trigger('click');
          }
        });
        $(this).prop('disabled', true);
      } else {
        alert('please select the node firstly');
      }
    });

    $('#btn-reset').on('click', function() {
      $('#chart-container')
        .find('.hidden').removeClass('hidden')
        .end().find('.slide-up, .slide-right, .slide-left, .focused')
        .removeClass('slide-up slide-right slide-left focused');
      $('#chart-container')
        .find('.isCollapsedSibling, .isChildrenCollapsed, .isSiblingsCollapsed')
        .removeClass('isCollapsedSibling isChildrenCollapsed isSiblingsCollapsed');
        
      $('#btn-report-path').prop('disabled', false);
      $('#selected-node').val('');
    });

  });
  </script>
  </body>
</html>