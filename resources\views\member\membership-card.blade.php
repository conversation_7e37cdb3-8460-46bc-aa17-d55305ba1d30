<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>सदस्यता कार्ड - {{ $member->name }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Devanagari', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            color: #333;
        }

        .card-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .organization-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }

        .card-title {
            font-size: 14px;
            font-weight: 500;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .card-body {
            padding: 25px 20px;
        }

        .member-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #667eea;
            margin: 0 auto 15px;
            display: block;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .member-photo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .member-name {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
            color: #2d3748;
        }

        .member-details {
            background: #f8fafc;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            color: #718096;
            font-weight: 500;
        }

        .detail-value {
            color: #2d3748;
            font-weight: 600;
            text-align: right;
        }

        .membership-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-approved {
            background: #c6f6d5;
            color: #22543d;
        }

        .card-footer {
            background: #f8fafc;
            padding: 15px 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .validity {
            font-size: 11px;
            color: #718096;
            margin-bottom: 8px;
        }

        .signature-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .signature {
            text-align: center;
            font-size: 10px;
            color: #718096;
        }

        .signature-line {
            width: 80px;
            height: 1px;
            background: #cbd5e0;
            margin: 5px auto;
        }

        .qr-placeholder {
            width: 40px;
            height: 40px;
            background: #e2e8f0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: #718096;
        }

        .decorative-border {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            pointer-events: none;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .card-container {
                box-shadow: none;
                max-width: none;
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="card-container">
        <div class="card-header">
            <div class="decorative-border"></div>
            <div class="organization-name">छत्तीसगढ़ यादव समाज</div>
            <div class="card-title">सदस्यता पहचान पत्र</div>
        </div>

        <div class="card-body">
            @if ($member->photo)
                <img src="{{ asset('storage/' . $member->photo) }}" alt="{{ $member->name }}" class="member-photo">
            @else
                <div class="member-photo-placeholder">
                    {{ strtoupper(substr($member->name, 0, 1)) }}
                </div>
            @endif


            <div class="member-name">{{ $member->name }}</div>

            <div class="membership-number">
                {{ $member->membership_number }}
            </div>

            <div class="member-details">
                <div class="detail-row">
                    <span class="detail-label">पिता/पति का नाम:</span>
                    <span class="detail-value">{{ $member->fathers_husband_name }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">मोबाइल नंबर:</span>
                    <span class="detail-value">{{ $member->mobile_number }}</span>
                </div>
                @if ($member->birth_date)
                    <div class="detail-row">
                        <span class="detail-label">जन्म तिथि:</span>
                        <span class="detail-value">{{ $member->birth_date->format('d/m/Y') }}</span>
                    </div>
                @endif
                <div class="detail-row">
                    <span class="detail-label">सदस्यता प्रकार:</span>
                    <span class="detail-value">{{ ucfirst($member->membership_type) }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">स्थिति:</span>
                    <span class="detail-value">
                        <span class="status-badge status-approved">{{ $member->getStatusDisplayText() }}</span>
                    </span>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <div class="validity">
                जारी दिनांक: {{ $member->created_at->format('d/m/Y') }}
                @if ($member->isApproved())
                    | स्वीकृत: {{ $member->updated_at->format('d/m/Y') }}
                @endif
            </div>

            <div class="signature-section">
                <div class="signature">
                    @if ($member->signature)
                        <img src="{{ public_path('storage/' . $member->signature) }}" alt="Member Signature"
                            style="width: 80px; height: 30px; object-fit: contain; border-bottom: 1px solid #cbd5e0;">
                    @else
                        <div class="signature-line"></div>
                    @endif
                    <div>सदस्य हस्ताक्षर</div>
                </div>

                <div class="qr-placeholder">
                    QR
                </div>

                <div class="signature">
                    <div class="signature-line"></div>
                    <div>अधिकृत हस्ताक्षर</div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
