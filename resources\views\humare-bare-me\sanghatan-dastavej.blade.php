@extends('layouts.app')

@section('title', 'संगठन के दस्तावेज')

@section('content')
<div class="bg-white py-12">
    <div class="container mx-auto px-4">
        <!-- Page Header -->
        <div class="mb-12 text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-4">संगठन के दस्तावेज</h1>
            <div class="w-24 h-1 bg-orange-500 mx-auto mb-6"></div>
            <p class="text-gray-700 max-w-2xl mx-auto text-lg">
                यहां आप यादव समाज से संबंधित सभी महत्वपूर्ण दस्तावेज देख और डाउनलोड कर सकते हैं।
            </p>
        </div>
        
        <div class="max-w-6xl mx-auto">
            @if($documents->count() > 0)
                <!-- Documents Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($documents as $document)
                        <div class="document-card bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-blue-300 transform hover:-translate-y-1 overflow-hidden">
                            <!-- Document Header -->
                            <div class="document-content p-6 bg-gradient-to-br from-white to-blue-50">
                                <!-- File Type Icon -->
                                <div class="flex items-center justify-between mb-6">
                                    <div class="flex items-center">
                                        @php
                                            $fileType = strtolower($document->file_type);
                                            $iconClass = 'fas fa-file-alt';
                                            $iconColor = 'text-gray-600';
                                            $bgColor = 'bg-gray-100';

                                            switch($fileType) {
                                                case 'pdf':
                                                    $iconClass = 'fas fa-file-pdf';
                                                    $iconColor = 'text-red-600';
                                                    $bgColor = 'bg-red-100';
                                                    break;
                                                case 'doc':
                                                case 'docx':
                                                    $iconClass = 'fas fa-file-word';
                                                    $iconColor = 'text-blue-600';
                                                    $bgColor = 'bg-blue-100';
                                                    break;
                                                case 'xls':
                                                case 'xlsx':
                                                    $iconClass = 'fas fa-file-excel';
                                                    $iconColor = 'text-green-600';
                                                    $bgColor = 'bg-green-100';
                                                    break;
                                                case 'ppt':
                                                case 'pptx':
                                                    $iconClass = 'fas fa-file-powerpoint';
                                                    $iconColor = 'text-orange-600';
                                                    $bgColor = 'bg-orange-100';
                                                    break;
                                                case 'jpg':
                                                case 'jpeg':
                                                case 'png':
                                                    $iconClass = 'fas fa-file-image';
                                                    $iconColor = 'text-purple-600';
                                                    $bgColor = 'bg-purple-100';
                                                    break;
                                                case 'txt':
                                                    $iconClass = 'fas fa-file-alt';
                                                    $iconColor = 'text-gray-600';
                                                    $bgColor = 'bg-gray-100';
                                                    break;
                                            }
                                        @endphp

                                        <div class="flex items-center {{ $bgColor }} rounded-lg p-3 mr-4">
                                            <i class="{{ $iconClass }} {{ $iconColor }} text-2xl mr-3"></i>
                                            <span class="inline-block px-3 py-1 text-xs font-bold text-gray-700 bg-white rounded-full shadow-sm">
                                                {{ strtoupper($document->file_type) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-semibold text-gray-700">{{ $document->file_size_human }}</div>
                                        <div class="text-xs text-gray-500">फ़ाइल साइज़</div>
                                    </div>
                                </div>

                                <!-- Document Title -->
                                <h3 class="text-lg font-semibold text-blue-900 mb-3 line-clamp-2">
                                    {{ $document->title }}
                                </h3>

                                <!-- Document Description -->
                                @if($document->description)
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                        {{ $document->description }}
                                    </p>
                                @endif

                                <!-- Document Meta Info -->
                                <div class="flex items-center justify-between text-sm text-gray-600 mb-6 pt-4 border-t border-gray-100">
                                    <span class="flex items-center bg-gray-100 px-3 py-1 rounded-full">
                                        <i class="fas fa-download mr-2 text-blue-600"></i>
                                        <span class="font-medium">{{ $document->download_count }} डाउनलोड</span>
                                    </span>
                                    <span class="flex items-center bg-gray-100 px-3 py-1 rounded-full">
                                        <i class="fas fa-calendar mr-2 text-blue-600"></i>
                                        <span class="font-medium">{{ $document->created_at->format('d/m/Y') }}</span>
                                    </span>
                                </div>

                                <!-- Download Button -->
                                <div class="button-area">
                                    <div class="flex gap-3">
                                        <a href="{{ route('humare-bare-me.documents.download', $document->id) }}"
                                           class="download-btn flex-1 px-4 py-3 rounded-lg transition-all duration-200 text-center text-sm font-bold shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                            <i class="fas fa-download mr-2"></i>
                                            डाउनलोड करें
                                        </a>

                                        @if(in_array(strtolower($document->file_type), ['jpg', 'jpeg', 'png', 'pdf']))
                                            <a href="{{ Storage::url($document->file_path) }}"
                                               target="_blank"
                                               class="preview-btn px-4 py-3 rounded-lg transition-all duration-200 text-sm font-bold shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                                View
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Download Instructions -->
                <div class="mt-12 bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-navy-blue mb-4">डाउनलोड निर्देश</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-500 mr-2 mt-1"></i>
                            <span>डाउनलोड बटन पर क्लिक करके फ़ाइल को अपने डिवाइस में सेव करें</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-eye text-green-500 mr-2 mt-1"></i>
                            <span>आंख के आइकन पर क्लिक करके फ़ाइल को ब्राउज़र में देखें</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-mobile-alt text-purple-500 mr-2 mt-1"></i>
                            <span>मोबाइल डिवाइस पर भी सभी दस्तावेज उपलब्ध हैं</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-shield-alt text-orange-500 mr-2 mt-1"></i>
                            <span>सभी फ़ाइलें सुरक्षित और वायरस-मुक्त हैं</span>
                        </div>
                    </div>
                </div>
            @else
                <!-- No Documents Message -->
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-file-alt text-6xl text-gray-300 mb-6"></i>
                        <h3 class="text-xl font-semibold text-gray-700 mb-4">कोई दस्तावेज उपलब्ध नहीं</h3>
                        <p class="text-gray-500 mb-6">
                            फिलहाल कोई दस्तावेज प्रकाशित नहीं किया गया है। कृपया बाद में दोबारा देखें।
                        </p>
                        <a href="{{ route('humare-bare-me.uddeshya') }}"
                           class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-semibold shadow-lg">
                            <i class="fas fa-arrow-left mr-2"></i>
                            वापस जाएं
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Document Categories (if you want to add categories later) -->
@if($documents->count() > 5)
    <div class="bg-gray-50 py-8">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <h3 class="text-xl font-semibold text-navy-blue mb-6 text-center">दस्तावेज श्रेणियां</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @php
                        $categories = $documents->groupBy('file_type');
                    @endphp
                    @foreach($categories as $type => $docs)
                        <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                            <div class="text-2xl mb-2">
                                @switch(strtolower($type))
                                    @case('pdf')
                                        <i class="fas fa-file-pdf text-red-500"></i>
                                        @break
                                    @case('doc')
                                    @case('docx')
                                        <i class="fas fa-file-word text-blue-500"></i>
                                        @break
                                    @case('xls')
                                    @case('xlsx')
                                        <i class="fas fa-file-excel text-green-500"></i>
                                        @break
                                    @default
                                        <i class="fas fa-file-alt text-gray-500"></i>
                                @endswitch
                            </div>
                            <div class="text-sm font-medium text-gray-700">{{ strtoupper($type) }}</div>
                            <div class="text-xs text-gray-500">{{ $docs->count() }} फ़ाइलें</div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endif
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Ensure buttons are always visible with proper contrast */
.document-card {
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.document-card .document-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.document-card .button-area {
    margin-top: auto;
    padding-top: 1rem;
}

/* Force button visibility */
.download-btn {
    background-color: #2563eb !important;
    color: white !important;
    border: 2px solid #2563eb !important;
}

.download-btn:hover {
    background-color: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
}

.preview-btn {
    background-color: white !important;
    color: #2563eb !important;
    border: 2px solid #2563eb !important;
}

.preview-btn:hover {
    background-color: #2563eb !important;
    color: white !important;
}
</style>
@endpush
