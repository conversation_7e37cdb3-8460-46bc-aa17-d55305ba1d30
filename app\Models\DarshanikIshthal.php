<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DarshanikIshthal extends Model
{
    use HasFactory;

    protected $table = 'darshanik_ishthal';

    protected $fillable = [
        'place_name',
        'guide_name',
        'guide_mobile',
        'guide_address',
        'place_details',
        'guide_details',
        'transport_info',
        'accommodation_info',
        'other_info',
        'submitted_by_name',
        'submitted_by_mobile',
        'membership_number',
        'is_verified',
        'is_active'
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Scope for verified entries only
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for active entries only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for public display (verified and active)
     */
    public function scopePublic($query)
    {
        return $query->verified()->active();
    }

    /**
     * Get the membership details if membership number exists
     */
    public function getMembershipDetails()
    {
        if ($this->membership_number) {
            return \App\Models\Membership::where('membership_number', $this->membership_number)->first();
        }
        return null;
    }

    /**
     * Check if the entry can be verified with given membership number
     */
    public static function verifyWithMembership($id, $membershipNumber)
    {
        $entry = self::find($id);
        if (!$entry) {
            return false;
        }

        // Check if membership number exists in database
        $membership = \App\Models\Membership::where('membership_number', $membershipNumber)
            ->where('status', 'approved')
            ->first();

        return $membership ? $entry : false;
    }
}
