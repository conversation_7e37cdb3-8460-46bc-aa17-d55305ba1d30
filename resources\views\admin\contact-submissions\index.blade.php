@extends('layouts.admin') @section('title', 'संपर्क सबमिशन') @section('content') <div class="p-6"> <!-- Page Header --> <div class="mb-6 flex justify-between items-center"> <div> <h1 class="text-2xl font-bold text-gray-900">संपर्क सबमिशन</h1> <p class="mt-1 text-sm text-gray-600">सभी संपर्क फॉर्म सबमिशन देखें और प्रबंधित करें</p> </div> </div> <!-- Filters --> <div class="bg-white shadow-sm rounded-lg p-6 mb-6 border border-gray-200"> <form method="GET" action="{{ route('admin.contact-submissions.index') }}" class="flex flex-col sm:flex-row gap-4"> <div class="flex-1"> <label class="block text-sm font-medium text-gray-700 mb-2">खोजें</label> <input type="text" name="search" value="{{ request('search') }}" placeholder="नाम, ईमेल, विषय या संदेश में खोजें..." class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"> </div> <div> <label class="block text-sm font-medium text-gray-700 mb-2">स्थिति</label> <select name="status" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white"> <option value="">सभी स्थितियां</option> <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>लंबित</option> <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>पढ़ा गया</option> <option value="replied" {{ request('status') === 'replied' ? 'selected' : '' }}>उत्तर दिया गया</option> </select> </div> <div class="flex gap-2 items-end"> <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors shadow-sm"> खोजें </button> <a href="{{ route('admin.contact-submissions.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors shadow-sm"> रीसेट </a> </div> </form> </div> <!-- Submissions Table --> <div class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200"> @if($submissions->count() > 0) <div class="overflow-x-auto"> <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">नाम</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ईमेल</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">विषय</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">स्थिति</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">सुझाव</th> <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">दिनांक</th> <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">कार्य</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @foreach($submissions as $submission) <tr class="hover:bg-gray-50"> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm font-medium text-gray-900">{{ $submission->name }}</div> @if($submission->phone) <div class="text-sm text-gray-500">{{ $submission->phone }}</div> @endif </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900">{{ $submission->email }}</div> </td> <td class="px-6 py-4"> <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $submission->subject }}">{{ $submission->subject }}</div> </td> <td class="px-6 py-4 whitespace-nowrap"> <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $submission->status_badge_color }}"> {{ $submission->status_text }} </span> </td> <td class="px-6 py-4 whitespace-nowrap"> @if($submission->suggestion) <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"> हां </span> @else <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"> नहीं </span> @endif </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900">{{ $submission->created_at->format('d/m/Y') }}</div> <div class="text-sm text-gray-500">{{ $submission->created_at->format('H:i') }}</div> </td> <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"> <div class="flex justify-end space-x-2"> <a href="{{ route('admin.contact-submissions.show', $submission) }}" class="text-blue-600 hover:text-blue-900"> देखें </a> <form action="{{ route('admin.contact-submissions.destroy', $submission) }}" method="POST" class="inline" onsubmit="return confirm('क्या आप वाकई इस सबमिशन को हटाना चाहते हैं?')"> @csrf @method('DELETE') <button type="submit" class="text-red-600 hover:text-red-900"> हटाएं </button> </form> </div> </td> </tr> @endforeach </tbody> </table> </div> <!-- Pagination --> <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6"> {{ $submissions->links() }} </div> @else <div class="text-center py-12"> <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" /> </svg> <h3 class="mt-2 text-sm font-medium text-gray-900">कोई संपर्क सबमिशन नहीं मिला</h3> <p class="mt-1 text-sm text-gray-500">अभी तक कोई संपर्क फॉर्म सबमिशन नहीं आया है।</p> </div> @endif </div> </div> @endsection 