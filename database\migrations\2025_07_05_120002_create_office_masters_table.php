<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('office_masters', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'सरकारी कार्यालय', 'निजी कंपनी', 'स्वरोजगार'
            $table->string('name_english')->nullable(); // English name for reference
            $table->text('description')->nullable(); // Description of office type
            $table->string('category')->nullable(); // e.g., 'government', 'private', 'self-employed'
            $table->boolean('is_active')->default(true); // Active/Inactive status
            $table->integer('display_order')->default(0); // For ordering in dropdowns
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('office_masters');
    }
};
