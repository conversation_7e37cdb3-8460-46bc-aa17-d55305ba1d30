@extends('layouts.admin') @section('title', '<PERSON><PERSON>uk<PERSON> Details') @section('content') <div class="container mx-auto px-4 py-8"> <!-- Header --> <div class="flex items-center justify-between mb-8"> <div class="flex items-center"> <a href="{{ route('admin.pramukh-padadhikaris.index') }}" class="mr-4 text-navy-600 hover:text-navy-800:text-navy-300"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path> </svg> </a> <div> <h1 class="text-3xl font-bold text-navy-900">{{ $pramukhPadadhikari->name }}</h1> <p class="text-gray-600">{{ $pramukhPadadhikari->post_in_community }}</p> </div> </div> <div class="flex space-x-3"> <a href="{{ route('admin.pramukh-padadhikaris.edit', $pramukhPadadhikari) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> <span>Edit</span> </a> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.toggle-status', $pramukhPadadhikari) }}" class="inline"> @csrf @method('PATCH') <button type="submit" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2" onclick="return confirm('Are you sure you want to {{ $pramukhPadadhikari->active ? 'deactivate' : 'activate' }} this pramukh padadhikari?')"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path> </svg> <span>{{ $pramukhPadadhikari->active ? 'Deactivate' : 'Activate' }}</span> </button> </form> </div> </div> <!-- Success/Error Messages --> @if(session('success')) <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6"> {{ session('success') }} </div> @endif @if($errors->any()) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"> <ul class="list-disc list-inside"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> @endif <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> <!-- Main Information --> <div class="lg:col-span-2"> <div class="bg-white rounded-lg shadow-md p-6"> <h2 class="text-xl font-semibold text-gray-900 mb-6">Personal Information</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label class="block text-sm font-medium text-gray-500 mb-1">नाम</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->naam }}</p> </div> <div> <label class="block text-sm font-medium text-gray-500 mb-1">संगठन में पदनाम</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->sangathan_me_padnaam }}</p> </div> @if($pramukhPadadhikari->vibhagiy_padnaam) <div> <label class="block text-sm font-medium text-gray-500 mb-1">विभागीय पदनाम</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->vibhagiy_padnaam }}</p> </div> @endif @if($pramukhPadadhikari->vibhag_ka_naam) <div> <label class="block text-sm font-medium text-gray-500 mb-1">विभाग का नाम</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->vibhag_ka_naam }}</p> </div> @endif <div> <label class="block text-sm font-medium text-gray-500 mb-1">मोबाइल नंबर</label> <div class="flex items-center space-x-2"> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->formatted_mobile }}</p> <a href="tel:+91{{ $pramukhPadadhikari->mobile_number }}" class="text-navy-600 hover:text-navy-800:text-navy-300"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> </a> </div> </div> <div> <label class="block text-sm font-medium text-gray-500 mb-1">स्थिति</label> <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $pramukhPadadhikari->active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}"> <span class="w-2 h-2 mr-2 rounded-full {{ $pramukhPadadhikari->active ? 'bg-green-400' : 'bg-red-400' }}"></span> {{ $pramukhPadadhikari->status_text }} </span> </div> @if($pramukhPadadhikari->vartaman_pata) <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-500 mb-1">वर्तमान पता</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->vartaman_pata }}</p> </div> @endif @if($pramukhPadadhikari->isthayi_pata) <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-500 mb-1">स्थायी पता</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->isthayi_pata }}</p> </div> @endif @if($pramukhPadadhikari->sanchipt_vishesh) <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-500 mb-1">संक्षिप्त विशेष</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->sanchipt_vishesh }}</p> </div> @endif @if($pramukhPadadhikari->sadasyata_kramank_evam_prakar) <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-500 mb-1">सदस्यता क्रमांक एवं प्रकार</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->sadasyata_kramank_evam_prakar }}</p> </div> @endif </div> </div> <!-- Location Information --> @if($pramukhPadadhikari->division || $pramukhPadadhikari->district || $pramukhPadadhikari->vikaskhand) <div class="bg-white rounded-lg shadow-md p-6 mt-6"> <h2 class="text-xl font-semibold text-gray-900 mb-6">स्थान की जानकारी</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"> @if($pramukhPadadhikari->division) <div> <label class="block text-sm font-medium text-gray-500 mb-1">संभाग</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->division->division_name_hin }}</p> </div> @endif @if($pramukhPadadhikari->district) <div> <label class="block text-sm font-medium text-gray-500 mb-1">जिला</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->district->district_name_hin }}</p> </div> @endif @if($pramukhPadadhikari->vikaskhand) <div> <label class="block text-sm font-medium text-gray-500 mb-1">विकासखंड</label> <p class="text-lg text-gray-900">{{ $pramukhPadadhikari->vikaskhand->sub_district_name_hin }}</p> </div> @endif </div> </div> @endif </div> <!-- Sidebar --> <div class="lg:col-span-1"> <!-- Quick Actions --> <div class="bg-white rounded-lg shadow-md p-6 mb-6"> <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3> <div class="space-y-3"> <a href="tel:+91{{ $pramukhPadadhikari->mobile_number }}" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path> </svg> <span>Call Now</span> </a> <a href="sms:+91{{ $pramukhPadadhikari->mobile_number }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path> </svg> <span>Send SMS</span> </a> <a href="https://wa.me/91{{ $pramukhPadadhikari->mobile_number }}" target="_blank" class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"> <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24"> <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/> </svg> <span>WhatsApp</span> </a> </div> </div> <!-- Record Information --> <div class="bg-white rounded-lg shadow-md p-6"> <h3 class="text-lg font-semibold text-gray-900 mb-4">Record Information</h3> <div class="space-y-4"> <div> <label class="block text-sm font-medium text-gray-500 mb-1">Created</label> <p class="text-sm text-gray-900">{{ $pramukhPadadhikari->created_at->format('F d, Y') }}</p> <p class="text-xs text-gray-500">{{ $pramukhPadadhikari->created_at->format('g:i A') }}</p> </div> @if($pramukhPadadhikari->updated_at != $pramukhPadadhikari->created_at) <div> <label class="block text-sm font-medium text-gray-500 mb-1">Last Updated</label> <p class="text-sm text-gray-900">{{ $pramukhPadadhikari->updated_at->format('F d, Y') }}</p> <p class="text-xs text-gray-500">{{ $pramukhPadadhikari->updated_at->format('g:i A') }}</p> </div> @endif <div> <label class="block text-sm font-medium text-gray-500 mb-1">Record ID</label> <p class="text-sm text-gray-900 font-mono">#{{ $pramukhPadadhikari->id }}</p> </div> </div> </div> <!-- Danger Zone --> <div class="bg-red-50 border border-red-200 rounded-lg p-6 mt-6"> <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3> <form method="POST" action="{{ route('admin.pramukh-padadhikaris.destroy', $pramukhPadadhikari) }}" class="inline"> @csrf @method('DELETE') <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this pramukh padadhikari? This action cannot be undone and will permanently remove all associated data.')"> Delete Pramukh Padadhikari </button> </form> <p class="text-xs text-red-600 mt-2">This action cannot be undone. Please be certain.</p> </div> </div> </div> </div> @endsection 