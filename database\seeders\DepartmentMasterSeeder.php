<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DepartmentMaster;

class DepartmentMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Department Masters
        $departmentMasters = [
            ['name' => 'शिक्षा विभाग', 'name_english' => 'Education Department', 'category' => 'government', 'ministry' => 'शिक्षा मंत्रालय', 'display_order' => 1],
            ['name' => 'स्वास्थ्य विभाग', 'name_english' => 'Health Department', 'category' => 'government', 'ministry' => 'स्वास्थ्य मंत्रालय', 'display_order' => 2],
            ['name' => 'पुलिस विभाग', 'name_english' => 'Police Department', 'category' => 'government', 'ministry' => 'गृह मंत्रालय', 'display_order' => 3],
            ['name' => 'राजस्व विभाग', 'name_english' => 'Revenue Department', 'category' => 'government', 'ministry' => 'वित्त मंत्रालय', 'display_order' => 4],
            ['name' => 'कृषि विभाग', 'name_english' => 'Agriculture Department', 'category' => 'government', 'ministry' => 'कृषि मंत्रालय', 'display_order' => 5],
            ['name' => 'वन विभाग', 'name_english' => 'Forest Department', 'category' => 'government', 'ministry' => 'पर्यावरण मंत्रालय', 'display_order' => 6],
            ['name' => 'परिवहन विभाग', 'name_english' => 'Transport Department', 'category' => 'government', 'ministry' => 'परिवहन मंत्रालय', 'display_order' => 7],
            ['name' => 'लोक निर्माण विभाग', 'name_english' => 'Public Works Department', 'category' => 'government', 'ministry' => 'शहरी विकास मंत्रालय', 'display_order' => 8],
            ['name' => 'न्यायपालिका', 'name_english' => 'Judiciary', 'category' => 'autonomous', 'ministry' => 'न्याय मंत्रालय', 'display_order' => 9],
            ['name' => 'रक्षा विभाग', 'name_english' => 'Defence Department', 'category' => 'government', 'ministry' => 'रक्षा मंत्रालय', 'display_order' => 10],
            ['name' => 'सूचना प्रौद्योगिकी विभाग', 'name_english' => 'IT Department', 'category' => 'government', 'ministry' => 'इलेक्ट्रॉनिक्स मंत्रालय', 'display_order' => 11],
            ['name' => 'निजी क्षेत्र', 'name_english' => 'Private Sector', 'category' => 'private', 'ministry' => null, 'display_order' => 12],
        ];

        foreach ($departmentMasters as $department) {
            DepartmentMaster::firstOrCreate(
                ['name' => $department['name']], // Check by name
                $department // Create with all data if not exists
            );
        }
    }
}
