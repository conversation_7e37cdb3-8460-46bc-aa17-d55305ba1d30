@extends('layouts.admin') @section('title', 'वैवाहिक उपयोगकर्ता विवरण - ' . $user->naam) @section('content') <div class="space-y-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"> <div> <h1 class="text-2xl font-bold admin-text-primary">वैवाहिक उपयोगकर्ता विवरण</h1> <p class="text-gray-600 mt-1">{{ $user->naam }} की जानकारी</p> </div> <a href="{{ route('admin.vaivahik-users.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path> </svg> वापस जाएं </a> </div> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"> <!-- User Profile Card --> <div class="lg:col-span-1"> <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">प्रोफाइल जानकारी</h3> </div> <div class="text-center"> @if($user->photo1) <img src="{{ asset('storage/' . $user->photo1) }}" class="w-32 h-32 rounded-full object-cover mx-auto mb-4 border-4 border-gray-200"> @else <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center"> <svg class="w-16 h-16 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path> </svg> </div> @endif <h4 class="text-xl font-bold admin-text-primary mb-2">{{ $user->naam }}</h4> <p class="text-gray-600 mb-4">{{ $user->ling }}</p> <div class="grid grid-cols-2 gap-4 text-center"> <div class="border-r border-gray-200"> <div class="text-2xl font-bold admin-text-primary">{{ $user->age ?? 'N/A' }}</div> <div class="text-sm text-gray-500">आयु</div> </div> <div> <div class="text-lg font-bold"> @if($user->status === 'approved') <span class="text-green-600">स्वीकृत</span> @elseif($user->status === 'pending') <span class="text-yellow-600">प्रतीक्षा में</span> @else <span class="text-red-600">अस्वीकृत</span> @endif </div> <div class="text-sm text-gray-500">स्थिति</div> </div> </div> </div> </div> <!-- Quick Actions --> <div class="admin-card p-6 mt-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">त्वरित कार्य</h3> </div> <div class="space-y-3"> @if($user->status === 'pending') <form method="POST" action="{{ route('admin.vaivahik-users.approve', $user->id) }}"> @csrf @method('PATCH') <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors" onclick="return confirm('क्या आप इस उपयोगकर्ता को स्वीकृत करना चाहते हैं?')"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path> </svg> स्वीकृत करें </button> </form> <form method="POST" action="{{ route('admin.vaivahik-users.reject', $user->id) }}"> @csrf @method('PATCH') <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors" onclick="return confirm('क्या आप इस उपयोगकर्ता को अस्वीकृत करना चाहते हैं?')"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> अस्वीकृत करें </button> </form> @endif <button type="button" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors" onclick="document.getElementById('updateLoginModal').classList.remove('hidden')"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path> </svg> लॉगिन जानकारी अपडेट करें </button> <form method="POST" action="{{ route('admin.vaivahik-users.destroy', $user->id) }}"> @csrf @method('DELETE') <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors" onclick="return confirm('क्या आप इस उपयोगकर्ता का खाता हटाना चाहते हैं? प्रोफाइल सुरक्षित रहेगी।')"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path> </svg> खाता हटाएं </button> </form> </div> </div> <!-- Photo Gallery --> <div class="admin-card p-6 mt-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">फोटो गैलरी</h3> </div> <div class="grid grid-cols-2 gap-4"> @if($user->photo1) <div class="relative group"> <img src="{{ asset('storage/' . $user->photo1) }}" class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-75 transition-opacity" onclick="openImageModal('{{ asset('storage/' . $user->photo1) }}')"> <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center"> <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path> </svg> </div> </div> @else <div class="w-full h-32 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center"> <span class="text-gray-500 text-sm">फोटो 1 नहीं है</span> </div> @endif @if($user->photo2) <div class="relative group"> <img src="{{ asset('storage/' . $user->photo2) }}" class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-75 transition-opacity" onclick="openImageModal('{{ asset('storage/' . $user->photo2) }}')"> <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center"> <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path> </svg> </div> </div> @else <div class="w-full h-32 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center"> <span class="text-gray-500 text-sm">फोटो 2 नहीं है</span> </div> @endif @if($user->photo3) <div class="relative group"> <img src="{{ asset('storage/' . $user->photo3) }}" class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-75 transition-opacity" onclick="openImageModal('{{ asset('storage/' . $user->photo3) }}')"> <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center"> <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path> </svg> </div> </div> @else <div class="w-full h-32 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center"> <span class="text-gray-500 text-sm">फोटो 3 नहीं है</span> </div> @endif @if($user->photo4) <div class="relative group"> <img src="{{ asset('storage/' . $user->photo4) }}" class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-75 transition-opacity" onclick="openImageModal('{{ asset('storage/' . $user->photo4) }}')"> <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center"> <svg class="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path> </svg> </div> </div> @else <div class="w-full h-32 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center"> <span class="text-gray-500 text-sm">फोटो 4 नहीं है</span> </div> @endif </div> </div> </div> <!-- User Details --> <div class="lg:col-span-2 space-y-6"> <!-- Login Information --> <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">लॉगिन जानकारी</h3> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label class="block text-sm font-medium text-gray-600 mb-2">ईमेल:</label> <p class="admin-text-primary">{{ $user->owner_email }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">खाता स्थिति:</label> <p> @if($user->is_active) <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">सक्रिय</span> @else <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">निष्क्रिय</span> @endif </p> </div> </div> </div> <!-- Personal Information --> <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">व्यक्तिगत जानकारी</h3> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label class="block text-sm font-medium text-gray-600 mb-2">मोबाइल नंबर:</label> <p class="admin-text-primary">{{ $user->mobile ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">जन्म तिथि:</label> <p class="admin-text-primary">{{ $user->birth_date_string ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">जन्म समय व दिन:</label> <p class="admin-text-primary">{{ $user->janmasamay_din ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">ऊंचाई:</label> <p class="admin-text-primary">{{ $user->uchai ? $user->uchai . ' से.मी.' : 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">पिता का नाम:</label> <p class="admin-text-primary">{{ $user->pita_ka_naam ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">माता का नाम:</label> <p class="admin-text-primary">{{ $user->mata_ka_naam ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">शिक्षा:</label> <p class="admin-text-primary">{{ $user->saikshanik_yogyata ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">उपजीविका/व्यवसाय:</label> <p class="admin-text-primary">{{ $user->upjivika ?? ($user->vartaman_karya ?? 'N/A') }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">पारिवारिक सदस्यता संख्या:</label> <p class="admin-text-primary">{{ $user->family_member_membership_number ?? 'N/A' }}</p> </div> <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-600 mb-2">पता:</label> <p class="admin-text-primary">{{ $user->pata ?? 'N/A' }}</p> </div> <div class="md:col-span-2"> <label class="block text-sm font-medium text-gray-600 mb-2">भाई-बहन विवरण:</label> <p class="admin-text-primary">{{ $user->bhai_bahan_vivran ?? 'N/A' }}</p> </div> </div> </div> <!-- Family Information --> <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">पारिवारिक जानकारी</h3> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"> <div> <label class="block text-sm font-medium text-gray-600 mb-2">जाति:</label> <p class="admin-text-primary">{{ $user->jati ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">उपजाति:</label> <p class="admin-text-primary">{{ $user->upjati ?? 'N/A' }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">गोत्र:</label> <p class="admin-text-primary">{{ $user->gotra ?? 'N/A' }}</p> </div> </div> </div> <!-- Biodata Section --> @if($user->biodata) <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">बायोडाटा</h3> </div> <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"> <div class="flex items-center"> <svg class="w-8 h-8 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path> </svg> <div> <p class="font-medium admin-text-primary">बायोडाटा PDF</p> <p class="text-sm text-gray-500">अपलोड किया गया</p> </div> </div> <a href="{{ asset('storage/' . $user->biodata) }}" target="_blank" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"> <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> डाउनलोड करें </a> </div> </div> @endif <!-- Registration Information --> <div class="admin-card p-6"> <div class="border-b border-gray-200 pb-4 mb-6"> <h3 class="text-lg font-semibold admin-text-primary">पंजीकरण जानकारी</h3> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div> <label class="block text-sm font-medium text-gray-600 mb-2">पंजीकरण दिनांक:</label> <p class="admin-text-primary">{{ $user->created_at->format('d/m/Y H:i') }}</p> </div> <div> <label class="block text-sm font-medium text-gray-600 mb-2">अंतिम अपडेट:</label> <p class="admin-text-primary">{{ $user->updated_at->format('d/m/Y H:i') }}</p> </div> </div> </div> </div> </div> </div> <!-- Update Login Modal --> <div id="updateLoginModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"> <form method="POST" action="{{ route('admin.vaivahik-users.update-login', $user->id) }}"> @csrf @method('PATCH') <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">लॉगिन जानकारी अपडेट करें</h3> <button type="button" class="text-gray-400 hover:text-gray-600:text-gray-300" onclick="document.getElementById('updateLoginModal').classList.add('hidden')"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> <div class="space-y-4"> <div> <label for="owner_email" class="block text-sm font-medium text-gray-700 mb-2">ईमेल पता</label> <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-navy-500 focus:border-navy-500" id="owner_email" name="owner_email" value="{{ $user->owner_email }}" required> </div> <div> <label for="owner_password" class="block text-sm font-medium text-gray-700 mb-2">नया पासवर्ड (वैकल्पिक)</label> <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-navy-500 focus:border-navy-500" id="owner_password" name="owner_password" placeholder="नया पासवर्ड दर्ज करें"> <p class="text-xs text-gray-500 mt-1">खाली छोड़ें यदि पासवर्ड नहीं बदलना है</p> </div> <div class="flex items-center"> <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" id="is_active" name="is_active" {{ $user->is_active ? 'checked' : '' }}> <label for="is_active" class="ml-2 block text-sm text-gray-700">खाता सक्रिय है</label> </div> </div> <div class="flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200"> <button type="button" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors" onclick="document.getElementById('updateLoginModal').classList.add('hidden')"> रद्द करें </button> <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"> अपडेट करें </button> </div> </form> </div> </div> <!-- Image Modal --> <div id="imageModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden z-50"> <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white"> <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">फोटो देखें</h3> <button type="button" class="text-gray-400 hover:text-gray-600:text-gray-300" onclick="document.getElementById('imageModal').classList.add('hidden')"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </button> </div> <div class="text-center"> <img id="modalImage" src="" alt="फोटो" class="max-w-full max-h-96 mx-auto rounded-lg"> </div> </div> </div> <script> function openImageModal(imageSrc) { document.getElementById('modalImage').src = imageSrc; document.getElementById('imageModal').classList.remove('hidden'); } // Close modals when clicking outside document.getElementById('updateLoginModal').addEventListener('click', function(e) { if (e.target === this) { this.classList.add('hidden'); } }); document.getElementById('imageModal').addEventListener('click', function(e) { if (e.target === this) { this.classList.add('hidden'); } }); </script> @endsection 