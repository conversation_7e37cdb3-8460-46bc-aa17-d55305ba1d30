<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ImageGallery extends Component
{
    public array $images;
    public string $title;
    public bool $showLightbox;
    public string $galleryId;

    /**
     * Create a new component instance.
     */
    public function __construct(
        array $images = [],
        string $title = '',
        bool $showLightbox = true,
        string $galleryId = ''
    ) {
        $this->images = $images;
        $this->title = $title;
        $this->showLightbox = $showLightbox;
        $this->galleryId = $galleryId ?: 'gallery-' . uniqid();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.image-gallery');
    }
}
