<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\Activity;
use App\Models\Membership;
use App\Models\VaivahikPanjiyan;
use App\Models\EkadashSadasyata;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage with dynamic content.
     */
    public function index()
    {
        // Get latest news for homepage (4 items)
        $latestNews = News::where('is_published', true)
            ->orderBy('publication_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();

        // Get latest announcements (general news items that can serve as announcements)
        $announcements = News::where('is_published', true)
            ->where('category', News::CATEGORY_GENERAL)
            ->orderBy('publication_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get विज्ञापन data (advertisements/notices)
        $vigyapan = News::where('is_published', true)
            ->whereIn('category', [News::CATEGORY_PRESS, News::CATEGORY_GENERAL])
            ->orderBy('publication_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        // Get upcoming events (activities with future dates or recent activities)
        $upcomingEvents = Activity::where('is_published', true)
            ->where(function($query) {
                $query->where('event_date', '>=', now())
                      ->orWhere('event_date', '>=', now()->subMonths(1));
            })
            ->orderBy('event_date', 'desc')
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get();

        // Get today's birthdays from all models
        $todaysBirthdays = $this->getTodaysBirthdays();

        // Get latest registrations for activity notifications
        $latestRegistrations = $this->getLatestRegistrations();

        // Get all registration form links
        $registrationForms = $this->getRegistrationForms();

        return view('homepage', compact('latestNews', 'announcements', 'vigyapan', 'upcomingEvents', 'todaysBirthdays', 'latestRegistrations', 'registrationForms'));
    }

    /**
     * Helper method to truncate HTML content safely
     */
    private function truncateHtml($html, $length = 120)
    {
        $text = strip_tags($html);
        return strlen($text) > $length ? substr($text, 0, $length) . '...' : $text;
    }

    /**
     * Get today's birthdays from all models
     */
    private function getTodaysBirthdays()
    {
        $today = now();
        $birthdays = collect();

        // Get birthdays from Membership model
        $membershipBirthdays = Membership::where('status', Membership::STATUS_APPROVED)
            ->where('is_active', true)
            ->whereNotNull('birth_date')
            ->whereRaw('MONTH(birth_date) = ? AND DAY(birth_date) = ?', [$today->month, $today->day])
            ->select('name', 'birth_date', 'membership_number')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'सदस्य',
                    'membership_number' => $member->membership_number,
                    'birth_date' => $member->birth_date
                ];
            });

        // Get birthdays from VaivahikPanjiyan model
        $vaivahikBirthdays = VaivahikPanjiyan::where('status', VaivahikPanjiyan::STATUS_APPROVED)
            ->where('is_active', true)
            ->whereNotNull('janmatithi')
            ->whereRaw('MONTH(janmatithi) = ? AND DAY(janmatithi) = ?', [$today->month, $today->day])
            ->select('naam as name', 'janmatithi as birth_date', 'id')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'वैवाहिक सदस्य',
                    'membership_number' => 'VP' . str_pad($member->id, 6, '0', STR_PAD_LEFT),
                    'birth_date' => $member->birth_date
                ];
            });

        // Get birthdays from EkadashSadasyata model
        $ekadashBirthdays = EkadashSadasyata::where('status', EkadashSadasyata::STATUS_APPROVED)
            ->where('is_active', true)
            ->whereNotNull('birth_date')
            ->whereRaw('MONTH(birth_date) = ? AND DAY(birth_date) = ?', [$today->month, $today->day])
            ->select('name', 'birth_date', 'membership_number')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'एकादश सदस्य',
                    'membership_number' => $member->membership_number,
                    'birth_date' => $member->birth_date
                ];
            });

        return $birthdays->concat($membershipBirthdays)
                        ->concat($vaivahikBirthdays)
                        ->concat($ekadashBirthdays);
    }

    /**
     * Get latest registrations for activity notifications
     */
    private function getLatestRegistrations()
    {
        $registrations = collect();

        // Get latest membership registrations
        $latestMemberships = Membership::where('status', Membership::STATUS_APPROVED)
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->select('name', 'created_at', 'membership_number')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'सदस्यता पंजीयन',
                    'date' => $member->created_at,
                    'membership_number' => $member->membership_number
                ];
            });

        // Get latest vaivahik registrations
        $latestVaivahik = VaivahikPanjiyan::where('status', VaivahikPanjiyan::STATUS_APPROVED)
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->select('naam as name', 'created_at', 'id')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'वैवाहिक पंजीयन',
                    'date' => $member->created_at,
                    'membership_number' => 'VP' . str_pad($member->id, 6, '0', STR_PAD_LEFT)
                ];
            });

        // Get latest ekadash registrations
        $latestEkadash = EkadashSadasyata::where('status', EkadashSadasyata::STATUS_APPROVED)
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->select('name', 'created_at', 'membership_number')
            ->get()
            ->map(function ($member) {
                return [
                    'name' => $member->name,
                    'type' => 'एकादश सदस्यता',
                    'date' => $member->created_at,
                    'membership_number' => $member->membership_number
                ];
            });

        return $registrations->concat($latestMemberships)
                           ->concat($latestVaivahik)
                           ->concat($latestEkadash)
                           ->sortByDesc('date')
                           ->take(5);
    }

    /**
     * Get all registration form links
     */
    private function getRegistrationForms()
    {
        return [
            [
                'title' => 'सदस्य हेतु पंजीयन',
                'description' => 'संघ की सदस्यता के लिए आवेदन करें',
                'route' => 'sadasya.aavedan',
                'icon' => 'user-plus',
                'color' => 'blue'
            ],
            [
                'title' => 'विवाह बंधन हेतु पंजीयन',
                'description' => 'वैवाहिक सेवा के लिए पंजीकरण',
                'route' => 'sadasya.vaivahik-panjiyan.new',
                'icon' => 'heart',
                'color' => 'pink'
            ],
            [
                'title' => 'नौकरी रोजगार स्वरोजगार सहायता',
                'description' => 'रोजगार सहायता के लिए आवेदन',
                'route' => 'anya-seva.naukri-sahayta',
                'icon' => 'briefcase',
                'color' => 'green'
            ],
            [
                'title' => 'बहुराष्ट्रीय कंपनी ट्रैकिंग',
                'description' => 'MNC में कार्यरत सदस्यों का पंजीकरण',
                'route' => 'anya-seva.multinational-company',
                'icon' => 'building',
                'color' => 'purple'
            ],
            [
                'title' => 'यादव व्यापार एवं ग्राहक',
                'description' => 'व्यापारिक सेवाओं का पंजीकरण',
                'route' => 'anya-seva.yadav-vyapar-grahak',
                'icon' => 'store',
                'color' => 'orange'
            ],
            [
                'title' => 'एकादश सदस्यता',
                'description' => 'न्यूनतम ₹11 में सदस्यता',
                'route' => 'ekadash-sadasyata.aavedan',
                'icon' => 'star',
                'color' => 'yellow'
            ],
            [
                'title' => 'दार्शनिक इष्ठल',
                'description' => 'पर्यटन गाइड सेवा पंजीकरण',
                'route' => 'anya-seva.darshanik-ishthal',
                'icon' => 'map',
                'color' => 'indigo'
            ]
        ];
    }
}
