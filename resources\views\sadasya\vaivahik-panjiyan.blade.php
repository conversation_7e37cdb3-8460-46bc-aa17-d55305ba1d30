@extends('layouts.app')

@section('title', 'वैवाहिक पंजीयन')

@section('content')
    <div class="bg-gray-50 py-10">
        <div class="container-custom">
            <!-- Page Header -->
            <div class="text-center mb-10">
                <h1 class="text-3xl md:text-4xl font-bold text-navy-800 mb-4">वैवाहिक पंजीयन</h1>
                <div class="w-24 h-1.5 bg-saffron-500 mx-auto rounded-full mb-6"></div>
                <p class="text-gray-700 max-w-2xl mx-auto">समुदाय के सदस्यों के लिए वैवाहिक पंजीयन सेवा</p>
            </div>

            <!-- Quick Access Section -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0">
                    <!-- New Registration -->
                    <div class="text-center lg:text-left">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">नया पंजीयन</h3>
                        <p class="text-gray-600 mb-4">अपना वैवाहिक पंजीयन करें और खाता बनाएं</p>
                        <a href="{{ route('sadasya.vaivahik-panjiyan.new') }}"
                           class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            नया पंजीयन करें
                        </a>
                    </div>

                    <!-- Login Options -->
                    <div class="text-center">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">लॉगिन करें</h3>
                        <p class="text-gray-600 mb-4">अपने खाते में प्रवेश करें</p>
                        <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                            <a href="{{ route('vaivahik.login') }}"
                               class="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-pink-500 to-rose-600 hover:from-pink-600 hover:to-rose-700 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                                </svg>
                                वैवाहिक लॉगिन
                            </a>
                            <a href="{{ route('member.login') }}"
                               class="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                </svg>
                                सदस्य लॉगिन
                            </a>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">
                            खाता नहीं है?
                            <a href="{{ route('vaivahik.register') }}" class="text-pink-600 hover:text-pink-800 underline">यहाँ पंजीकरण करें</a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Mobile Card View (visible on small screens) -->
            <div class="block lg:hidden space-y-3 px-2">
                @forelse($vaivahikData as $data)
                    <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 mx-1 transform hover:scale-[1.02]">
                        <!-- Enhanced Header -->
                        <div class="bg-gradient-to-br {{ $data->ling == 'पुरुष' ? 'from-blue-600 via-indigo-600 to-purple-700' : 'from-pink-500 via-rose-500 to-red-600' }} px-4 py-3 relative overflow-hidden">
                            <!-- Background Pattern -->
                            <div class="absolute inset-0 opacity-10">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12"></div>
                            </div>
                            <div class="flex items-center space-x-3 relative z-10">
                                <!-- Enhanced Profile Picture -->
                                <div class="flex-shrink-0">
                                    @if ($data->photo1)
                                        <div class="relative">
                                            <img src="{{ asset('storage/' . $data->photo1) }}" alt="{{ $data->naam }}"
                                                class="w-14 h-14 rounded-full border-3 border-white/50 object-cover shadow-lg">
                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                                        </div>
                                    @else
                                        <div class="w-14 h-14 rounded-full bg-white/20 flex items-center justify-center border-3 border-white/50 shadow-lg">
                                            <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    @endif
                                </div>

                                <!-- Enhanced Name and Details -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-base font-bold text-white truncate mb-1">{{ $data->naam }}</h3>
                                    <div class="flex items-center space-x-2 text-xs">
                                        <span class="bg-white/20 px-2 py-1 rounded-full text-white/95 font-medium">{{ $data->ling }}</span>
                                        @if ($data->janmatithi)
                                            <span class="bg-white/20 px-2 py-1 rounded-full text-white/95 font-medium">
                                                {{ \Carbon\Carbon::parse($data->janmatithi)->age }} वर्ष
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Enhanced Action Button -->
                                @if($isLoggedIn)
                                    <a href="{{ route('sadasya.vaivahik-panjiyan.show', $data->id) }}"
                                        class="bg-white/25 hover:bg-white/35 text-white px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                        विवरण
                                    </a>
                                @else
                                    <button onclick="showLoginPrompt()"
                                        class="bg-white/25 hover:bg-white/35 text-white px-3 py-2 rounded-lg text-xs font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                        लॉगिन
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- Enhanced Content Section -->
                        <div class="bg-gradient-to-r from-gray-50 via-blue-50 to-indigo-50 p-3">
                            <!-- Compact Info Grid -->
                            <div class="grid grid-cols-1 gap-2">
                                <!-- Key Information Row -->
                                <div class="flex flex-wrap gap-2 text-xs">
                                    <!-- Education Badge -->
                                    @if ($data->saikshanik_yogyata)
                                        <span class="inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051l.94.43a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z" />
                                            </svg>
                                            {{ Str::limit($data->saikshanik_yogyata, 15) }}
                                        </span>
                                    @endif

                                    <!-- Occupation Badge -->
                                    @if ($data->upjivika)
                                        <span class="inline-flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2z" />
                                            </svg>
                                            {{ Str::limit($data->upjivika, 15) }}
                                        </span>
                                    @elseif ($data->vartaman_karya)
                                        <div class="flex items-center text-[10px]">
                                            <svg class="w-3 h-3 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2z" />
                                            </svg>
                                            <span class="truncate" title="{{ $data->vartaman_karya }}">
                                                {{ Str::limit($data->vartaman_karya, 25) }}
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Address -->
                                    @if ($data->pata)
                                        <div class="flex items-center text-[10px]">
                                            <svg class="w-3 h-3 text-orange-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" />
                                            </svg>
                                            <span class="truncate" title="{{ $data->pata }}">
                                                {{ Str::limit($data->pata, 25) }}
                                            </span>
                                        </div>
                                    @endif

                                    @if($isLoggedIn)
                                        <!-- Additional Details (Only for logged in users) -->
                                        @if ($data->family_member_membership_number)
                                            <div class="flex items-center text-[10px]">
                                                <svg class="w-3 h-3 text-purple-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                <span class="truncate" title="पारिवारिक सदस्य: {{ $data->family_member_membership_number }}">
                                                    {{ $data->family_member_membership_number }}
                                                </span>
                                            </div>
                                        @endif
                                    @else
                                        <!-- Login prompt for more details -->
                                        <div class="text-[10px] text-gray-500 italic">
                                            पूर्ण विवरण के लिए लॉगिन करें
                                        </div>
                                    @endif
                                </div>

                                <!-- Right Column: Interest Button -->
                                <div class="flex justify-end items-center gap-1">
                                    @if($isLoggedIn)
                                        <button class="bg-pink-500 hover:bg-pink-600 text-white p-1 rounded" title="रुचि व्यक्त करें">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" />
                                            </svg>
                                        </button>
                                    @else
                                        <button onclick="showLoginPrompt()" class="bg-gray-400 text-white p-1 rounded" title="लॉगिन करें">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 102 0V4a1 1 0 011-1h10a1 1 0 011 1v12a1 1 0 102 0V4a1 1 0 00-1-1H4a1 1 0 00-1 1z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="bg-white rounded-lg shadow-md border border-gray-100 p-4 text-center mx-1">
                        <div class="text-gray-400 mb-2">
                            <svg class="mx-auto h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <p class="text-gray-500 text-xs">कोई डेटा उपलब्ध नहीं है</p>
                    </div>
                @endforelse
            </div>

            <!-- Desktop Table View (hidden on small screens) -->
            <div class="hidden lg:block bg-white rounded-xl shadow-xl overflow-hidden border border-gray-200">
                <div class="overflow-x-auto table-container">
                    <table class="min-w-full text-base text-left table-enhanced">
                        <thead class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
                            <tr>
                                <th class="px-6 py-4 font-semibold">फोटो</th>
                                <th class="px-6 py-4 font-semibold">नाम</th>
                                <th class="px-6 py-4 font-semibold">लिंग</th>
                                <th class="px-6 py-4 font-semibold">जन्म तिथि</th>
                                <th class="px-6 py-4 font-semibold">शिक्षा</th>
                                <th class="px-6 py-4 font-semibold">उपजीविका</th>
                                <th class="px-6 py-4 font-semibold">पता</th>
                                @if($isLoggedIn)
                                    <th class="px-6 py-4 font-semibold">पारिवारिक सदस्यता</th>
                                @endif
                                <th class="px-6 py-4 font-semibold text-center">कार्य</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            @forelse($vaivahikData as $data)
                                <tr class="bg-white hover:bg-gray-50 transition-colors duration-200">
                                    <!-- Photo -->
                                    <td class="px-6 py-4">
                                        @if ($data->photo1)
                                            <img src="{{ asset('storage/' . $data->photo1) }}" alt="{{ $data->naam }}"
                                                class="h-16 w-16 rounded-full object-cover border-3 border-gray-300 shadow-md hover:shadow-lg transition-shadow">
                                        @else
                                            <div class="h-16 w-16 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center shadow-md">
                                                <svg class="h-8 w-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                        @endif
                                    </td>

                                    <!-- Name -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="h-10 w-10 rounded-full flex items-center justify-center font-bold text-white text-sm {{ $data->ling == 'पुरुष' ? 'bg-blue-500' : 'bg-pink-500' }} shadow-md">
                                                {{ substr($data->naam, 0, 1) }}
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900 text-lg">{{ $data->naam }}</div>
                                                @auth('vaivahik')
                                                    @if($data->pita_ka_naam)
                                                        <div class="text-sm text-gray-500">पिता: {{ Str::limit($data->pita_ka_naam, 20) }}</div>
                                                    @endif
                                                @endauth
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Gender -->
                                    <td class="px-6 py-4">
                                        <span class="inline-flex items-center px-3 py-1 text-sm font-medium rounded-full {{ $data->ling == 'पुरुष' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800' }}">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                @if($data->ling == 'पुरुष')
                                                    <path d="M10 2L3 9h4v9h6V9h4l-7-7z"/>
                                                @else
                                                    <path d="M10 2a4 4 0 100 8 4 4 0 000-8zM8 14a6 6 0 00-6 6h12a6 6 0 00-6-6H8z"/>
                                                @endif
                                            </svg>
                                            {{ $data->ling }}
                                        </span>
                                    </td>

                                    <!-- Birth Date -->
                                    <td class="px-6 py-4">
                                        @if($data->janmatithi)
                                            <div class="text-gray-900 font-medium">{{ $data->birth_date_string }}</div>
                                            @if($data->age)
                                                <div class="text-sm text-gray-500">{{ $data->age }} वर्ष</div>
                                            @endif
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>

                                    <!-- Education -->
                                    <td class="px-6 py-4">
                                        @if($data->saikshanik_yogyata)
                                            <div class="text-gray-900 font-medium">{{ Str::limit($data->saikshanik_yogyata, 25) }}</div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>

                                    <!-- Upjivika (Occupation) -->
                                    <td class="px-6 py-4">
                                        @if($data->upjivika)
                                            <div class="text-gray-900 font-medium">{{ Str::limit($data->upjivika, 25) }}</div>
                                        @elseif($data->vartaman_karya)
                                            <div class="text-gray-900 font-medium">{{ Str::limit($data->vartaman_karya, 25) }}</div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>

                                    <!-- Location -->
                                    <td class="px-6 py-4">
                                        @if($data->pata)
                                            <div class="text-gray-900 font-medium">{{ Str::limit($data->pata, 30) }}</div>
                                        @else
                                            <span class="text-gray-400">-</span>
                                        @endif
                                    </td>

                                    @if($isLoggedIn)
                                        <!-- Family Membership Number (Only for logged in users) -->
                                        <td class="px-6 py-4">
                                            @if($data->family_member_membership_number)
                                                <div class="text-gray-900 font-medium">{{ $data->family_member_membership_number }}</div>
                                                <div class="text-sm text-gray-500">पारिवारिक सदस्य</div>
                                            @else
                                                <span class="text-gray-400">-</span>
                                            @endif
                                        </td>
                                    @endif

                                    <!-- Actions -->
                                    <td class="px-6 py-4">
                                        <div class="flex items-center justify-center space-x-2">
                                            <a href="{{ route('sadasya.vaivahik-panjiyan.show', $data->id) }}"
                                               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                                </svg>
                                                @if($isLoggedIn) पूर्ण विवरण देखें @else सदस्यता लॉगिन करें @endif
                                            </a>
                                            <button class="inline-flex items-center px-3 py-2 bg-pink-500 hover:bg-pink-600 text-white text-sm font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="px-6 py-16 text-center">
                                        <div class="flex flex-col items-center space-y-4">
                                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <p class="text-lg font-medium text-gray-900">कोई प्रोफाइल उपलब्ध नहीं है</p>
                                                <p class="text-sm text-gray-500 mt-1">नए पंजीयन की प्रतीक्षा में</p>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            @if($vaivahikData->hasPages())
                <div class="mt-8 flex justify-center">
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 px-6 py-4">
                        {{ $vaivahikData->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    <style>
        .btn-primary {
            @apply inline-flex items-center py-2.5 px-6 bg-saffron-500 text-white rounded-md font-medium hover:bg-saffron-600 transition-colors;
        }

        .form-input {
            @apply border border-gray-300 focus:border-saffron-500 focus:ring focus:ring-saffron-200 focus:ring-opacity-50 rounded-md shadow-sm px-4 py-2;
        }

        /* Enhanced table styling */
        .table-enhanced {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-enhanced thead th {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table-enhanced tbody tr:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .table-enhanced tbody tr {
            transition: all 0.2s ease;
        }

        /* Custom scrollbar for table */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>

    <script>
        function showLoginPrompt() {
            if (confirm('पूर्ण विवरण देखने के लिए कृपया सदस्यता लॉगिन करें। क्या आप लॉगिन पेज पर जाना चाहते हैं?')) {
                // Create a form to submit with the intended URL
                const form = document.createElement('form');
                form.method = 'GET';
                form.action = '{{ route("member.login") }}';

                const intendedInput = document.createElement('input');
                intendedInput.type = 'hidden';
                intendedInput.name = 'intended';
                intendedInput.value = window.location.href;

                form.appendChild(intendedInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
