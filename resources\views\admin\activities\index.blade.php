@extends('layouts.admin') @section('title', 'Activities') @section('breadcrumbs') <div class="text-sm breadcrumbs text-gray-500"> <ul> <li><a href="{{ route('admin.dashboard') }}">Dashboard</a></li> <li>Activities</li> </ul> </div> @endsection @section('header') <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6"> <h1 class="text-2xl font-bold">Activities</h1> <div class="flex flex-col md:flex-row gap-2"> <a href="{{ route('admin.activities.create') }}" class="btn btn-primary"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /> </svg> Add New Activity </a> </div> </div> @endsection @section('content') <!-- Category Filter --> <div class="bg-white shadow rounded-lg p-4 mb-6"> <div class="flex flex-col md:flex-row md:items-center gap-4"> <h3 class="font-medium">Filter by Category:</h3> <div class="flex flex-wrap gap-2"> @foreach($categoryOptions as $key => $label) <a href="{{ route('admin.activities.index', ['category' => $key]) }}" class="px-3 py-1 rounded-full text-sm {{ $category == $key ? 'bg-navy-600 text-white' : 'bg-gray-100 text-gray-700' }}"> {{ $label }} </a> @endforeach </div> </div> </div> @if(session('success')) <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6"> <p>{{ session('success') }}</p> </div> @endif <div class="bg-white shadow rounded-lg overflow-hidden"> @if(count($activities) > 0) <table class="min-w-full divide-y divide-gray-200"> <thead class="bg-gray-50"> <tr> <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th> <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th> <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th> <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th> <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th> <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th> </tr> </thead> <tbody class="bg-white divide-y divide-gray-200"> @foreach($activities as $activity) <tr> <td class="px-6 py-4 whitespace-nowrap"> <div class="flex items-center"> @if($activity->image_path) <div class="flex-shrink-0 h-10 w-10"> <img class="h-10 w-10 rounded-full object-cover" src="{{ asset($activity->image_path) }}" alt="{{ $activity->title }}"> </div> @endif <div class="ml-4"> <div class="text-sm font-medium text-gray-900">{{ $activity->title }}</div> </div> </div> </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900"> {{ $activity->event_date ? $activity->event_date->format('M d, Y') : 'N/A' }} </div> </td> <td class="px-6 py-4 whitespace-nowrap"> <div class="text-sm text-gray-900">{{ $activity->location ?? 'N/A' }}</div> </td> <td class="px-6 py-4 whitespace-nowrap"> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $activity->is_published ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}"> {{ $activity->is_published ? 'Published' : 'Draft' }} </span> </td> <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"> {{ $activity->display_order }} </td> <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"> <div class="flex justify-end space-x-2"> <a href="{{ route('admin.activities.edit', $activity->id) }}" class="text-indigo-600 hover:text-indigo-900:text-indigo-300">Edit</a> <form action="{{ route('admin.activities.destroy', $activity->id) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this activity?');"> @csrf @method('DELETE') <button type="submit" class="text-red-600 hover:text-red-900:text-red-300">Delete</button> </form> </div> </td> </tr> @endforeach </tbody> </table> @else <div class="text-center py-12"> <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" /> </svg> <h3 class="mt-2 text-sm font-medium text-gray-900">No activities found</h3> <p class="mt-1 text-sm text-gray-500">Get started by creating a new activity.</p> <div class="mt-6"> <a href="{{ route('admin.activities.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-navy-600 hover:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500:bg-navy-800"> <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /> </svg> New Activity </a> </div> </div> @endif </div> @endsection 