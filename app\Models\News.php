<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class News extends Model
{
    use HasFactory;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'news';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'category',
        'title',
        'content',
        'image_path',
        'publication_date',
        'source',
        'display_order',
        'is_published',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'publication_date' => 'date',
        'is_published' => 'boolean',
    ];
    
    /**
     * Constants for news categories to avoid magic strings
     */
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_MEETINGS = 'meetings';
    const CATEGORY_OBITUARIES = 'obituaries';
    const CATEGORY_APPOINTMENTS = 'appointments';
    const CATEGORY_PRESS = 'press';
    const CATEGORY_MEDIA = 'media';
    
    /**
     * Get all news by category, ordered by display_order and publication_date
     */
    public static function getByCategory($category, $year = null)
    {
        $query = self::where('category', $category)
            ->where('is_published', true);
            
        if ($year) {
            $query->whereYear('publication_date', $year);
        }
            
        return $query->orderBy('display_order')
            ->orderBy('publication_date', 'desc')
            ->paginate(10);
    }
    
    /**
     * Get available category options for dropdown
     */
    public static function getCategoryOptions()
    {
        return [
            self::CATEGORY_GENERAL => 'General News',
            self::CATEGORY_MEETINGS => 'Meetings',
            self::CATEGORY_OBITUARIES => 'Obituaries',
            self::CATEGORY_APPOINTMENTS => 'Appointments',
            self::CATEGORY_PRESS => 'Press Releases',
            self::CATEGORY_MEDIA => 'Media Coverage',
        ];
    }
    
    /**
     * Get the images associated with this news item.
     */
    public function images(): HasMany
    {
        return $this->hasMany(NewsImage::class)->ordered();
    }

    /**
     * Get the featured image for this news item.
     */
    public function featuredImage()
    {
        return $this->images()->featured()->first() ?? $this->images()->first();
    }

    /**
     * Get the image path with fallback to category-specific placeholder
     * This method maintains backward compatibility with the old single image system
     */
    public function getImageUrl()
    {
        // First check if there are new multiple images
        $featuredImage = $this->featuredImage();
        if ($featuredImage) {
            return $featuredImage->getImageUrl();
        }

        // Fall back to the old single image field for backward compatibility
        if ($this->image_path) {
            // If the path already starts with /storage, use it directly with url()
            if (str_starts_with($this->image_path, '/storage/')) {
                return url($this->image_path);
            }
            return asset($this->image_path);
        }

        // Return category-specific placeholder
        return asset('images/placeholders/news-' . $this->category . '.jpg');
    }

    /**
     * Get all image URLs for this news item.
     */
    public function getAllImageUrls()
    {
        $imageUrls = $this->images->map(function ($image) {
            return $image->getImageUrl();
        })->toArray();

        // Include the old single image if it exists and no new images are present
        if (empty($imageUrls) && $this->image_path) {
            // If the path already starts with /storage, use it directly with url()
            if (str_starts_with($this->image_path, '/storage/')) {
                $imageUrls[] = url($this->image_path);
            } else {
                $imageUrls[] = asset($this->image_path);
            }
        }

        return $imageUrls;
    }

    /**
     * Check if this news item has multiple images.
     */
    public function hasMultipleImages()
    {
        return $this->images()->count() > 1 ||
               ($this->images()->count() === 1 && $this->image_path);
    }

    /**
     * Get the display name for the category.
     */
    public function getCategoryDisplayName()
    {
        $categoryOptions = self::getCategoryOptions();
        return $categoryOptions[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Get the color class for the category.
     */
    public function getCategoryColorClass()
    {
        $colors = [
            self::CATEGORY_GENERAL => 'blue',
            self::CATEGORY_MEETINGS => 'green',
            self::CATEGORY_OBITUARIES => 'gray',
            self::CATEGORY_APPOINTMENTS => 'purple',
            self::CATEGORY_PRESS => 'red',
            self::CATEGORY_MEDIA => 'yellow',
        ];

        return $colors[$this->category] ?? 'blue';
    }

    /**
     * Get image metadata for display purposes.
     */
    public function getImageMetadata($imageUrl)
    {
        try {
            // For external URLs, we can't get file size easily
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                return [
                    'size' => 'External Image',
                    'dimensions' => 'Click to view full size'
                ];
            }

            // For local images
            $imagePath = public_path(str_replace(asset(''), '', $imageUrl));
            if (file_exists($imagePath)) {
                $size = filesize($imagePath);
                $sizeFormatted = $this->formatBytes($size);

                $imageInfo = getimagesize($imagePath);
                $dimensions = $imageInfo ? $imageInfo[0] . ' × ' . $imageInfo[1] . ' px' : 'Unknown';

                return [
                    'size' => $sizeFormatted,
                    'dimensions' => $dimensions
                ];
            }
        } catch (\Exception $e) {
            // Fallback for any errors
        }

        return [
            'size' => 'Unknown',
            'dimensions' => 'Click to view full size'
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get featured news for all categories for the index page
     */
    public static function getFeaturedNews()
    {
        $featured = [];
        
        foreach ([
            self::CATEGORY_GENERAL,
            self::CATEGORY_MEETINGS, 
            self::CATEGORY_OBITUARIES,
            self::CATEGORY_APPOINTMENTS,
            self::CATEGORY_PRESS,
            self::CATEGORY_MEDIA
        ] as $category) {
            $featured[$category] = self::where('category', $category)
                ->where('is_published', true)
                ->orderBy('display_order')
                ->orderBy('publication_date', 'desc')
                ->first();
        }
        
        return $featured;
    }
} 