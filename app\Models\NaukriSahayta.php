<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class NaukriSahayta extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'naukri_sahayta';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'naam',
        'pita_ka_naam',
        'pata',
        'umra',
        'ahartayen',
        'ruchi',
        'mobile_number',
        'biodata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'umra' => 'integer',
    ];

    /**
     * Get biodata file URL if exists
     */
    public function getBiodataUrlAttribute()
    {
        if ($this->biodata && Storage::exists($this->biodata)) {
            return Storage::url($this->biodata);
        }
        return null;
    }

    /**
     * Check if biodata file exists
     */
    public function biodataExists()
    {
        return $this->biodata && Storage::exists($this->biodata);
    }

    /**
     * Delete biodata file from storage
     */
    public function deleteBiodataFile()
    {
        if ($this->biodataExists()) {
            Storage::delete($this->biodata);
        }
    }

    /**
     * Get biodata file name
     */
    public function getBiodataFileNameAttribute()
    {
        if ($this->biodata) {
            return basename($this->biodata);
        }
        return null;
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Delete biodata file when model is deleted
        static::deleting(function ($naukriSahayta) {
            $naukriSahayta->deleteBiodataFile();
        });
    }
}
