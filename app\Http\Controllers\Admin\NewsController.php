<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\NewsImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class NewsController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('admin.auth');
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $category = $request->query('category', News::CATEGORY_GENERAL);
        $news = News::where('category', $category)
                 ->orderBy('display_order')
                 ->orderBy('publication_date', 'desc')
                 ->get();
        
        return view('admin.news.index', [
            'news' => $news,
            'category' => $category,
            'categoryOptions' => News::getCategoryOptions()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.news.create', [
            'categoryOptions' => News::getCategoryOptions()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'category' => 'required|string',
                'title' => 'required|string|max:255',
                'content' => 'required',
                'publication_date' => 'nullable|date',
                'source' => 'nullable|string|max:255',
                'image' => 'nullable|image|max:2048', // max 2MB (backward compatibility)
                'images' => 'nullable|array|max:10', // max 10 images
                'images.*' => 'image|max:2048', // max 2MB per image
                'image_captions' => 'nullable|array',
                'image_captions.*' => 'nullable|string|max:255',
                'image_alt_texts' => 'nullable|array',
                'image_alt_texts.*' => 'nullable|string|max:255',
                'featured_image_index' => 'nullable|integer',
                'display_order' => 'nullable|integer',
            ]);

            DB::beginTransaction();

            $data = $request->except(['image', 'images', 'image_captions', 'image_alt_texts', 'featured_image_index']);

            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;

            // Handle backward compatibility - single image upload
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                $path = $request->file('image')->store('public/news');
                $data['image_path'] = Storage::url($path);
            }

            $news = News::create($data);

            // Handle multiple image uploads
            if ($request->hasFile('images')) {
                $this->handleMultipleImageUploads($request, $news);
            }

            DB::commit();

            return redirect()
                ->route('admin.news.index', ['category' => $request->category])
                ->with('success', 'News item created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create news item: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $news = News::findOrFail($id);
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $news = News::findOrFail($id);
        
        return view('admin.news.edit', [
            'news' => $news,
            'categoryOptions' => News::getCategoryOptions()
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $news = News::findOrFail($id);

            $validated = $request->validate([
                'category' => 'required|string',
                'title' => 'required|string|max:255',
                'content' => 'required',
                'publication_date' => 'nullable|date',
                'source' => 'nullable|string|max:255',
                'image' => 'nullable|image|max:2048', // max 2MB (backward compatibility)
                'images' => 'nullable|array|max:10', // max 10 images
                'images.*' => 'image|max:2048', // max 2MB per image
                'image_captions' => 'nullable|array',
                'image_captions.*' => 'nullable|string|max:255',
                'image_alt_texts' => 'nullable|array',
                'image_alt_texts.*' => 'nullable|string|max:255',
                'featured_image_index' => 'nullable|integer',
                'remove_images' => 'nullable|array',
                'remove_images.*' => 'integer',
                'display_order' => 'nullable|integer',
            ]);

            DB::beginTransaction();

            $data = $request->except(['image', 'images', 'image_captions', 'image_alt_texts', 'featured_image_index', 'remove_images']);

            // Set boolean fields explicitly
            $data['is_published'] = $request->has('is_published') ? 1 : 0;

            // Handle backward compatibility - single image upload
            if ($request->hasFile('image') && $request->file('image')->isValid()) {
                // Delete old image if exists
                if ($news->image_path && Storage::exists(str_replace('/storage', 'public', $news->image_path))) {
                    Storage::delete(str_replace('/storage', 'public', $news->image_path));
                }

                $path = $request->file('image')->store('public/news');
                $data['image_path'] = Storage::url($path);
            }

            $news->update($data);

            // Handle image removal
            if ($request->has('remove_images')) {
                $this->removeImages($request->remove_images);
            }

            // Handle multiple image uploads
            if ($request->hasFile('images')) {
                $this->handleMultipleImageUploads($request, $news);
            }

            DB::commit();

            return redirect()
                ->route('admin.news.index', ['category' => $news->category])
                ->with('success', 'News item updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update news item: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $news = News::findOrFail($id);
        $category = $news->category;

        // Delete associated single image if exists
        if ($news->image_path && Storage::exists(str_replace('/storage', 'public', $news->image_path))) {
            Storage::delete(str_replace('/storage', 'public', $news->image_path));
        }

        // Delete all associated multiple images
        foreach ($news->images as $image) {
            if (Storage::exists(str_replace('/storage', 'public', $image->image_path))) {
                Storage::delete(str_replace('/storage', 'public', $image->image_path));
            }
        }

        $news->delete();

        return redirect()
            ->route('admin.news.index', ['category' => $category])
            ->with('success', 'News item deleted successfully');
    }

    /**
     * Handle multiple image uploads for a news item.
     */
    private function handleMultipleImageUploads(Request $request, News $news)
    {
        $images = $request->file('images');
        $captions = $request->input('image_captions', []);
        $altTexts = $request->input('image_alt_texts', []);
        $featuredIndex = $request->input('featured_image_index');

        foreach ($images as $index => $image) {
            if ($image && $image->isValid()) {
                $path = $image->store('public/news');
                $imageUrl = Storage::url($path);

                NewsImage::create([
                    'news_id' => $news->id,
                    'image_path' => $imageUrl,
                    'caption' => $captions[$index] ?? null,
                    'alt_text' => $altTexts[$index] ?? null,
                    'sort_order' => $index,
                    'is_featured' => $featuredIndex == $index,
                ]);
            }
        }
    }

    /**
     * Remove specified images.
     */
    private function removeImages(array $imageIds)
    {
        $images = NewsImage::whereIn('id', $imageIds)->get();

        foreach ($images as $image) {
            if (Storage::exists(str_replace('/storage', 'public', $image->image_path))) {
                Storage::delete(str_replace('/storage', 'public', $image->image_path));
            }
            $image->delete();
        }
    }
}