@extends('layouts.admin') @section('title', 'नया दस्तावेज अपलोड करें') @section('content') <div class="p-6"> <!-- Page Header --> <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"> <div> <h1 class="text-2xl font-bold admin-text-primary">नया दस्तावेज अपलोड करें</h1> <p class="admin-text-secondary mt-1">संगठन के लिए नया दस्तावेज अपलोड करें</p> </div> <a href="{{ route('admin.organization-documents.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-arrow-left"></i> वापस जाएं </a> </div> <!-- Error Messages --> @if($errors->any()) <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6 relative" role="alert"> <div class="font-bold">त्रुटि!</div> <div class="mt-2">कृपया निम्नलिखित समस्याओं को ठीक करें:</div> <ul class="mt-2 ml-4 list-disc"> @foreach($errors->all() as $error) <li>{{ $error }}</li> @endforeach </ul> </div> @endif <!-- Document Upload Form --> <div class="grid grid-cols-1 lg:grid-cols-3 gap-6"> <div class="lg:col-span-2"> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold admin-text-primary">दस्तावेज की जानकारी</h3> </div> <div class="p-6"> <form action="{{ route('admin.organization-documents.store') }}" method="POST" enctype="multipart/form-data"> @csrf <!-- Title --> <div class="mb-6"> <label for="title" class="block text-sm font-medium admin-text-primary mb-2"> शीर्षक <span class="text-red-500">*</span> </label> <input type="text" class="admin-form-input w-full @error('title') border-red-500 @enderror" id="title" name="title" value="{{ old('title') }}" required> @error('title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Description --> <div class="mb-6"> <label for="description" class="block text-sm font-medium admin-text-primary mb-2">विवरण</label> <textarea class="admin-form-input w-full @error('description') border-red-500 @enderror" id="description" name="description" rows="4" placeholder="दस्तावेज के बारे में संक्षिप्त विवरण...">{{ old('description') }}</textarea> @error('description') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- File Upload --> <div class="mb-6"> <label for="file" class="block text-sm font-medium admin-text-primary mb-2"> फ़ाइल अपलोड करें <span class="text-red-500">*</span> </label> <input type="file" class="admin-form-input w-full @error('file') border-red-500 @enderror" id="file" name="file" required accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png"> <div class="mt-2 text-sm admin-text-secondary"> समर्थित फ़ाइल प्रकार: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, JPEG, PNG<br> अधिकतम फ़ाइल आकार: 10MB </div> @error('file') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Display Order --> <div class="mb-6"> <label for="display_order" class="block text-sm font-medium admin-text-primary mb-2">प्रदर्शन क्रम</label> <input type="number" class="admin-form-input w-full @error('display_order') border-red-500 @enderror" id="display_order" name="display_order" value="{{ old('display_order', 0) }}" min="0"> <div class="mt-2 text-sm admin-text-secondary">कम संख्या वाले दस्तावेज पहले दिखाए जाएंगे</div> @error('display_order') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror </div> <!-- Published Status --> <div class="mb-6"> <div class="flex items-center"> <input type="checkbox" class="h-4 w-4 text-saffron-600 focus:ring-saffron-500 border-gray-300 rounded" id="is_published" name="is_published" value="1" {{ old('is_published', true) ? 'checked' : '' }}> <label for="is_published" class="ml-2 block text-sm admin-text-primary"> प्रकाशित करें </label> </div> <div class="mt-2 text-sm admin-text-secondary">अगर चेक किया गया है तो दस्तावेज वेबसाइट पर दिखाई देगा</div> </div> <!-- Submit Buttons --> <div class="flex gap-3"> <button type="submit" class="bg-saffron-500 hover:bg-saffron-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"> <i class="fas fa-upload"></i> अपलोड करें </button> <a href="{{ route('admin.organization-documents.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"> रद्द करें </a> </div> </form> </div> </div> </div> <!-- Help Section --> <div class="lg:col-span-1"> <div class="admin-card"> <div class="px-6 py-4 border-b border-gray-200"> <h3 class="text-lg font-semibold text-blue-600">सहायता</h3> </div> <div class="p-6"> <h4 class="text-blue-600 font-semibold mb-4">दस्तावेज अपलोड करने के लिए निर्देश:</h4> <ul class="space-y-3"> <li class="flex items-start"> <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i> <div> <strong class="admin-text-primary">शीर्षक:</strong> <span class="admin-text-secondary"> दस्तावेज का स्पष्ट और संक्षिप्त नाम दें</span> </div> </li> <li class="flex items-start"> <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i> <div> <strong class="admin-text-primary">विवरण:</strong> <span class="admin-text-secondary"> दस्तावेज के बारे में संक्षिप्त जानकारी दें</span> </div> </li> <li class="flex items-start"> <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i> <div> <strong class="admin-text-primary">फ़ाइल:</strong> <span class="admin-text-secondary"> केवल समर्थित फ़ाइल प्रकार अपलोड करें</span> </div> </li> <li class="flex items-start"> <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i> <div> <strong class="admin-text-primary">क्रम:</strong> <span class="admin-text-secondary"> दस्तावेजों को व्यवस्थित करने के लिए क्रम संख्या दें</span> </div> </li> </ul> <div class="border-t border-gray-200 my-6"></div> <h4 class="text-blue-600 font-semibold mb-4">समर्थित फ़ाइल प्रकार:</h4> <div class="grid grid-cols-2 gap-4"> <div> <div class="text-sm admin-text-secondary space-y-1"> <div>• PDF</div> <div>• DOC/DOCX</div> <div>• XLS/XLSX</div> <div>• PPT/PPTX</div> </div> </div> <div> <div class="text-sm admin-text-secondary space-y-1"> <div>• TXT</div> <div>• JPG/JPEG</div> <div>• PNG</div> </div> </div> </div> </div> </div> </div> </div> </div> @endsection @push('scripts') <script> // File upload preview and validation document.getElementById('file').addEventListener('change', function(e) { const file = e.target.files[0]; if (file) { // Check file size (10MB = 10 * 1024 * 1024 bytes) if (file.size > 10 * 1024 * 1024) { alert('फ़ाइल का आकार 10MB से अधिक नहीं होना चाहिए।'); this.value = ''; return; } // Auto-fill title if empty const titleInput = document.getElementById('title'); if (!titleInput.value) { const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension titleInput.value = fileName; } } }); // Form validation document.querySelector('form').addEventListener('submit', function(e) { const fileInput = document.getElementById('file'); const titleInput = document.getElementById('title'); if (!fileInput.files.length) { e.preventDefault(); alert('कृपया एक फ़ाइल चुनें।'); return; } if (!titleInput.value.trim()) { e.preventDefault(); alert('कृपया शीर्षक दर्ज करें।'); titleInput.focus(); return; } }); </script> @endpush 