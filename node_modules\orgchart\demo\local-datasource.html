<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Organization Chart Plugin</title>
  <link rel="icon" href="img/logo.png">
  <link rel="stylesheet" href="css/jquery.orgchart.css">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <div id="chart-container"></div>

  <script type="text/javascript" src="js/jquery.min.js"></script>
  <script type="text/javascript" src="js/jquery.orgchart.js"></script>
  <script type="text/javascript">
    $(function() {

    var datasource = {
      'name': 'Lao Lao',
      'title': 'general manager',
      'children': [
        { 'name': '<PERSON> Miao', 'title': 'department manager' },
        { 'name': '<PERSON> Miao', 'title': 'department manager',
          'children': [
            { 'name': '<PERSON><PERSON>', 'title': 'senior engineer' },
            { 'name': 'Hei Hei', 'title': 'senior engineer',
              'children': [
                { 'name': '<PERSON>', 'title': 'engineer' }
              ]
            },
            { 'name': 'Pang Pang', 'title': 'senior engineer' }
          ]
        },
        { 'name': 'Hong <PERSON>o', 'title': 'department manager' }
      ]
    };

    $('#chart-container').orgchart({
      'data' : datasource,
      'nodeContent': 'title'
    });

  });
  </script>
  </body>
</html>